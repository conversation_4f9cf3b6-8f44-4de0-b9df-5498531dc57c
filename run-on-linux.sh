#!/bin/bash

# 美国地址生成器 - Linux直接运行脚本
# 无需Docker，直接在Linux上运行Next.js应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=========================================="
    echo "  美国地址生成器 - Linux运行工具"
    echo "  Next.js 15 + React 19 + Supabase"
    echo "=========================================="
    echo -e "${NC}"
    echo
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    log_info "检测到操作系统: $OS $VER"
}

# 检查并安装Node.js
install_nodejs() {
    log_step "检查Node.js环境..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js已安装: $NODE_VERSION"
        
        # 检查版本是否满足要求
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR" -lt 18 ]; then
            log_warning "Node.js版本过低，需要18.0+，正在更新..."
            install_node_fresh
        else
            log_success "Node.js版本满足要求"
        fi
    else
        log_info "Node.js未安装，正在安装..."
        install_node_fresh
    fi
}

# 安装最新Node.js
install_node_fresh() {
    case "$OS" in
        *"Ubuntu"*|*"Debian"*)
            curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
            sudo apt-get install -y nodejs
            ;;
        *"CentOS"*|*"Red Hat"*|*"Rocky"*|*"AlmaLinux"*)
            curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
            sudo yum install -y nodejs
            ;;
        *"Alpine"*)
            sudo apk add --no-cache nodejs npm
            ;;
        *)
            log_error "不支持的操作系统，请手动安装Node.js 20.x"
            log_info "访问: https://nodejs.org/en/download/"
            exit 1
            ;;
    esac
    
    log_success "Node.js安装完成: $(node --version)"
}

# 检查项目文件
check_project_files() {
    log_step "检查项目文件..."
    
    if [ ! -f "package.json" ]; then
        log_error "package.json文件不存在，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    log_success "项目文件检查通过"
}

# 配置环境变量
setup_environment() {
    log_step "配置环境变量..."
    
    if [ ! -f ".env.local" ]; then
        if [ -f ".env.local.example" ]; then
            cp .env.local.example .env.local
            log_success "已创建.env.local文件"
            
            echo
            log_warning "请编辑.env.local文件配置以下必需变量:"
            echo "  - NEXT_PUBLIC_SUPABASE_URL=your_supabase_url"
            echo "  - NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key"
            echo "  - ADMIN_USERNAME=your_admin_username"
            echo "  - ADMIN_PASSWORD=your_secure_password"
            echo "  - JWT_SECRET=your_jwt_secret_32_chars_min"
            echo
            
            read -p "配置完成后按回车继续，或输入'edit'现在编辑: " user_input
            if [ "$user_input" = "edit" ]; then
                ${EDITOR:-nano} .env.local
            fi
        else
            log_error ".env.local.example文件不存在"
            exit 1
        fi
    else
        log_info ".env.local文件已存在"
    fi
}

# 安装依赖
install_dependencies() {
    log_step "安装项目依赖..."
    
    # 检查包管理器
    if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
        log_info "使用pnpm安装依赖..."
        pnpm install
    elif [ -f "pnpm-lock.yaml" ]; then
        log_info "安装pnpm..."
        npm install -g pnpm
        log_info "使用pnpm安装依赖..."
        pnpm install
    else
        log_info "使用npm安装依赖..."
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 构建应用
build_application() {
    log_step "构建应用..."

    # 尝试构建
    local build_success=false

    if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
        if pnpm run build; then
            build_success=true
        fi
    else
        if npm run build; then
            build_success=true
        fi
    fi

    # 如果构建失败，尝试修复
    if [ "$build_success" = false ]; then
        log_warning "构建失败，尝试自动修复..."

        # 清理缓存
        log_info "清理构建缓存..."
        rm -rf .next
        rm -rf node_modules/.cache

        # 重新安装依赖
        log_info "重新安装依赖..."
        if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
            pnpm install --force
        else
            rm -rf node_modules package-lock.json
            npm install
        fi

        # 再次尝试构建
        log_info "重新尝试构建..."
        if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
            if pnpm run build; then
                build_success=true
            fi
        else
            if npm run build; then
                build_success=true
            fi
        fi

        if [ "$build_success" = false ]; then
            log_error "构建仍然失败，请检查代码语法错误"
            log_info "常见问题："
            echo "  1. 检查TypeScript语法错误"
            echo "  2. 检查未闭合的注释块"
            echo "  3. 检查缺少的依赖包"
            echo "  4. 运行: npx tsc --noEmit 检查类型错误"
            exit 1
        fi
    fi

    log_success "应用构建完成"
}

# 检查端口占用
check_port() {
    local port=3002
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log_warning "端口$port已被占用"
        log_info "正在查找占用进程..."
        
        if command -v lsof &> /dev/null; then
            lsof -ti:$port | head -1 | xargs -r ps -p
        elif command -v netstat &> /dev/null; then
            netstat -tulpn | grep ":$port "
        fi
        
        read -p "是否终止占用进程并继续? (y/n): " kill_process
        if [[ $kill_process =~ ^[Yy]$ ]]; then
            if command -v lsof &> /dev/null; then
                lsof -ti:$port | xargs -r kill -9
                log_success "已终止占用进程"
            else
                log_warning "请手动终止占用端口$port的进程"
                exit 1
            fi
        else
            log_info "请手动终止占用进程后重新运行"
            exit 1
        fi
    fi
}

# 启动应用
start_application() {
    log_step "启动应用..."
    
    check_port
    
    log_info "应用启动中..."
    echo
    log_success "🎉 应用启动成功!"
    echo
    echo -e "${CYAN}📱 访问信息:${NC}"
    echo "  主页: http://localhost:3002"
    echo "  健康检查: http://localhost:3002/api/health"
    echo
    echo -e "${CYAN}🔧 管理命令:${NC}"
    echo "  停止应用: Ctrl+C"
    echo "  重启应用: 重新运行此脚本"
    echo
    echo -e "${YELLOW}💡 提示: 应用运行在前台，关闭终端会停止应用${NC}"
    echo
    
    # 启动应用
    if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
        pnpm start
    else
        npm start
    fi
}

# 开发模式启动
start_dev_mode() {
    log_step "启动开发模式..."
    
    check_port
    
    log_info "开发服务器启动中..."
    echo
    log_success "🚀 开发服务器启动成功!"
    echo
    echo -e "${CYAN}📱 访问信息:${NC}"
    echo "  主页: http://localhost:3002"
    echo "  开发模式支持热重载"
    echo
    echo -e "${YELLOW}💡 提示: 开发模式运行在前台，关闭终端会停止服务${NC}"
    echo
    
    # 启动开发服务器
    if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
        pnpm dev
    else
        npm run dev
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --help, -h     显示帮助信息"
    echo "  --dev, -d      启动开发模式"
    echo "  --build, -b    仅构建应用"
    echo "  --install, -i  仅安装依赖"
    echo "  --check, -c    仅检查环境"
    echo
    echo "示例:"
    echo "  $0              # 完整安装并启动生产模式"
    echo "  $0 --dev       # 启动开发模式"
    echo "  $0 --build     # 仅构建应用"
    echo
}

# 主函数
main() {
    show_banner
    
    # 解析参数
    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
        --dev|-d)
            DEV_MODE=true
            ;;
        --build|-b)
            BUILD_ONLY=true
            ;;
        --install|-i)
            INSTALL_ONLY=true
            ;;
        --check|-c)
            CHECK_ONLY=true
            ;;
    esac
    
    detect_os
    install_nodejs
    check_project_files
    
    if [ "${CHECK_ONLY:-}" = "true" ]; then
        log_success "环境检查完成"
        exit 0
    fi
    
    setup_environment
    install_dependencies
    
    if [ "${INSTALL_ONLY:-}" = "true" ]; then
        log_success "依赖安装完成"
        exit 0
    fi
    
    if [ "${DEV_MODE:-}" != "true" ]; then
        build_application
    fi
    
    if [ "${BUILD_ONLY:-}" = "true" ]; then
        log_success "应用构建完成"
        exit 0
    fi
    
    if [ "${DEV_MODE:-}" = "true" ]; then
        start_dev_mode
    else
        start_application
    fi
}

# 运行主函数
main "$@"
