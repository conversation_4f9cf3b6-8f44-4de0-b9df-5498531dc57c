"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ear<PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Copy, RefreshCw, Settings, Check, Download, Save, Database, Users, X, FileSpreadsheet, Mail } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import ConfigDialog from "@/components/config-dialog"
import JsonDownloadDialog from "@/components/json-download-dialog"
import BatchResultsDialog from "@/components/batch-results-dialog"
import BatchGenerateControl from "@/components/batch-generate-control"
import { exportToExcel } from "@/lib/excel-export"
import { generateEmail } from "@/lib/email-generator"
import Link from "next/link"

interface PersonData {
  fullName: string
  firstName: string
  lastName: string
  gender: string
  birthday: string
  title: string
  hairColor: string
  country: string
  street: string
  city: string
  state: string
  stateFullName: string
  zipCode: string
  phone: string
  email: string
  fullAddress: string
  occupation: string
  company: string
  companySize: string
  industry: string
  status: string
  salary: string
  ssn: string
  cardType: string
  cardNumber: string
  cvv: number
  expiry: string
  username: string
  password: string
  height: string
  weight: string
  bloodType: string
  os: string
  guid: string
  userAgent: string
  education: string
  website: string
  securityQuestion: string
  securityAnswer: string
}

interface SchoolData {
  name: string
  ncesid: string
  zip: string
  website: string
  address: string
  city: string
  state: string
  telephone: string
  st_grade: string
  end_grade: string
}

interface UniversityData {
  name: string
  ipedsid: string
  zip: string
  website: string
  address: string
  city: string
  state: string
  telephone: string
  type: string
}

// 生成随机数种子
const generateSeed = () => Math.floor(Math.random() * 1000000)

const DEFAULT_VISIBLE_FIELDS = [
  "fullName",
  "firstName",
  "lastName",
  "gender",
  "birthday",
  "title",
  "hairColor",
  "street",
  "city",
  "state",
  "zipCode",
  "phone",
  "email",
  "fullAddress",
  "ssn",
  "cardType",
  "cardNumber",
  "cvv",
  "expiry",
  "schoolName",
  "schoolId",
  "schoolZip",
  "schoolWebsite",
  "schoolAddress",
  "schoolPhone",
  "schoolGrades",
  "universityName",
  "universityId",
  "universityZip",
  "universityWebsite",
  "universityAddress",
  "universityPhone",
  "universityType",
]

const FIELD_CATEGORIES = {
  basic: {
    title: "基本信息",
    fields: ["fullName", "firstName", "lastName", "gender", "birthday", "title", "hairColor"],
  },
  contact: {
    title: "联系信息",
    fields: ["street", "city", "state", "stateFullName", "zipCode", "phone", "email", "fullAddress"],
  },
  work: {
    title: "工作信息",
    fields: ["occupation", "company", "companySize", "industry", "status", "salary"],
  },
  physical: {
    title: "身体信息",
    fields: ["height", "weight", "bloodType"],
  },
  financial: {
    title: "金融信息",
    fields: ["ssn", "cardType", "cardNumber", "cvv", "expiry"],
  },
  account: {
    title: "账户信息",
    fields: ["username", "password", "securityQuestion", "securityAnswer"],
  },
  tech: {
    title: "技术信息",
    fields: ["os", "userAgent", "guid"],
  },
  other: {
    title: "其他信息",
    fields: ["education", "website", "country"],
  },
  school: {
    title: "高中信息",
    fields: [
      "schoolName",
      "schoolId",
      "schoolZip",
      "schoolWebsite",
      "schoolAddress",
      "schoolCity",
      "schoolState",
      "schoolPhone",
      "schoolGrades",
    ],
  },
  university: {
    title: "大学信息",
    fields: [
      "universityName",
      "universityId",
      "universityZip",
      "universityWebsite",
      "universityAddress",
      "universityCity",
      "universityState",
      "universityPhone",
      "universityType",
    ],
  },
}

const FIELD_LABELS: Record<string, string> = {
  fullName: "全名",
  firstName: "名",
  lastName: "姓",
  gender: "性别",
  birthday: "生日",
  title: "称谓",
  hairColor: "发色",
  country: "国家",
  street: "街道",
  city: "城市",
  state: "州",
  stateFullName: "州全名",
  zipCode: "邮编",
  phone: "电话",
  email: "邮箱",
  fullAddress: "完整地址",
  occupation: "职业",
  company: "公司",
  companySize: "公司规模",
  industry: "行业",
  status: "工作状态",
  salary: "薪资",
  ssn: "社会安全号",
  cardType: "信用卡类型",
  cardNumber: "信用卡号",
  cvv: "CVV",
  expiry: "到期日期",
  username: "用户名",
  password: "密码",
  height: "身高",
  weight: "体重",
  bloodType: "血型",
  os: "操作系统",
  guid: "GUID",
  userAgent: "用户代理",
  education: "教育程度",
  website: "个人网站",
  securityQuestion: "安全问题",
  securityAnswer: "安全答案",
  schoolName: "高中名称",
  schoolId: "高中ID",
  schoolZip: "高中邮编",
  schoolWebsite: "高中网站",
  schoolAddress: "高中地址",
  schoolCity: "高中城市",
  schoolState: "高中州",
  schoolPhone: "高中电话",
  schoolGrades: "年级范围",
  universityName: "大学名称",
  universityId: "大学ID",
  universityZip: "大学邮编",
  universityWebsite: "大学网站",
  universityAddress: "大学地址",
  universityCity: "大学城市",
  universityState: "大学州",
  universityPhone: "大学电话",
  universityType: "大学类型",
}

const US_STATES = [
  { code: "AL", name: "Alabama" },
  { code: "AK", name: "Alaska" },
  { code: "AZ", name: "Arizona" },
  { code: "AR", name: "Arkansas" },
  { code: "CA", name: "California" },
  { code: "CO", name: "Colorado" },
  { code: "CT", name: "Connecticut" },
  { code: "DE", name: "Delaware" },
  { code: "FL", name: "Florida" },
  { code: "GA", name: "Georgia" },
  { code: "HI", name: "Hawaii" },
  { code: "ID", name: "Idaho" },
  { code: "IL", name: "Illinois" },
  { code: "IN", name: "Indiana" },
  { code: "IA", name: "Iowa" },
  { code: "KS", name: "Kansas" },
  { code: "KY", name: "Kentucky" },
  { code: "LA", name: "Louisiana" },
  { code: "ME", name: "Maine" },
  { code: "MD", name: "Maryland" },
  { code: "MA", name: "Massachusetts" },
  { code: "MI", name: "Michigan" },
  { code: "MN", name: "Minnesota" },
  { code: "MS", name: "Mississippi" },
  { code: "MO", name: "Missouri" },
  { code: "MT", name: "Montana" },
  { code: "NE", name: "Nebraska" },
  { code: "NV", name: "Nevada" },
  { code: "NH", name: "New Hampshire" },
  { code: "NJ", name: "New Jersey" },
  { code: "NM", name: "New Mexico" },
  { code: "NY", name: "New York" },
  { code: "NC", name: "North Carolina" },
  { code: "ND", name: "North Dakota" },
  { code: "OH", name: "Ohio" },
  { code: "OK", name: "Oklahoma" },
  { code: "OR", name: "Oregon" },
  { code: "PA", name: "Pennsylvania" },
  { code: "RI", name: "Rhode Island" },
  { code: "SC", name: "South Carolina" },
  { code: "SD", name: "South Dakota" },
  { code: "TN", name: "Tennessee" },
  { code: "TX", name: "Texas" },
  { code: "UT", name: "Utah" },
  { code: "VT", name: "Vermont" },
  { code: "VA", name: "Virginia" },
  { code: "WA", name: "Washington" },
  { code: "WV", name: "West Virginia" },
  { code: "WI", name: "Wisconsin" },
  { code: "WY", name: "Wyoming" },
]

export default function HomePage() {
  const [data, setData] = useState<PersonData | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [batchGenerating, setBatchGenerating] = useState(false)
  const [batchData, setBatchData] = useState<Array<{
    personData: PersonData
    schoolData: SchoolData | null
    universityData: UniversityData | null
  }>>([])
  const [showBatchResults, setShowBatchResults] = useState(false)
  const [configOpen, setConfigOpen] = useState(false)
  const [copiedField, setCopiedField] = useState<string | null>(null)
  const [schoolData, setSchoolData] = useState<SchoolData | null>(null)
  const [universityData, setUniversityData] = useState<UniversityData | null>(null)
  const [jsonDialogOpen, setJsonDialogOpen] = useState(false)

  const searchParams = useSearchParams()
  const router = useRouter()

  // 从URL参数获取配置
  const state = searchParams.get("state") || ""
  const city = searchParams.get("city") || ""
  const gender = searchParams.get("gender") || ""
  const minAge = Number.parseInt(searchParams.get("minAge") || "0") || 0
  const maxAge = Number.parseInt(searchParams.get("maxAge") || "0") || 0
  const visibleFields = searchParams.get("fields")?.split(",") || DEFAULT_VISIBLE_FIELDS
  const seed = Number.parseInt(searchParams.get("seed") || "0") || generateSeed()

  // 高中和大学筛选参数
  const highState = searchParams.get("highState") || ""
  const highCity = searchParams.get("highCity") || ""
  const universityState = searchParams.get("universityState") || ""
  const universityCity = searchParams.get("universityCity") || ""

  // 如果没有fields参数，重定向到默认配置
  useEffect(() => {
    if (!searchParams.get("fields")) {
      const params = new URLSearchParams(searchParams.toString())
      params.set("fields", DEFAULT_VISIBLE_FIELDS.join(","))
      // 如果没有seed参数，生成一个新的随机种子
      if (!searchParams.get("seed")) {
        params.set("seed", generateSeed().toString())
      }
      // 设置默认年龄段为18-25岁
      if (!searchParams.get("minAge")) {
        params.set("minAge", "18")
      }
      if (!searchParams.get("maxAge")) {
        params.set("maxAge", "25")
      }
      router.replace(`/?${params.toString()}`)
    }
  }, [searchParams, router])

  const generateAge = (minAge: number, maxAge: number) => {
    if (minAge === 0 && maxAge === 0) return null
    const age = Math.floor(Math.random() * (maxAge - minAge + 1)) + minAge
    const currentYear = new Date().getFullYear()
    const birthYear = currentYear - age
    return birthYear
  }

  const updateUrlParams = (updates: Record<string, string>) => {
    const params = new URLSearchParams(searchParams.toString())
    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        params.set(key, value)
      } else {
        params.delete(key)
      }
    })
    router.replace(`/?${params.toString()}`)
  }

  const fetchData = async (newSeed?: number) => {
    setLoading(true)
    try {
      let attempts = 0
      let result: PersonData | null = null
      let currentSeed = newSeed || seed

      while (attempts < 10) {
        // 最多尝试10次
        const params = new URLSearchParams()
        if (state) params.append("state", state)
        if (city) params.append("city", city)
        params.append("_refresh", currentSeed.toString())

        const response = await fetch(`https://www.usaddrgen.com/api/generate.php?${params.toString()}`)
        const apiData = await response.json()

        if (apiData.success) {
          result = apiData.data

          // 检查性别过滤
          if (gender && result.gender.toLowerCase() !== gender.toLowerCase()) {
            attempts++
            currentSeed = generateSeed()
            continue
          }
          break
        }
        attempts++
      }

      if (result) {
        // 处理年龄范围
        let targetBirthYear = null
        if (minAge > 0 && maxAge > 0) {
          targetBirthYear = generateAge(minAge, maxAge)
        }

        // 如果有年龄范围，更新生日
        if (targetBirthYear) {
          const birthParts = result.birthday.split("/")
          const birthMonth = birthParts[0]
          const birthDay = birthParts[1]
          result.birthday = `${birthMonth}/${birthDay}/${targetBirthYear}`
        }

        // 获取高中信息
        let schoolResult: SchoolData | null = null
        const schoolStateToUse = highState || result.state
        const schoolCityToUse = highCity || result.city

        if (schoolStateToUse) {
          try {
            const schoolParams = new URLSearchParams()
            schoolParams.append("select", "name,ncesid,zip,website,address,city,state,telephone,st_grade,end_grade")

            let whereClause = `state="${schoolStateToUse}" AND end_grade = "12"`
            if (schoolCityToUse) {
              whereClause += ` AND city="${schoolCityToUse.toUpperCase()}"`
            }

            schoolParams.append("where", whereClause)
            schoolParams.append("order_by", `random(${currentSeed})`)
            schoolParams.append("limit", "1")

            const schoolResponse = await fetch(
              `https://public.opendatasoft.com/api/explore/v2.1/catalog/datasets/us-public-schools/records?${schoolParams.toString()}`,
            )
            const schoolApiData = await schoolResponse.json()

            // 如果没有找到结果且指定了城市，尝试去掉城市限制
            if ((!schoolApiData.results || schoolApiData.results.length === 0) && schoolCityToUse) {
              const fallbackParams = new URLSearchParams()
              fallbackParams.append("select", "name,ncesid,zip,website,address,city,state,telephone,st_grade,end_grade")
              fallbackParams.append("where", `state="${schoolStateToUse}" AND end_grade = "12"`)
              fallbackParams.append("order_by", `random(${currentSeed})`)
              fallbackParams.append("limit", "1")

              const fallbackResponse = await fetch(
                `https://public.opendatasoft.com/api/explore/v2.1/catalog/datasets/us-public-schools/records?${fallbackParams.toString()}`,
              )
              const fallbackData = await fallbackResponse.json()

              if (fallbackData.results && fallbackData.results.length > 0) {
                const school = fallbackData.results[0]
                schoolResult = {
                  name: school.name || "",
                  ncesid: school.ncesid || "",
                  zip: school.zip || "",
                  website: school.website || "",
                  address: school.address || "",
                  city: school.city || "",
                  state: school.state || "",
                  telephone: school.telephone || "",
                  st_grade: school.st_grade || "",
                  end_grade: school.end_grade || "",
                }
              }
            } else if (schoolApiData.results && schoolApiData.results.length > 0) {
              const school = schoolApiData.results[0]
              schoolResult = {
                name: school.name || "",
                ncesid: school.ncesid || "",
                zip: school.zip || "",
                website: school.website || "",
                address: school.address || "",
                city: school.city || "",
                state: school.state || "",
                telephone: school.telephone || "",
                st_grade: school.st_grade || "",
                end_grade: school.end_grade || "",
              }
            }
          } catch (error) {
            console.error("Failed to fetch school data:", error)
          }
        }

        // 获取大学信息
        let universityResult: UniversityData | null = null
        const universityStateToUse = universityState || result.state

        if (universityStateToUse) {
          try {
            const universityParams = new URLSearchParams()
            universityParams.append("select", "name,ipedsid,zip,website,address,city,state,telephone,type")

            let whereClause = `state="${universityStateToUse}" AND type="1"`
            if (universityCity) {
              whereClause += ` AND city="${universityCity.toUpperCase()}"`
            }

            universityParams.append("where", whereClause)
            universityParams.append("order_by", `random(${currentSeed})`)
            universityParams.append("limit", "1")

            const universityResponse = await fetch(
              `https://public.opendatasoft.com/api/explore/v2.1/catalog/datasets/us-colleges-and-universities/records?${universityParams.toString()}`,
            )
            const universityApiData = await universityResponse.json()

            if (universityApiData.results && universityApiData.results.length > 0) {
              const university = universityApiData.results[0]
              universityResult = {
                name: university.name || "",
                ipedsid: university.ipedsid || "",
                zip: university.zip || "",
                website: university.website || "",
                address: university.address || "",
                city: university.city || "",
                state: university.state || "",
                telephone: university.telephone || "",
                type: university.type || "",
              }
            }
          } catch (error) {
            console.error("Failed to fetch university data:", error)
          }
        }

        setSchoolData(schoolResult)
        setUniversityData(universityResult)

        // 更新URL参数
        if (newSeed) {
          updateUrlParams({
            seed: currentSeed.toString(),
          })
        }

        // 强制使用真实临时邮箱
        if (result) {
          try {
            const emailGenerator = new (await import('@/lib/email-generator')).EmailGenerator()
            const tempEmail = await emailGenerator.createRealTempMail()
            result.email = tempEmail.email
            result.password = tempEmail.password
            console.log(`成功生成真实邮箱: ${result.email}`)
          } catch (error) {
            console.error('创建真实邮箱失败:', error)
            // 不再使用模拟邮箱，直接显示错误
            setError('生成真实邮箱失败，请检查邮箱服务配置')
            setLoading(false)
            return
          }
        }
        setData(result)
      } else {
        toast({
          title: "生成失败",
          description: "无法生成符合条件的数据，请重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "请求失败",
        description: "网络错误，请检查网络连接",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
      toast({
        title: "复制成功",
        description: `已复制 ${FIELD_LABELS[field]}`,
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  const saveToDatabase = async () => {
    if (!data) {
      toast({
        title: "保存失败",
        description: "没有数据可保存",
        variant: "destructive",
      })
      return
    }

    setSaving(true)
    try {
      const generationParams = {
        state,
        city,
        gender,
        minAge,
        maxAge,
        seed,
        highState,
        highCity,
        universityState,
        universityCity,
        visibleFields
      }

      const response = await fetch('/api/save-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          personData: data,
          schoolData,
          universityData,
          generationParams
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "保存成功",
          description: "数据已保存到数据库",
        })
      } else {
        throw new Error(result.error || '保存失败')
      }
    } catch (error) {
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "保存数据时发生错误",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const batchGenerate = async (count: number = batchCount) => {
    setBatchGenerating(true)
    setBatchData([])

    try {
      const results = []

      for (let i = 0; i < count; i++) {
        const currentSeed = generateSeed()
        let attempts = 0
        let result: PersonData | null = null

        while (attempts < 5) { // 减少单个数据的尝试次数
          const params = new URLSearchParams()
          if (state) params.append("state", state)
          if (city) params.append("city", city)
          params.append("_refresh", (currentSeed + attempts).toString())

          const response = await fetch(`https://www.usaddrgen.com/api/generate.php?${params.toString()}`)
          const apiData = await response.json()

          if (apiData.success) {
            result = apiData.data

            // 检查性别过滤
            if (gender && result.gender.toLowerCase() !== gender.toLowerCase()) {
              attempts++
              continue
            }
            break
          }
          attempts++
        }

        if (result) {
          // 处理年龄范围
          let targetBirthYear = null
          if (minAge > 0 && maxAge > 0) {
            targetBirthYear = generateAge(minAge, maxAge)
          }

          if (targetBirthYear) {
            const birthParts = result.birthday.split("/")
            const birthMonth = birthParts[0]
            const birthDay = birthParts[1]
            result.birthday = `${birthMonth}/${birthDay}/${targetBirthYear}`
          }

          // 强制使用真实临时邮箱
          try {
            const emailGenerator = new (await import('@/lib/email-generator')).EmailGenerator()
            const tempEmail = await emailGenerator.createRealTempMail()
            result.email = tempEmail.email
            result.password = tempEmail.password
            console.log(`批量生成 - 成功生成真实邮箱: ${result.email}`)
          } catch (error) {
            console.error('批量生成 - 创建真实邮箱失败:', error)
            // 不再使用模拟邮箱，跳过此条数据
            console.log(`跳过第 ${i + 1} 条数据，邮箱生成失败`)
            continue
          }

          // 获取学校和大学信息（简化版）
          let schoolResult: SchoolData | null = null
          let universityResult: UniversityData | null = null

          // 获取高中信息
          const schoolStateToUse = highState || result.state
          if (schoolStateToUse) {
            try {
              const schoolParams = new URLSearchParams()
              schoolParams.append("select", "name,ncesid,zip,website,address,city,state,telephone,st_grade,end_grade")
              schoolParams.append("where", `state="${schoolStateToUse}" AND end_grade = "12"`)
              schoolParams.append("order_by", `random(${currentSeed + i})`)
              schoolParams.append("limit", "1")

              const schoolResponse = await fetch(
                `https://public.opendatasoft.com/api/explore/v2.1/catalog/datasets/us-public-schools/records?${schoolParams.toString()}`,
              )
              const schoolApiData = await schoolResponse.json()

              if (schoolApiData.results && schoolApiData.results.length > 0) {
                const school = schoolApiData.results[0]
                schoolResult = {
                  name: school.name || "",
                  ncesid: school.ncesid || "",
                  zip: school.zip || "",
                  website: school.website || "",
                  address: school.address || "",
                  city: school.city || "",
                  state: school.state || "",
                  telephone: school.telephone || "",
                  st_grade: school.st_grade || "",
                  end_grade: school.end_grade || "",
                }
              }
            } catch (error) {
              console.error("Failed to fetch school data:", error)
            }
          }

          // 获取大学信息
          const universityStateToUse = universityState || result.state
          if (universityStateToUse) {
            try {
              const universityParams = new URLSearchParams()
              universityParams.append("select", "name,ipedsid,zip,website,address,city,state,telephone,type")
              universityParams.append("where", `state="${universityStateToUse}" AND type="1"`)
              universityParams.append("order_by", `random(${currentSeed + i})`)
              universityParams.append("limit", "1")

              const universityResponse = await fetch(
                `https://public.opendatasoft.com/api/explore/v2.1/catalog/datasets/us-colleges-and-universities/records?${universityParams.toString()}`,
              )
              const universityApiData = await universityResponse.json()

              if (universityApiData.results && universityApiData.results.length > 0) {
                const university = universityApiData.results[0]
                universityResult = {
                  name: university.name || "",
                  ipedsid: university.ipedsid || "",
                  zip: university.zip || "",
                  website: university.website || "",
                  address: university.address || "",
                  city: university.city || "",
                  state: university.state || "",
                  telephone: university.telephone || "",
                  type: university.type || "",
                }
              }
            } catch (error) {
              console.error("Failed to fetch university data:", error)
            }
          }

          results.push({
            personData: result,
            schoolData: schoolResult,
            universityData: universityResult
          })
        }
      }

      setBatchData(results)
      setShowBatchResults(true)

      toast({
        title: "批量生成完成",
        description: `成功生成 ${results.length} 条数据`,
      })

    } catch (error) {
      toast({
        title: "批量生成失败",
        description: error instanceof Error ? error.message : "生成数据时发生错误",
        variant: "destructive",
      })
    } finally {
      setBatchGenerating(false)
    }
  }



  const saveBatchToDatabase = async () => {
    if (batchData.length === 0) {
      toast({
        title: "保存失败",
        description: "没有批量数据可保存",
        variant: "destructive",
      })
      return
    }

    setSaving(true)
    try {
      let successCount = 0
      let failCount = 0

      for (const item of batchData) {
        try {
          const generationParams = {
            state,
            city,
            gender,
            minAge,
            maxAge,
            seed: generateSeed(), // 每个数据使用不同的种子
            highState,
            highCity,
            universityState,
            universityCity,
            visibleFields
          }

          const response = await fetch('/api/save-data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              personData: item.personData,
              schoolData: item.schoolData,
              universityData: item.universityData,
              generationParams
            }),
          })

          const result = await response.json()

          if (result.success) {
            successCount++
          } else {
            failCount++
          }
        } catch (error) {
          failCount++
        }
      }

      if (successCount > 0) {
        toast({
          title: "批量保存完成",
          description: `成功保存 ${successCount} 条数据${failCount > 0 ? `，失败 ${failCount} 条` : ''}`,
        })
      } else {
        toast({
          title: "批量保存失败",
          description: "所有数据保存失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "批量保存失败",
        description: error instanceof Error ? error.message : "保存数据时发生错误",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const exportSingleToExcel = async () => {
    if (!data) {
      toast({
        title: "导出失败",
        description: "没有数据可导出",
        variant: "destructive",
      })
      return
    }

    try {
      await exportToExcel({
        personData: data,
        schoolData,
        universityData
      }, undefined, true) // 启用邮箱替换

      toast({
        title: "导出成功",
        description: "Excel文件已下载（邮箱已替换为生成的邮箱）",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "导出Excel时发生错误",
        variant: "destructive",
      })
    }
  }

  const exportBatchToExcel = async () => {
    if (batchData.length === 0) {
      toast({
        title: "导出失败",
        description: "没有批量数据可导出",
        variant: "destructive",
      })
      return
    }

    try {
      await exportToExcel(batchData, undefined, true) // 启用邮箱替换

      toast({
        title: "导出成功",
        description: `已导出 ${batchData.length} 条数据到Excel文件（邮箱已替换为生成的邮箱）`,
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "导出Excel时发生错误",
        variant: "destructive",
      })
    }
  }

  const exportDatabaseToExcel = async () => {
    try {
      // 先获取统计信息
      const statsResponse = await fetch('/api/export/excel')
      const statsResult = await statsResponse.json()

      if (!statsResult.success) {
        toast({
          title: "导出失败",
          description: statsResult.error,
          variant: "destructive",
        })
        return
      }

      if (!statsResult.data.canExport) {
        toast({
          title: "导出失败",
          description: "数据库中没有数据可导出",
          variant: "destructive",
        })
        return
      }

      // 执行导出
      const exportResponse = await fetch('/api/export/excel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ limit: 1000, offset: 0 }),
      })

      if (!exportResponse.ok) {
        throw new Error('导出请求失败')
      }

      // 下载文件
      const blob = await exportResponse.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url

      // 从响应头获取文件名
      const contentDisposition = exportResponse.headers.get('Content-Disposition')
      const filename = contentDisposition
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
        : 'database-export.xlsx'

      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "导出成功",
        description: `已导出 ${statsResult.data.total} 条数据（其中 ${statsResult.data.withEmail} 条包含邮箱）`,
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "导出Excel时发生错误",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    // 只有当URL参数完整时才调用API
    if (searchParams.get("fields") && searchParams.get("seed")) {
      fetchData()
    }
  }, [state, city, gender, minAge, maxAge, seed, highState, highCity, universityState, universityCity])

  const renderDataItem = (key: string, value: any) => {
    if (!visibleFields.includes(key)) return null

    // 处理学校数据字段
    if (key.startsWith("school") && schoolData) {
      let schoolValue = ""
      switch (key) {
        case "schoolName":
          schoolValue = schoolData.name
          break
        case "schoolId":
          schoolValue = schoolData.ncesid
          break
        case "schoolZip":
          schoolValue = schoolData.zip
          break
        case "schoolWebsite":
          schoolValue = schoolData.website && schoolData.website !== "NOT AVAILABLE" ? schoolData.website : ""
          break
        case "schoolAddress":
          schoolValue = `${schoolData.address}, ${schoolData.city}, ${schoolData.state} ${schoolData.zip}`
          break
        case "schoolCity":
          schoolValue = schoolData.city
          break
        case "schoolState":
          schoolValue = schoolData.state
          break
        case "schoolPhone":
          schoolValue = schoolData.telephone
          break
        case "schoolGrades":
          schoolValue = `${schoolData.st_grade}-${schoolData.end_grade}年级`
          break
      }

      if (!schoolValue) return null

      return (
        <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex-1">
            <div className="text-sm text-gray-600 mb-1">{FIELD_LABELS[key]}</div>
            <div className="font-medium text-gray-900">
              {key === "schoolWebsite" && schoolValue ? (
                <a
                  href={schoolValue.startsWith("http") ? schoolValue : `https://${schoolValue}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  {schoolValue}
                </a>
              ) : (
                schoolValue
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => copyToClipboard(schoolValue, key)}
            className="ml-2 h-8 w-8 p-0"
          >
            {copiedField === key ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
          </Button>
        </div>
      )
    }

    // 处理大学数据字段
    if (key.startsWith("university") && universityData) {
      let universityValue = ""
      switch (key) {
        case "universityName":
          universityValue = universityData.name
          break
        case "universityId":
          universityValue = universityData.ipedsid
          break
        case "universityZip":
          universityValue = universityData.zip
          break
        case "universityWebsite":
          universityValue =
            universityData.website && universityData.website !== "NOT AVAILABLE" ? universityData.website : ""
          break
        case "universityAddress":
          universityValue = `${universityData.address}, ${universityData.city}, ${universityData.state} ${universityData.zip}`
          break
        case "universityCity":
          universityValue = universityData.city
          break
        case "universityState":
          universityValue = universityData.state
          break
        case "universityPhone":
          universityValue = universityData.telephone
          break
        case "universityType":
          const typeMap: Record<string, string> = {
            "1": "公立大学",
            "2": "私立非营利大学",
            "3": "私立营利大学",
          }
          universityValue = typeMap[universityData.type] || universityData.type
          break
      }

      if (!universityValue) return null

      return (
        <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex-1">
            <div className="text-sm text-gray-600 mb-1">{FIELD_LABELS[key]}</div>
            <div className="font-medium text-gray-900">
              {key === "universityWebsite" && universityValue ? (
                <a
                  href={universityValue.startsWith("http") ? universityValue : `https://${universityValue}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  {universityValue}
                </a>
              ) : (
                universityValue
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => copyToClipboard(universityValue, key)}
            className="ml-2 h-8 w-8 p-0"
          >
            {copiedField === key ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
          </Button>
        </div>
      )
    }

    return (
      <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div className="flex-1">
          <div className="text-sm text-gray-600 mb-1">{FIELD_LABELS[key]}</div>
          <div className="font-medium text-gray-900">{value?.toString()}</div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => copyToClipboard(value?.toString() || "", key)}
          className="ml-2 h-8 w-8 p-0"
        >
          {copiedField === key ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
        </Button>
      </div>
    )
  }

  const renderCategory = (categoryKey: string, category: any) => {
    const categoryFields = category.fields.filter((field: string) => {
      if (!visibleFields.includes(field)) return false

      // 处理学校字段
      if (field.startsWith("school")) {
        return schoolData !== null
      }

      // 处理大学字段
      if (field.startsWith("university")) {
        return universityData !== null
      }

      // 处理普通字段
      return data && data[field as keyof PersonData]
    })

    if (categoryFields.length === 0) return null

    return (
      <Card key={categoryKey} className="w-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-800">{category.title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {categoryFields.map((field: string) => renderDataItem(field, data?.[field as keyof PersonData]))}
        </CardContent>
      </Card>
    )
  }

  const handleRegenerate = () => {
    const newSeed = generateSeed()
    fetchData(newSeed)
  }

  const currentYear = new Date().getFullYear()

  const getStateDisplayName = (stateCode: string) => {
    const stateInfo = US_STATES.find((s) => s.code === stateCode)
    return stateInfo ? `${stateInfo.name} (${stateInfo.code})` : stateCode
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">美国地址生成器</h1>
          <p className="text-lg text-gray-600 mb-6">生成真实的美国地址和个人信息数据</p>

          <div className="flex flex-wrap justify-center gap-4 mb-6">
            <Badge variant="outline" className="text-sm">
              随机种子: {seed}
            </Badge>
            {state && (
              <Badge variant="secondary" className="text-sm">
                州: {getStateDisplayName(state)}
              </Badge>
            )}
            {city && (
              <Badge variant="secondary" className="text-sm">
                城市: {city}
              </Badge>
            )}
            {gender && (
              <Badge variant="secondary" className="text-sm">
                性别: {gender === "Male" ? "男" : "女"}
              </Badge>
            )}
            {minAge > 0 && maxAge > 0 && (
              <Badge variant="secondary" className="text-sm">
                年龄: {minAge}-{maxAge}岁 ({currentYear - maxAge}-{currentYear - minAge}年出生)
              </Badge>
            )}
            {highState && (
              <Badge variant="secondary" className="text-sm">
                高中州: {getStateDisplayName(highState)}
              </Badge>
            )}
            {highCity && (
              <Badge variant="secondary" className="text-sm">
                高中城市: {highCity}
              </Badge>
            )}
            {universityState && (
              <Badge variant="secondary" className="text-sm">
                大学州: {getStateDisplayName(universityState)}
              </Badge>
            )}
            {universityCity && (
              <Badge variant="secondary" className="text-sm">
                大学城市: {universityCity}
              </Badge>
            )}
          </div>

          <div className="flex justify-center gap-4 flex-wrap">
            <Button onClick={handleRegenerate} disabled={loading || batchGenerating} className="bg-blue-600 hover:bg-blue-700">
              {loading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重新生成
                </>
              )}
            </Button>
            <BatchGenerateControl
              onGenerate={batchGenerate}
              loading={loading}
              generating={batchGenerating}
              defaultCount={10}
            />
            <Button
              variant="outline"
              onClick={saveToDatabase}
              disabled={!data || saving}
              className="bg-green-50 hover:bg-green-100 border-green-200"
            >
              {saving ? (
                <>
                  <Save className="mr-2 h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  保存到数据库
                </>
              )}
            </Button>
            <Button variant="outline" onClick={() => setJsonDialogOpen(true)} disabled={!data}>
              <Download className="mr-2 h-4 w-4" />
              导出JSON
            </Button>
            <Button
              variant="outline"
              onClick={exportSingleToExcel}
              disabled={!data}
              className="bg-emerald-50 hover:bg-emerald-100 border-emerald-200"
            >
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              导出Excel
            </Button>

            <Button variant="outline" onClick={() => setConfigOpen(true)}>
              <Settings className="mr-2 h-4 w-4" />
              配置参数
            </Button>

            <Button
              variant="outline"
              onClick={exportDatabaseToExcel}
              className="bg-indigo-50 hover:bg-indigo-100 border-indigo-200"
            >
              <Database className="mr-2 h-4 w-4" />
              导出数据库Excel
            </Button>
            {batchData.length > 0 && (
              <Button
                variant="outline"
                onClick={() => setShowBatchResults(true)}
                className="bg-yellow-50 hover:bg-yellow-100 border-yellow-200"
              >
                <Users className="mr-2 h-4 w-4" />
                查看批量结果 ({batchData.length}条)
              </Button>
            )}
          </div>
        </div>

        {data && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(FIELD_CATEGORIES).map(([key, category]) => renderCategory(key, category))}
          </div>
        )}

        {!data && !loading && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">点击"重新生成"按钮开始生成数据</p>
          </div>
        )}
      </div>

      <ConfigDialog
        open={configOpen}
        onOpenChange={setConfigOpen}
        currentConfig={{
          state,
          city,
          gender,
          minAge,
          maxAge,
          visibleFields,
          seed,
          birthYear: 0,
          highState,
          highCity,
          universityState,
          universityCity,
        }}
      />
      <JsonDownloadDialog
        open={jsonDialogOpen}
        onOpenChange={setJsonDialogOpen}
        data={data}
        schoolData={schoolData}
        universityData={universityData}
        visibleFields={visibleFields}
      />
      <BatchResultsDialog
        open={showBatchResults}
        onOpenChange={setShowBatchResults}
        batchData={batchData}
        onSaveBatch={saveBatchToDatabase}
        saving={saving}
      />
    </div>
  )
}
