/**
 * 应用配置管理
 * 统一管理环境变量和应用配置
 */

export interface AuthConfig {
  adminUsername: string
  adminPassword: string
  jwtSecret: string
  sessionTimeout: number
  maxLoginAttempts: number
  lockoutDuration: number
}

export interface AppConfig {
  auth: AuthConfig
  app: {
    name: string
    version: string
    environment: string
    nextAuthUrl?: string
  }
  database: {
    supabaseUrl: string
    supabaseKey: string
  }
  email: {
    imapServer?: string
    imapPort?: number
    imapUser?: string
    imapPassword?: string
    tempMailDomain?: string
  }
  security: {
    forceHttps: boolean
    securityHeaders: boolean
    debugMode: boolean
  }
  api: {
    timeout: number
    logLevel: string
  }
}

/**
 * 获取环境变量，支持默认值
 */
function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key]
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue
    }
    throw new Error(`环境变量 ${key} 未设置`)
  }
  return value
}

/**
 * 获取数字类型的环境变量
 */
function getEnvNumber(key: string, defaultValue?: number): number {
  const value = process.env[key]
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue
    }
    throw new Error(`环境变量 ${key} 未设置`)
  }
  
  const numValue = parseInt(value, 10)
  if (isNaN(numValue)) {
    throw new Error(`环境变量 ${key} 必须是数字`)
  }
  
  return numValue
}

/**
 * 获取布尔类型的环境变量
 */
function getEnvBoolean(key: string, defaultValue?: boolean): boolean {
  const value = process.env[key]
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue
    }
    throw new Error(`环境变量 ${key} 未设置`)
  }
  
  return value.toLowerCase() === 'true'
}

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars(): void {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'ADMIN_USERNAME',
    'ADMIN_PASSWORD',
    'JWT_SECRET'
  ]

  const missingVars = requiredVars.filter(varName => !process.env[varName])

  if (missingVars.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missingVars.join(', ')}`)
  }

  // 验证环境变量格式
  const jwtSecret = process.env.JWT_SECRET
  if (jwtSecret && jwtSecret.length < 32) {
    throw new Error('JWT_SECRET 必须至少32个字符')
  }

  const adminPassword = process.env.ADMIN_PASSWORD
  if (adminPassword && adminPassword.length < 6) {
    throw new Error('ADMIN_PASSWORD 必须至少6个字符')
  }
}

/**
 * 创建应用配置
 */
function createAppConfig(): AppConfig {
  // 验证必需的环境变量
  validateRequiredEnvVars()

  return {
    auth: {
      adminUsername: getEnvVar('ADMIN_USERNAME'),
      adminPassword: getEnvVar('ADMIN_PASSWORD'),
      jwtSecret: getEnvVar('JWT_SECRET'),
      sessionTimeout: getEnvNumber('SESSION_TIMEOUT_HOURS', 24) * 60 * 60 * 1000, // 转换为毫秒
      maxLoginAttempts: getEnvNumber('MAX_LOGIN_ATTEMPTS', 5),
      lockoutDuration: getEnvNumber('LOCKOUT_DURATION_MINUTES', 15) * 60 * 1000, // 转换为毫秒
    },
    app: {
      name: getEnvVar('APP_NAME', '美国地址生成器'),
      version: getEnvVar('APP_VERSION', '1.0.0'),
      environment: getEnvVar('NODE_ENV', 'development'),
      nextAuthUrl: getEnvVar('NEXTAUTH_URL', ''),
    },
    database: {
      supabaseUrl: getEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
      supabaseKey: getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
    },
    email: {
      imapServer: getEnvVar('IMAP_SERVER', ''),
      imapPort: getEnvNumber('IMAP_PORT', 993),
      imapUser: getEnvVar('IMAP_USER', ''),
      imapPassword: getEnvVar('IMAP_PASSWORD', ''),
      tempMailDomain: getEnvVar('TEMP_MAIL_DOMAIN', ''),
    },
    security: {
      forceHttps: getEnvBoolean('FORCE_HTTPS', false),
      securityHeaders: getEnvBoolean('SECURITY_HEADERS', true),
      debugMode: getEnvBoolean('DEBUG_MODE', false),
    },
    api: {
      timeout: getEnvNumber('API_TIMEOUT', 30000),
      logLevel: getEnvVar('LOG_LEVEL', 'info'),
    }
  }
}

// 导出配置实例
let config: AppConfig | null = null

export function getConfig(): AppConfig {
  if (!config) {
    try {
      config = createAppConfig()
    } catch (error) {
      console.error('配置初始化失败:', error)
      throw error
    }
  }
  return config
}

/**
 * 检查配置是否有效
 */
export function validateConfig(): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    const cfg = getConfig()

    // 验证身份验证配置
    if (!cfg.auth.adminUsername || cfg.auth.adminUsername.length < 3) {
      errors.push('管理员用户名至少需要3个字符')
    }

    if (cfg.auth.adminUsername === 'admin') {
      warnings.push('建议使用更复杂的管理员用户名，避免使用默认的 "admin"')
    }

    if (!cfg.auth.adminPassword || cfg.auth.adminPassword.length < 6) {
      errors.push('管理员密码至少需要6个字符')
    }

    if (cfg.auth.adminPassword === 'your_secure_password_here') {
      errors.push('请设置真实的管理员密码，不要使用默认值')
    }

    // 检查密码强度
    const password = cfg.auth.adminPassword
    if (password && password.length >= 6) {
      const hasUpper = /[A-Z]/.test(password)
      const hasLower = /[a-z]/.test(password)
      const hasNumber = /\d/.test(password)
      const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password)

      if (!hasUpper || !hasLower || !hasNumber) {
        warnings.push('建议密码包含大写字母、小写字母和数字')
      }

      if (!hasSpecial) {
        warnings.push('建议密码包含特殊字符以提高安全性')
      }
    }

    if (!cfg.auth.jwtSecret || cfg.auth.jwtSecret.length < 32) {
      errors.push('JWT密钥至少需要32个字符')
    }

    if (cfg.auth.jwtSecret === 'your_jwt_secret_key_at_least_32_characters_long') {
      errors.push('请设置真实的JWT密钥，不要使用默认值')
    }

    // 验证数据库配置
    if (!cfg.database.supabaseUrl || !cfg.database.supabaseUrl.startsWith('https://')) {
      errors.push('Supabase URL格式无效，必须以 https:// 开头')
    }

    if (cfg.database.supabaseUrl === 'your_supabase_project_url') {
      errors.push('请设置真实的Supabase项目URL')
    }

    if (!cfg.database.supabaseKey || cfg.database.supabaseKey.length < 20) {
      errors.push('Supabase密钥格式无效，长度至少20个字符')
    }

    if (cfg.database.supabaseKey === 'your_supabase_anon_key') {
      errors.push('请设置真实的Supabase匿名密钥')
    }

    // 验证会话配置
    if (cfg.auth.sessionTimeout < 60000) { // 小于1分钟
      warnings.push('会话超时时间过短，建议至少1小时')
    }

    if (cfg.auth.sessionTimeout > 7 * 24 * 60 * 60 * 1000) { // 大于7天
      warnings.push('会话超时时间过长，可能存在安全风险')
    }

    // 验证邮件配置（如果提供）
    if (cfg.email.imapServer && cfg.email.imapUser && !cfg.email.imapPassword) {
      warnings.push('IMAP服务器和用户已配置，但缺少密码')
    }

    // 验证生产环境配置
    if (cfg.app.environment === 'production') {
      if (!cfg.security.forceHttps) {
        warnings.push('生产环境建议启用HTTPS强制重定向')
      }

      if (cfg.security.debugMode) {
        warnings.push('生产环境不应启用调试模式')
      }

      if (!cfg.app.nextAuthUrl) {
        warnings.push('生产环境建议设置NEXTAUTH_URL')
      }
    }

  } catch (error) {
    errors.push(`配置验证失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 获取安全的配置信息（隐藏敏感信息）
 */
export function getSafeConfig() {
  const cfg = getConfig()

  return {
    app: cfg.app,
    auth: {
      adminUsername: cfg.auth.adminUsername,
      sessionTimeout: cfg.auth.sessionTimeout,
      maxLoginAttempts: cfg.auth.maxLoginAttempts,
      lockoutDuration: cfg.auth.lockoutDuration,
      // 隐藏敏感信息
      jwtSecret: cfg.auth.jwtSecret.substring(0, 8) + '...',
      adminPassword: '***',
    },
    database: {
      supabaseUrl: cfg.database.supabaseUrl,
      // 隐藏密钥
      supabaseKey: cfg.database.supabaseKey.substring(0, 10) + '...',
    },
    email: {
      imapServer: cfg.email.imapServer,
      imapPort: cfg.email.imapPort,
      imapUser: cfg.email.imapUser ? cfg.email.imapUser.replace(/(.{3}).*(@.*)/, '$1***$2') : '',
      imapPassword: cfg.email.imapPassword ? '***' : '',
      tempMailDomain: cfg.email.tempMailDomain,
    },
    security: cfg.security,
    api: cfg.api
  }
}

// 开发环境下的配置检查
if (process.env.NODE_ENV === 'development') {
  try {
    const validation = validateConfig()
    if (!validation.isValid) {
      console.error('❌ 配置验证失败:')
      validation.errors.forEach(error => console.error(`  - ${error}`))
    } else {
      console.log('✅ 配置验证通过')
    }

    if (validation.warnings.length > 0) {
      console.warn('⚠️ 配置警告:')
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`))
    }
  } catch (error) {
    console.error('❌ 配置初始化失败:', error)
  }
}

/**
 * 生成安全的随机密钥
 */
export function generateSecureKey(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 检查密码强度
 */
export function checkPasswordStrength(password: string): {
  score: number // 0-4
  feedback: string[]
} {
  const feedback: string[] = []
  let score = 0

  if (password.length >= 8) {
    score++
  } else {
    feedback.push('密码长度至少8个字符')
  }

  if (/[A-Z]/.test(password)) {
    score++
  } else {
    feedback.push('包含大写字母')
  }

  if (/[a-z]/.test(password)) {
    score++
  } else {
    feedback.push('包含小写字母')
  }

  if (/\d/.test(password)) {
    score++
  } else {
    feedback.push('包含数字')
  }

  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score++
  } else {
    feedback.push('包含特殊字符')
  }

  return { score, feedback }
}
