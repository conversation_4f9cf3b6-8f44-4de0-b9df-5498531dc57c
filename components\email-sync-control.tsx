"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  RefreshCw,
  Play,
  Pause,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle,
  Mail,
  Lock,
  User
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuthState } from "@/hooks/use-auth"

interface SyncStatus {
  isRunning: boolean
  interval: number
  lastRun: string | null
  nextRun: string | null
  status: string
  intervalText: string
  lastRunText: string
  nextRunText: string
}

interface EmailSyncControlProps {
  onSyncComplete?: () => void
}

export default function EmailSyncControl({ onSyncComplete }: EmailSyncControlProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [settingsOpen, setSettingsOpen] = useState(false)
  const [syncInterval, setSyncInterval] = useState(5)
  const { toast } = useToast()
  const { isAuthenticated, loading: authLoading } = useAuthState()

  // 获取同步状态
  const fetchSyncStatus = async () => {
    try {
      console.log('正在获取同步状态...')
      const response = await fetch('/api/email-sync/schedule', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache'
      })

      console.log('响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('同步状态结果:', result)

      if (result.success) {
        setSyncStatus(result.data)
      } else {
        console.error('获取同步状态失败:', result.error)

        // 如果是未授权错误，显示特殊提示
        if (result.code === 'UNAUTHORIZED') {
          setSyncStatus({
            isRunning: false,
            interval: 5,
            lastRun: null,
            nextRun: null,
            status: 'unauthorized',
            intervalText: '5 分钟',
            lastRunText: '需要登录',
            nextRunText: '需要登录'
          })
        } else {
          toast({
            title: "获取状态失败",
            description: result.error || "无法获取同步状态",
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      console.error('获取同步状态失败:', error)
      // 不显示toast，避免频繁弹出错误
      // 设置一个默认状态
      setSyncStatus({
        isRunning: false,
        interval: 5,
        lastRun: null,
        nextRun: null,
        status: 'unknown',
        intervalText: '5 分钟',
        lastRunText: '无法获取',
        nextRunText: '无法获取'
      })
    }
  }

  // 手动同步所有邮箱
  const manualSyncAll = async () => {
    setLoading(true)
    try {
      console.log('开始手动同步所有邮箱...')
      const response = await fetch('/api/email-sync-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          syncType: 'all'
        }),
      })

      console.log('手动同步响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('手动同步结果:', result)

      if (result.success) {
        toast({
          title: "同步完成",
          description: `成功同步 ${result.data?.totalSynced || 0} 条新邮件`,
        })

        if (onSyncComplete) {
          onSyncComplete()
        }

        // 刷新状态
        await fetchSyncStatus()
      } else {
        toast({
          title: "同步失败",
          description: result.error || "同步过程中发生错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('手动同步失败:', error)
      toast({
        title: "同步失败",
        description: error instanceof Error ? error.message : "网络错误",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 启动定时同步
  const startScheduledSync = async () => {
    try {
      console.log('启动定时同步，间隔:', syncInterval)
      const response = await fetch('/api/email-sync/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'start',
          interval: syncInterval
        }),
      })

      console.log('启动定时同步响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('启动定时同步结果:', result)

      if (result.success) {
        toast({
          title: "定时同步已启动",
          description: `每 ${syncInterval} 分钟自动同步一次`,
        })
        await fetchSyncStatus()
      } else {
        toast({
          title: "启动失败",
          description: result.error || "启动定时同步失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('启动定时同步失败:', error)
      toast({
        title: "启动失败",
        description: error instanceof Error ? error.message : "网络错误",
        variant: "destructive",
      })
    }
  }

  // 停止定时同步
  const stopScheduledSync = async () => {
    try {
      console.log('停止定时同步...')
      const response = await fetch('/api/email-sync/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'stop'
        }),
      })

      console.log('停止定时同步响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('停止定时同步结果:', result)

      if (result.success) {
        toast({
          title: "定时同步已停止",
          description: "不再自动同步邮件",
        })
        await fetchSyncStatus()
      } else {
        toast({
          title: "停止失败",
          description: result.error || "停止定时同步失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('停止定时同步失败:', error)
      toast({
        title: "停止失败",
        description: error instanceof Error ? error.message : "网络错误",
        variant: "destructive",
      })
    }
  }

  // 保存设置
  const saveSettings = async () => {
    if (syncInterval < 1 || syncInterval > 60) {
      toast({
        title: "设置无效",
        description: "同步间隔必须在1-60分钟之间",
        variant: "destructive",
      })
      return
    }

    try {
      console.log('保存设置，新间隔:', syncInterval)
      const response = await fetch('/api/email-sync/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'restart',
          interval: syncInterval
        }),
      })

      console.log('保存设置响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('保存设置结果:', result)

      if (result.success) {
        toast({
          title: "设置已保存",
          description: `同步间隔已更新为 ${syncInterval} 分钟`,
        })
        setSettingsOpen(false)
        await fetchSyncStatus()
      } else {
        toast({
          title: "保存失败",
          description: result.error || "保存设置失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "网络错误",
        variant: "destructive",
      })
    }
  }

  // 测试网络连接
  const testConnection = async () => {
    try {
      console.log('测试网络连接...')
      const response = await fetch('/api/email-sync/schedule', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      console.log('网络测试响应:', response.status, response.statusText)
      if (response.ok) {
        console.log('网络连接正常')
      } else {
        console.error('网络连接异常:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('网络连接测试失败:', error)
    }
  }

  useEffect(() => {
    // 只有在已登录时才获取同步状态
    if (!authLoading && isAuthenticated) {
      // 先测试网络连接
      testConnection()

      // 延迟获取状态，避免立即请求
      const timer = setTimeout(() => {
        fetchSyncStatus()
      }, 1000)

      // 每30秒刷新一次状态
      const interval = setInterval(fetchSyncStatus, 30000)

      return () => {
        clearTimeout(timer)
        clearInterval(interval)
      }
    } else if (!authLoading && !isAuthenticated) {
      // 未登录时设置特殊状态
      setSyncStatus({
        isRunning: false,
        interval: 5,
        lastRun: null,
        nextRun: null,
        status: 'unauthorized',
        intervalText: '5 分钟',
        lastRunText: '需要登录',
        nextRunText: '需要登录'
      })
    }
  }, [isAuthenticated, authLoading])

  return (
    <>
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Mail className="h-5 w-5" />
              邮件同步器
            </CardTitle>
            <div className="flex items-center gap-2">
              {syncStatus && (
                <Badge 
                  variant={syncStatus.isRunning ? "default" : "secondary"}
                  className={syncStatus.isRunning ? "bg-green-600" : ""}
                >
                  {syncStatus.status === 'running' ? '运行中' : '已停止'}
                </Badge>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSettingsOpen(true)}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 未登录提示 */}
          {!isAuthenticated && !authLoading && (
            <div className="flex items-center gap-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <Lock className="h-5 w-5 text-amber-600" />
              <div>
                <p className="font-medium text-amber-800">需要登录</p>
                <p className="text-sm text-amber-700">邮件同步功能需要登录后才能使用</p>
              </div>
            </div>
          )}

          {/* 同步状态信息 */}
          {syncStatus && isAuthenticated && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="font-medium">同步间隔</p>
                  <p className="text-gray-600">{syncStatus.intervalText}</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <div>
                  <p className="font-medium">上次同步</p>
                  <p className="text-gray-600">{syncStatus.lastRunText}</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <div>
                  <p className="font-medium">下次同步</p>
                  <p className="text-gray-600">{syncStatus.nextRunText}</p>
                </div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center gap-2">
            <Button
              onClick={manualSyncAll}
              disabled={loading || !isAuthenticated}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
            >
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  同步中...
                </>
              ) : !isAuthenticated ? (
                <>
                  <Lock className="h-4 w-4 mr-2" />
                  需要登录
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  立即同步
                </>
              )}
            </Button>

            {isAuthenticated && syncStatus?.isRunning ? (
              <Button
                onClick={stopScheduledSync}
                variant="outline"
                className="border-red-200 text-red-700 hover:bg-red-50"
              >
                <Pause className="h-4 w-4 mr-2" />
                停止定时
              </Button>
            ) : isAuthenticated ? (
              <Button
                onClick={startScheduledSync}
                variant="outline"
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                <Play className="h-4 w-4 mr-2" />
                启动定时
              </Button>
            ) : (
              <Button
                disabled
                variant="outline"
                className="border-gray-200 text-gray-400"
              >
                <User className="h-4 w-4 mr-2" />
                请先登录
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 设置对话框 */}
      <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>同步设置</DialogTitle>
            <DialogDescription>
              配置邮件自动同步的间隔时间
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="interval">同步间隔（分钟）</Label>
              <Input
                id="interval"
                type="number"
                min="1"
                max="60"
                value={syncInterval}
                onChange={(e) => setSyncInterval(parseInt(e.target.value) || 5)}
                className="mt-1"
              />
              <p className="text-sm text-gray-500 mt-1">
                建议设置为5-15分钟，避免频繁请求
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setSettingsOpen(false)}>
              取消
            </Button>
            <Button onClick={saveSettings}>
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
