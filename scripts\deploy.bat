@echo off
REM 美国地址生成器 Docker 部署脚本 (Windows)
REM 使用方法: scripts\deploy.bat [环境] [选项]

setlocal enabledelayedexpansion

REM 设置默认值
set ENV=dev
set FORCE_BUILD=false
set NO_CACHE=false
set WITH_NGINX=false

REM 解析参数
:parse_args
if "%1"=="" goto :check_deps
if "%1"=="dev" (
    set ENV=dev
    shift
    goto :parse_args
)
if "%1"=="prod" (
    set ENV=prod
    shift
    goto :parse_args
)
if "%1"=="--build" (
    set FORCE_BUILD=true
    shift
    goto :parse_args
)
if "%1"=="--no-cache" (
    set NO_CACHE=true
    shift
    goto :parse_args
)
if "%1"=="--nginx" (
    set WITH_NGINX=true
    shift
    goto :parse_args
)
if "%1"=="--help" (
    goto :show_help
)
echo 未知参数: %1
goto :show_help

:show_help
echo 美国地址生成器 Docker 部署脚本
echo.
echo 使用方法:
echo   scripts\deploy.bat [环境] [选项]
echo.
echo 环境:
echo   dev         开发环境部署
echo   prod        生产环境部署
echo.
echo 选项:
echo   --build     强制重新构建镜像
echo   --no-cache  构建时不使用缓存
echo   --nginx     同时启动 Nginx 反向代理
echo   --help      显示此帮助信息
echo.
echo 示例:
echo   scripts\deploy.bat dev                    # 开发环境部署
echo   scripts\deploy.bat prod --build          # 生产环境部署并重新构建
echo   scripts\deploy.bat prod --nginx          # 生产环境部署并启动 Nginx
goto :end

:check_deps
echo [INFO] 检查依赖...

REM 检查 Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker Desktop
    goto :error
)

REM 检查 Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
        goto :error
    )
)

echo [SUCCESS] 依赖检查通过

:check_env
REM 设置环境变量文件
if "%ENV%"=="prod" (
    set ENV_FILE=.env.production
) else (
    set ENV_FILE=.env.local
)

echo [INFO] 检查环境变量文件: %ENV_FILE%

if not exist "%ENV_FILE%" (
    echo [ERROR] 环境变量文件 %ENV_FILE% 不存在
    echo [INFO] 请复制 .env.local.example 为 %ENV_FILE% 并配置相应的环境变量
    goto :error
)

echo [SUCCESS] 环境变量文件检查通过

:build
if "%FORCE_BUILD%"=="true" (
    echo [INFO] 构建 Docker 镜像...
    
    set BUILD_ARGS=
    if "%NO_CACHE%"=="true" (
        set BUILD_ARGS=--no-cache
    )
    
    docker build !BUILD_ARGS! -t us-fake-gen-ui:latest .
    if errorlevel 1 (
        echo [ERROR] Docker 镜像构建失败
        goto :error
    )
    
    echo [SUCCESS] Docker 镜像构建完成
)

:deploy
echo [INFO] 部署应用 (环境: %ENV%)...

REM 检查 Docker Compose 命令
set COMPOSE_CMD=docker-compose
docker compose version >nul 2>&1
if not errorlevel 1 (
    set COMPOSE_CMD=docker compose
)

REM 停止现有容器
echo [INFO] 停止现有容器...
%COMPOSE_CMD% down

REM 设置服务
set SERVICES=app
if "%WITH_NGINX%"=="true" (
    set SERVICES=app nginx
    set COMPOSE_PROFILES=with-nginx
)

REM 启动服务
echo [INFO] 启动服务: %SERVICES%
%COMPOSE_CMD% up -d %SERVICES%
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    %COMPOSE_CMD% logs
    goto :error
)

REM 等待服务启动
echo [INFO] 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
%COMPOSE_CMD% ps | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] 应用部署失败
    %COMPOSE_CMD% logs
    goto :error
)

echo [SUCCESS] 应用部署成功
echo.
echo [INFO] 应用访问信息:
if "%WITH_NGINX%"=="true" (
    echo   HTTP:  http://localhost
    echo   HTTPS: https://localhost (需要配置 SSL 证书)
) else (
    echo   应用:  http://localhost:3000
)
echo   健康检查: http://localhost:3000/api/health

echo [SUCCESS] 部署完成!
goto :end

:error
echo [ERROR] 部署失败
exit /b 1

:end
endlocal
