import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { generateEmail } from '@/lib/email-generator'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { personData, schoolData, universityData, generationParams } = body

    if (!personData) {
      return NextResponse.json(
        { success: false, error: '缺少个人数据' },
        { status: 400 }
      )
    }

    // 生成邮箱（如果需要）
    let generatedEmailData = null
    try {
      const emailResult = await generateEmail()
      if (emailResult.success) {
        generatedEmailData = emailResult.data
      }
    } catch (error) {
      console.warn('生成邮箱失败，继续保存数据:', error)
    }

    // 准备要保存到Supabase的数据
    const dataToSave = {
      // 基本信息
      full_name: personData.fullName,
      first_name: personData.firstName,
      last_name: personData.lastName,
      gender: personData.gender,
      birthday: personData.birthday,
      title: personData.title,
      hair_color: personData.hairColor,
      country: personData.country || 'United States',

      // 地址信息
      street: personData.street,
      city: personData.city,
      state: personData.state,
      state_full_name: personData.stateFullName,
      zip_code: personData.zipCode,
      phone: personData.phone,
      email: generatedEmailData?.email || personData.email,
      full_address: personData.fullAddress,

      // 工作信息
      occupation: personData.occupation,
      company: personData.company,
      company_size: personData.companySize,
      industry: personData.industry,
      status: personData.status,
      salary: personData.salary,

      // 金融信息
      ssn: personData.ssn,
      card_type: personData.cardType,
      card_number: personData.cardNumber,
      cvv: personData.cvv,
      expiry: personData.expiry,

      // 账户信息
      username: personData.username,
      password: generatedEmailData?.password || personData.password,
      security_question: personData.securityQuestion,
      security_answer: personData.securityAnswer,

      // 身体信息
      height: personData.height,
      weight: personData.weight,
      blood_type: personData.bloodType,

      // 技术信息
      os: personData.os,
      guid: personData.guid,
      user_agent: personData.userAgent,

      // 其他信息
      education: personData.education,
      website: personData.website,

      // 学校信息
      school_name: schoolData?.name,
      school_id: schoolData?.id,
      school_zip: schoolData?.zip,
      school_website: schoolData?.website,
      school_address: schoolData?.address,
      school_city: schoolData?.city,
      school_state: schoolData?.state,
      school_phone: schoolData?.phone,
      school_grades: schoolData?.grades,

      // 大学信息
      university_name: universityData?.name,
      university_id: universityData?.id,
      university_zip: universityData?.zip,
      university_website: universityData?.website,
      university_address: universityData?.address,
      university_city: universityData?.city,
      university_state: universityData?.state,
      university_phone: universityData?.phone,
      university_type: universityData?.type,

      // 生成参数
      generation_seed: generationParams?.seed,
      generation_params: generationParams ? JSON.stringify(generationParams) : null
    }

    // 保存到Supabase
    const { data, error } = await supabase
      .from('saved_person_data')
      .insert([dataToSave])
      .select()

    if (error) {
      console.error('Supabase error:', error)
      return NextResponse.json(
        { success: false, error: '保存数据失败: ' + error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        savedData: data[0],
        emailData: generatedEmailData
      },
      message: '数据保存成功'
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : '服务器错误' },
      { status: 500 }
    )
  }
}
