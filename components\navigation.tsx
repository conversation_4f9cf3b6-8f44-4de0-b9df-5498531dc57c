"use client"

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuthState } from '@/hooks/use-auth'
import { UserMenu } from './user-menu'
import { Button } from './ui/button'
import {
  Home,
  Mail,
  Database,
  Settings,
  Menu,
  X,
  LogOut,
  User,
  TestTube
} from 'lucide-react'

/**
 * 主导航栏组件
 * 提供应用的主要导航功能和用户状态显示
 */
export function Navigation() {
  const router = useRouter()
  const { isAuthenticated, user, loading } = useAuthState()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // 导航菜单项
  const navigationItems = [
    {
      name: '首页',
      href: '/',
      icon: Home,
      description: '地址生成器主页'
    },
    {
      name: '系统设置',
      href: '/settings',
      icon: Settings,
      description: '系统配置和管理'
    },
    {
      name: '邮件管理系统',
      href: '/email-manager',
      icon: Mail,
      description: '邮件系统管理'
    },
    {
      name: '数据管理中心',
      href: '/saved-data',
      icon: Database,
      description: '保存的数据管理'
    },
    {
      name: '测试临时邮箱',
      href: '/email-test',
      icon: TestTube,
      description: '临时邮箱测试工具'
    }
  ]

  // 处理登出
  const handleLogout = async () => {
    try {
      // 获取token
      const token = localStorage.getItem('auth-token') ||
                   document.cookie.split('; ').find(row => row.startsWith('auth-token='))?.split('=')[1]

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      // 如果有token，添加到Authorization header
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers,
        credentials: 'include', // 确保包含cookies
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // 清除本地存储的token
        localStorage.removeItem('auth-token')

        // 登出成功，重定向到登录页面
        router.push('/login')
      } else {
        console.error('登出失败:', data.error || '未知错误')
        alert('登出失败: ' + (data.error || '未知错误'))
      }
    } catch (error) {
      console.error('登出请求失败:', error)
      alert('登出请求失败，请重试')
    }
  }

  // 如果正在加载，显示简化的导航栏
  if (loading) {
    return (
      <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  美国地址生成器
                </h1>
              </div>
            </div>
            <div className="flex items-center">
              <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-20 rounded"></div>
            </div>
          </div>
        </div>
      </nav>
    )
  }

  // 如果未登录，显示简化的导航栏
  if (!isAuthenticated) {
    return (
      <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  美国地址生成器
                </h1>
              </div>
            </div>
          </div>
        </div>
      </nav>
    )
  }

  return (
    <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* 左侧：Logo和主导航 */}
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                美国地址生成器
              </h1>
            </div>
            
            {/* 桌面端导航菜单 */}
            <div className="hidden md:ml-6 md:flex md:space-x-8">
              {navigationItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600 transition-colors duration-200"
                    title={item.description}
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    {item.name}
                  </Link>
                )
              })}
            </div>
          </div>

          {/* 右侧：用户菜单和移动端菜单按钮 */}
          <div className="flex items-center">
            {/* 用户信息显示 */}
            <div className="hidden md:flex md:items-center md:space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                <User className="w-4 h-4" />
                <span>欢迎，{user?.username || '管理员'}</span>
              </div>
              
              {/* 登出按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>登出</span>
              </Button>
            </div>

            {/* 移动端菜单按钮 */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="inline-flex items-center justify-center p-2"
              >
                {isMobileMenuOpen ? (
                  <X className="w-6 h-6" />
                ) : (
                  <Menu className="w-6 h-6" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* 移动端导航菜单 */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="pt-2 pb-3 space-y-1">
              {navigationItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="flex items-center px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    {item.name}
                  </Link>
                )
              })}
              
              {/* 移动端用户信息和登出 */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4 pb-3">
                <div className="flex items-center px-3 mb-3">
                  <User className="w-5 h-5 mr-3 text-gray-400" />
                  <span className="text-base font-medium text-gray-600 dark:text-gray-300">
                    {user?.username || '管理员'}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="mx-3 flex items-center space-x-2"
                >
                  <LogOut className="w-4 h-4" />
                  <span>登出</span>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
