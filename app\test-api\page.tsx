"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestApiPage() {
  const [result, setResult] = useState<string>("")
  const [loading, setLoading] = useState(false)

  const testApi = async () => {
    setLoading(true)
    setResult("测试中...")

    try {
      console.log('开始测试API...')
      
      const response = await fetch('/api/email-sync/schedule', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache'
      })

      console.log('API响应:', response)
      console.log('响应状态:', response.status, response.statusText)
      console.log('响应头:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('API数据:', data)

      setResult(`成功！状态: ${response.status}, 数据: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      console.error('API测试失败:', error)
      setResult(`失败: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setLoading(false)
    }
  }

  const testFetch = async () => {
    setLoading(true)
    setResult("测试基础fetch...")

    try {
      const response = await fetch('/')
      console.log('基础fetch响应:', response.status)
      setResult(`基础fetch成功: ${response.status}`)
    } catch (error) {
      console.error('基础fetch失败:', error)
      setResult(`基础fetch失败: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>API 测试页面</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={testApi} disabled={loading}>
              测试 /api/email-sync/schedule
            </Button>
            <Button onClick={testFetch} disabled={loading} variant="outline">
              测试基础 fetch
            </Button>
          </div>
          
          <div className="bg-gray-100 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">测试结果:</h3>
            <pre className="whitespace-pre-wrap text-sm">
              {result || "点击按钮开始测试"}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
