import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT, extractTokenFromRequest } from '@/lib/jwt'
import { isTokenBlacklisted } from '@/lib/token-blacklist'

/**
 * 定时邮件同步任务管理
 * 需要身份验证才能访问
 */

interface SyncSchedule {
  isRunning: boolean
  interval: number // 分钟
  lastRun: string | null
  nextRun: string | null
}

// 全局同步状态
export let syncSchedule: SyncSchedule = {
  isRunning: false,
  interval: 5, // 默认5分钟
  lastRun: null,
  nextRun: null
}

let syncInterval: NodeJS.Timeout | null = null

/**
 * 验证用户身份
 */
async function validateAuthentication(request: NextRequest): Promise<boolean> {
  try {
    // 检查是否为内部请求（来自定时任务或系统内部调用）
    const internalRequest = request.headers.get('X-Internal-Request')
    if (internalRequest === 'true') {
      return true // 内部请求直接通过
    }

    // 从Authorization header获取token
    const authToken = extractTokenFromRequest(request)
    if (!authToken) {
      // 从cookie获取token
      const cookieToken = request.cookies.get('auth-token')?.value
      if (!cookieToken) {
        return false
      }

      // 验证JWT token
      const payload = await verifyJWT(cookieToken)
      if (!payload) {
        return false
      }

      // 检查token是否在黑名单中
      if (isTokenBlacklisted(cookieToken)) {
        return false
      }

      return true
    }

    // 验证JWT token
    const payload = await verifyJWT(authToken)
    if (!payload) {
      return false
    }

    // 检查token是否在黑名单中
    if (isTokenBlacklisted(authToken)) {
      return false
    }

    return true
  } catch (error) {
    console.error('身份验证失败:', error)
    return false
  }
}

/**
 * 检查是否有活跃用户会话
 * 通过检查活跃会话管理器来判断
 */
async function hasActiveUserSession(): Promise<boolean> {
  try {
    // 检查活跃会话数量
    const { hasActiveSessions, getActiveSessionCount } = await import('@/lib/session-manager')
    const hasActive = hasActiveSessions()
    const count = getActiveSessionCount()

    console.log(`当前活跃会话数量: ${count}`)

    return hasActive
  } catch (error) {
    console.error('检查用户会话失败:', error)
    // 出错时保守处理，假设没有活跃用户
    return false
  }
}

/**
 * 执行同步任务
 * 直接调用邮件同步逻辑，避免HTTP请求问题
 */
async function executeSyncTask() {
  try {
    console.log('开始执行定时邮件同步...')

    // 检查是否有活跃用户会话
    const hasActiveUser = await hasActiveUserSession()
    if (!hasActiveUser) {
      console.log('没有活跃用户会话，停止定时同步')
      stopSyncSchedule()
      return
    }

    syncSchedule.lastRun = new Date().toISOString()

    // 直接调用邮件同步逻辑，避免HTTP请求
    const { EmailSynchronizer } = await import('@/app/api/email-sync/route')
    const emailSynchronizer = new EmailSynchronizer()

    const result = await emailSynchronizer.syncAllEmails()

    if (result.success) {
      console.log(`定时同步完成，同步了 ${result.totalSynced} 条新邮件`)
    } else {
      console.error('定时同步失败:', result.error)
    }

    // 计算下次运行时间
    syncSchedule.nextRun = new Date(Date.now() + syncSchedule.interval * 60 * 1000).toISOString()

  } catch (error) {
    console.error('定时同步任务执行失败:', error)
  }
}

/**
 * 启动定时同步
 */
export function startSyncSchedule(intervalMinutes: number = 5) {
  if (syncInterval) {
    clearInterval(syncInterval)
  }

  syncSchedule.interval = intervalMinutes
  syncSchedule.isRunning = true
  syncSchedule.nextRun = new Date(Date.now() + intervalMinutes * 60 * 1000).toISOString()

  // 立即执行一次
  executeSyncTask()

  // 设置定时器
  syncInterval = setInterval(executeSyncTask, intervalMinutes * 60 * 1000)
  
  console.log(`邮件同步定时器已启动，间隔 ${intervalMinutes} 分钟`)
}

/**
 * 停止定时同步
 */
export function stopSyncSchedule() {
  if (syncInterval) {
    clearInterval(syncInterval)
    syncInterval = null
  }

  syncSchedule.isRunning = false
  syncSchedule.nextRun = null
  
  console.log('邮件同步定时器已停止')
}

/**
 * 启动/停止定时同步
 * 需要身份验证
 */
export async function POST(request: NextRequest) {
  try {
    // 验证身份
    const isAuthenticated = await validateAuthentication(request)
    if (!isAuthenticated) {
      return NextResponse.json({
        success: false,
        error: '需要登录访问',
        code: 'UNAUTHORIZED'
      }, { status: 401 })
    }

    const body = await request.json()
    const { action, interval } = body

    switch (action) {
      case 'start':
        const syncInterval = interval || 5
        if (syncInterval < 1 || syncInterval > 60) {
          return NextResponse.json({
            success: false,
            error: '同步间隔必须在1-60分钟之间'
          }, { status: 400 })
        }
        
        startSyncSchedule(syncInterval)
        
        return NextResponse.json({
          success: true,
          message: `定时同步已启动，间隔 ${syncInterval} 分钟`,
          data: syncSchedule
        })

      case 'stop':
        stopSyncSchedule()
        
        return NextResponse.json({
          success: true,
          message: '定时同步已停止',
          data: syncSchedule
        })

      case 'restart':
        stopSyncSchedule()
        const restartInterval = interval || syncSchedule.interval
        startSyncSchedule(restartInterval)
        
        return NextResponse.json({
          success: true,
          message: `定时同步已重启，间隔 ${restartInterval} 分钟`,
          data: syncSchedule
        })

      default:
        return NextResponse.json({
          success: false,
          error: '无效的操作类型'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('定时同步管理失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '定时同步管理失败'
    }, { status: 500 })
  }
}

/**
 * 获取定时同步状态
 * 需要身份验证
 */
export async function GET(request: NextRequest) {
  try {
    // 验证身份
    const isAuthenticated = await validateAuthentication(request)
    if (!isAuthenticated) {
      return NextResponse.json({
        success: false,
        error: '需要登录访问',
        code: 'UNAUTHORIZED'
      }, { status: 401 })
    }

    return NextResponse.json({
      success: true,
      data: {
        ...syncSchedule,
        status: syncSchedule.isRunning ? 'running' : 'stopped',
        intervalText: `${syncSchedule.interval} 分钟`,
        lastRunText: syncSchedule.lastRun 
          ? new Date(syncSchedule.lastRun).toLocaleString('zh-CN')
          : '从未运行',
        nextRunText: syncSchedule.nextRun 
          ? new Date(syncSchedule.nextRun).toLocaleString('zh-CN')
          : '未计划'
      }
    })
  } catch (error) {
    console.error('获取定时同步状态失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取定时同步状态失败'
    }, { status: 500 })
  }
}
