"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Search,
  Mail,
  User,
  MapPin,
  Calendar,
  CheckSquare,
  Square,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Filter
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface EmailData {
  id: string
  full_name: string
  email: string
  created_at: string
  city?: string
  state?: string
}

interface ImportResult {
  total: number
  success: number
  skipped: number
  failed: number
  details: {
    imported: number
    alreadyExists: number
    errors: number
  }
}

interface ImportEmailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportComplete?: (result: ImportResult) => void
}

export default function ImportEmailsDialog({ 
  open, 
  onOpenChange, 
  onImportComplete 
}: ImportEmailsDialogProps) {
  const [emails, setEmails] = useState<EmailData[]>([])
  const [loading, setLoading] = useState(false)
  const [importing, setImporting] = useState(false)
  const [search, setSearch] = useState("")
  const [selectedEmails, setSelectedEmails] = useState<Set<string>>(new Set())
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [total, setTotal] = useState(0)
  const [excludeExisting, setExcludeExisting] = useState(true)
  const { toast } = useToast()

  const fetchEmails = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20",
        search,
        excludeExisting: excludeExisting.toString()
      })

      const response = await fetch(`/api/import/emails-from-data?${params}`)
      const result = await response.json()

      if (result.success) {
        setEmails(result.data)
        setTotal(result.pagination.total)
        setTotalPages(result.pagination.totalPages)
      } else {
        toast({
          title: "获取邮件列表失败",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "获取邮件列表失败",
        description: "网络错误",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleImport = async () => {
    if (selectedEmails.size === 0) {
      toast({
        title: "请选择邮件",
        description: "请至少选择一个邮件进行导入",
        variant: "destructive",
      })
      return
    }

    setImporting(true)
    try {
      const response = await fetch('/api/import/emails-from-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailIds: Array.from(selectedEmails),
          importType: 'imported'
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "导入成功",
          description: result.message,
        })
        
        if (onImportComplete) {
          onImportComplete(result.data)
        }
        
        // 重新获取邮件列表（排除已导入的）
        setSelectedEmails(new Set())
        await fetchEmails()
      } else {
        toast({
          title: "导入失败",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "导入失败",
        description: "网络错误",
        variant: "destructive",
      })
    } finally {
      setImporting(false)
    }
  }

  const toggleSelectAll = () => {
    if (selectedEmails.size === emails.length && emails.length > 0) {
      setSelectedEmails(new Set())
    } else {
      setSelectedEmails(new Set(emails.map(email => email.id)))
    }
  }

  const toggleEmailSelection = (emailId: string) => {
    const newSelected = new Set(selectedEmails)
    if (newSelected.has(emailId)) {
      newSelected.delete(emailId)
    } else {
      newSelected.add(emailId)
    }
    setSelectedEmails(newSelected)
  }

  const handleSearch = (value: string) => {
    setSearch(value)
    setCurrentPage(1)
    setSelectedEmails(new Set())
  }

  useEffect(() => {
    if (open) {
      fetchEmails()
    }
  }, [open, currentPage, search, excludeExisting])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            从数据中心导入邮件
          </DialogTitle>
          <DialogDescription>
            选择要导入到邮件管理系统的邮件地址
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搜索和筛选 */}
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索邮件地址或姓名..."
                value={search}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant={excludeExisting ? "default" : "outline"}
              onClick={() => setExcludeExisting(!excludeExisting)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              {excludeExisting ? "排除已存在" : "显示全部"}
            </Button>
            <Button onClick={fetchEmails} disabled={loading}>
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>

          {/* 统计信息 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Badge variant="outline">
                共 {total} 个邮件
              </Badge>
              {selectedEmails.size > 0 && (
                <Badge variant="secondary">
                  已选择 {selectedEmails.size} 个
                </Badge>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={toggleSelectAll}
              disabled={emails.length === 0}
            >
              {selectedEmails.size === emails.length && emails.length > 0 ? (
                <>
                  <CheckSquare className="h-4 w-4 mr-1" />
                  取消全选
                </>
              ) : (
                <>
                  <Square className="h-4 w-4 mr-1" />
                  全选当页
                </>
              )}
            </Button>
          </div>

          {/* 邮件列表 */}
          <div className="border rounded-lg max-h-96 overflow-y-auto">
            {loading ? (
              <div className="p-8 text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500">加载中...</p>
              </div>
            ) : emails.length === 0 ? (
              <div className="p-8 text-center">
                <Mail className="h-8 w-8 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500">
                  {search ? "没有找到匹配的邮件" : "没有可导入的邮件"}
                </p>
              </div>
            ) : (
              <div className="divide-y">
                {emails.map((email) => (
                  <div
                    key={email.id}
                    className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                      selectedEmails.has(email.id) ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                    }`}
                    onClick={() => toggleEmailSelection(email.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0">
                        {selectedEmails.has(email.id) ? (
                          <CheckSquare className="h-5 w-5 text-blue-600" />
                        ) : (
                          <Square className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Mail className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-gray-900 truncate">
                            {email.email}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            <span className="truncate">{email.full_name}</span>
                          </div>
                          {email.city && email.state && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              <span className="truncate">{email.city}, {email.state}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(email.created_at)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                第 {currentPage} / {totalPages} 页
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1 || loading}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages || loading}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button
            onClick={handleImport}
            disabled={selectedEmails.size === 0 || importing}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {importing ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                导入中...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                导入 ({selectedEmails.size})
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
