"use client"

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuthState } from '@/hooks/use-auth'
import { LoadingSpinner } from './loading-spinner'
import { Navigation } from './navigation'

interface LayoutWrapperProps {
  children: React.ReactNode
}

/**
 * 布局包装组件
 * 处理身份验证状态和页面访问控制
 */
export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, loading } = useAuthState()
  const [isInitialized, setIsInitialized] = useState(false)

  // 公开页面路径（不需要登录）
  const publicPaths = ['/login']

  // 检查是否为公开页面
  const isPublicPath = publicPaths.includes(pathname)

  useEffect(() => {
    // 等待身份验证状态加载完成
    if (!loading) {
      setIsInitialized(true)
      
      // 如果是公开页面，不需要检查登录状态
      if (isPublicPath) {
        return
      }
      
      // 如果未登录且不是公开页面，重定向到登录页面
      if (!isAuthenticated) {
        router.replace(`/login?redirect=${encodeURIComponent(pathname)}`)
        return
      }
      
      // 如果已登录且访问登录页面，重定向到首页
      if (isAuthenticated && pathname === '/login') {
        router.replace('/')
        return
      }
    }
  }, [isAuthenticated, loading, pathname, router, isPublicPath])

  // 显示加载状态
  if (!isInitialized || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            正在加载...
          </p>
        </div>
      </div>
    )
  }

  // 如果未登录且不是公开页面，显示加载状态（等待重定向）
  if (!isAuthenticated && !isPublicPath) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            正在跳转到登录页面...
          </p>
        </div>
      </div>
    )
  }

  // 如果已登录且访问登录页面，显示加载状态（等待重定向）
  if (isAuthenticated && pathname === '/login') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            正在跳转到首页...
          </p>
        </div>
      </div>
    )
  }

  // 正常渲染页面内容
  // 如果是登录页面，直接渲染子组件（不显示导航栏）
  if (isPublicPath) {
    return <>{children}</>
  }

  // 其他页面显示完整布局（包含导航栏）
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation />
      <main className="flex-1">
        {children}
      </main>
    </div>
  )
}
