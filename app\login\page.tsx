"use client"

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { LoginForm } from '@/components/login-form'
import { LoadingSpinner } from '@/components/loading-spinner'

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // 检查登录状态
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // 先检查本地存储的token
        const token = localStorage.getItem('auth-token')
        if (!token) {
          setLoading(false)
          return
        }

        // 使用AbortController设置超时
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 3000) // 3秒超时

        const response = await fetch('/api/auth/status', {
          signal: controller.signal,
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        clearTimeout(timeoutId)
        const data = await response.json()

        if (data.success && data.data.isAuthenticated) {
          setIsAuthenticated(true)
          // 如果已经登录，重定向到目标页面或首页
          const redirectTo = searchParams.get('redirect') || '/'
          router.replace(redirectTo)
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.warn('登录状态检查超时，跳过验证')
        } else {
          console.error('检查登录状态失败:', error)
        }
      } finally {
        setLoading(false)
      }
    }

    checkAuthStatus()
  }, [router, searchParams])

  // 登录成功后的处理
  const handleLoginSuccess = () => {
    const redirectTo = searchParams.get('redirect') || '/'
    router.push(redirectTo)
  }

  // 如果正在加载或已经登录，显示加载状态
  if (loading || isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" text="检查登录状态..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 系统标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          美国地址生成器
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          管理系统登录
        </p>
      </div>

      {/* 登录表单 */}
      <LoginForm onSuccess={handleLoginSuccess} />

      {/* 版本信息 */}
      <div className="text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          版本 1.0.0 | © 2025 美国地址生成器
        </p>
      </div>
    </div>
  )
}
