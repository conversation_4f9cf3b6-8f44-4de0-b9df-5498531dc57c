#!/usr/bin/env node

/**
 * 身份验证系统测试脚本
 * 全面测试身份验证系统的各种功能和场景
 */

const https = require('https')
const http = require('http')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 测试配置
const BASE_URL = 'http://localhost:3002'
const TEST_CREDENTIALS = {
  valid: { username: 'admin', password: 'admin123456' },
  invalid: { username: 'admin', password: 'wrongpassword' }
}

// HTTP请求工具
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const isHttps = urlObj.protocol === 'https:'
    const client = isHttps ? https : http
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    const req = client.request(requestOptions, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          })
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          })
        }
      })
    })

    req.on('error', reject)

    if (options.body) {
      req.write(JSON.stringify(options.body))
    }

    req.end()
  })
}

// 测试结果记录
class TestRunner {
  constructor() {
    this.tests = []
    this.passed = 0
    this.failed = 0
  }

  async runTest(name, testFn) {
    const startTime = Date.now()
    
    try {
      colorLog('blue', `🧪 ${name}`)
      const result = await testFn()
      const duration = Date.now() - startTime
      
      this.tests.push({
        name,
        success: true,
        duration,
        result
      })
      this.passed++
      colorLog('green', `✅ ${name} - 通过 (${duration}ms)`)
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      
      this.tests.push({
        name,
        success: false,
        duration,
        error: error.message
      })
      this.failed++
      colorLog('red', `❌ ${name} - 失败 (${duration}ms)`)
      colorLog('red', `   错误: ${error.message}`)
      
      throw error
    }
  }

  printSummary() {
    const total = this.passed + this.failed
    const successRate = Math.round((this.passed / total) * 100)
    
    console.log('\n' + '='.repeat(60))
    colorLog('cyan', '📊 测试结果汇总')
    console.log('='.repeat(60))
    
    colorLog('blue', `总测试数: ${total}`)
    colorLog('green', `通过: ${this.passed}`)
    colorLog('red', `失败: ${this.failed}`)
    colorLog('magenta', `成功率: ${successRate}%`)
    
    if (this.failed === 0) {
      colorLog('green', '\n🎉 所有测试通过！身份验证系统工作正常。')
    } else {
      colorLog('yellow', '\n⚠️ 部分测试失败，请检查上述错误信息。')
    }
  }
}

// 测试函数
async function testInvalidLogin() {
  const response = await makeRequest(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    body: TEST_CREDENTIALS.invalid
  })

  if (response.status !== 401) {
    throw new Error(`期望状态码401，实际${response.status}`)
  }

  if (response.data.success) {
    throw new Error('错误凭据不应该登录成功')
  }

  return response.data
}

async function testValidLogin() {
  const response = await makeRequest(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    body: TEST_CREDENTIALS.valid
  })

  if (response.status !== 200) {
    throw new Error(`期望状态码200，实际${response.status}`)
  }

  if (!response.data.success || !response.data.token) {
    throw new Error('登录响应格式错误')
  }

  return response.data
}

async function testUnauthorizedAccess() {
  const response = await makeRequest(`${BASE_URL}/api/saved-data`)

  if (response.status !== 401) {
    throw new Error(`期望状态码401，实际${response.status}`)
  }

  if (response.data.code !== 'UNAUTHORIZED') {
    throw new Error('未授权访问应该返回UNAUTHORIZED错误码')
  }

  return response.data
}

async function testAuthorizedAccess(token) {
  const response = await makeRequest(`${BASE_URL}/api/saved-data`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })

  if (response.status !== 200) {
    throw new Error(`期望状态码200，实际${response.status}`)
  }

  return response.data
}

async function testAuthStatus(token) {
  const response = await makeRequest(`${BASE_URL}/api/auth/status`, {
    headers: token ? { 'Authorization': `Bearer ${token}` } : {}
  })

  if (response.status !== 200) {
    throw new Error(`期望状态码200，实际${response.status}`)
  }

  return response.data
}

async function testEmailSyncAccess(token) {
  const response = await makeRequest(`${BASE_URL}/api/email-sync/schedule`, {
    headers: token ? { 'Authorization': `Bearer ${token}` } : {}
  })

  if (token) {
    if (response.status !== 200) {
      throw new Error(`有token时期望状态码200，实际${response.status}`)
    }
  } else {
    if (response.status !== 401) {
      throw new Error(`无token时期望状态码401，实际${response.status}`)
    }
  }

  return response.data
}

async function testLogout(token) {
  const response = await makeRequest(`${BASE_URL}/api/auth/logout`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })

  if (response.status !== 200) {
    throw new Error(`期望状态码200，实际${response.status}`)
  }

  if (!response.data.success) {
    throw new Error('登出响应格式错误')
  }

  return response.data
}

// 主测试流程
async function runAllTests() {
  const runner = new TestRunner()
  
  colorLog('cyan', '🚀 开始身份验证系统测试')
  console.log('='.repeat(60))

  try {
    // 1. 测试无效登录
    await runner.runTest('无效登录测试', testInvalidLogin)

    // 2. 测试未授权访问
    await runner.runTest('未授权API访问测试', testUnauthorizedAccess)
    await runner.runTest('未授权邮件同步访问测试', () => testEmailSyncAccess())

    // 3. 测试有效登录
    const loginResult = await runner.runTest('有效登录测试', testValidLogin)
    const token = loginResult.token

    // 4. 测试授权状态
    await runner.runTest('身份验证状态测试', () => testAuthStatus(token))

    // 5. 测试授权访问
    await runner.runTest('授权API访问测试', () => testAuthorizedAccess(token))
    await runner.runTest('授权邮件同步访问测试', () => testEmailSyncAccess(token))

    // 6. 测试登出
    await runner.runTest('登出功能测试', () => testLogout(token))

    // 7. 测试登出后状态
    await runner.runTest('登出后未授权访问测试', testUnauthorizedAccess)

  } catch (error) {
    // 某些测试失败是预期的，继续执行其他测试
  }

  runner.printSummary()
  
  // 返回测试是否全部通过
  return runner.failed === 0
}

// 运行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      colorLog('red', `测试执行失败: ${error.message}`)
      process.exit(1)
    })
}

module.exports = { runAllTests }
