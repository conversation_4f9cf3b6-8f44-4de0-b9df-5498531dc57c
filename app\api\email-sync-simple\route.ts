import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { verifyJWT, extractTokenFromRequest } from '@/lib/jwt'
import { isTokenBlacklisted } from '@/lib/token-blacklist'

/**
 * 简化版邮件同步器 - 使用现有的邮件测试API
 * 需要身份验证才能访问
 */

class SimpleEmailSynchronizer {
  /**
   * 同步单个邮箱的邮件
   */
  async syncEmailMessages(emailAddress: string): Promise<{
    success: boolean
    newMessages: number
    deletedMessages?: number
    error?: string
  }> {
    try {
      console.log(`开始同步邮箱: ${emailAddress}`)
      
      // 检查邮箱是否存在于数据库中
      const { data: emailRecord, error: emailError } = await supabase
        .from('emails')
        .select('id')
        .eq('address', emailAddress)
        .single()

      if (emailError || !emailRecord) {
        console.log(`邮箱 ${emailAddress} 不存在于数据库中`)
        return { success: false, newMessages: 0, deletedMessages: 0, error: '邮箱不存在' }
      }

      // 连接到真实IMAP服务器获取新邮件
      console.log('连接到真实IMAP服务器获取邮件...')

      let emails = []
      try {
        // 使用SimpleIMAP连接真实邮箱获取转发的邮件
        const { SimpleIMAP } = await import('@/lib/simple-imap')
        const imap = new SimpleIMAP()

        // 获取该临时邮箱的所有邮件
        const imapResult = await imap.getVerificationCode(emailAddress)

        if (!imapResult.emails || imapResult.emails.length === 0) {
          console.log(`IMAP中未找到 ${emailAddress} 的新邮件`)
          // 即使IMAP中没有邮件，也要检查是否需要删除数据库中的邮件
          const { data: existingMessages } = await supabase
            .from('messages')
            .select('id, subject, from_address, text_content')
            .eq('email_id', emailRecord.id)

          let deletedCount = 0
          if (existingMessages && existingMessages.length > 0) {
            console.log(`IMAP中无邮件，但数据库中有 ${existingMessages.length} 封邮件，准备删除`)

            for (const msg of existingMessages) {
              const { error: deleteError } = await supabase
                .from('messages')
                .delete()
                .eq('id', msg.id)

              if (deleteError) {
                console.error('删除邮件失败:', deleteError)
              } else {
                deletedCount++
                console.log(`成功删除邮件: ${msg.subject}`)
              }
            }

            // 更新邮箱的未读计数
            await this.updateEmailUnreadCount(emailRecord.id)
          }

          return { success: true, newMessages: 0, deletedMessages: deletedCount }
        }

        console.log(`从IMAP获取到 ${imapResult.emails.length} 封邮件`)
        emails = imapResult.emails

      } catch (imapError) {
        console.error('IMAP连接失败:', imapError)
        // IMAP失败时，从数据库获取现有邮件作为备用
        const { data: existingMessages } = await supabase
          .from('messages')
          .select('*')
          .eq('email_id', emailRecord.id)
          .order('received_at', { ascending: false })
          .limit(10)

        console.log(`IMAP失败，从数据库获取 ${existingMessages?.length || 0} 封现有邮件`)
        return { success: true, newMessages: 0, deletedMessages: 0 }
      }

      // 获取数据库中现有的邮件，用于对比删除
      const { data: existingMessages, error: existingError } = await supabase
        .from('messages')
        .select('id, subject, from_address, text_content')
        .eq('email_id', emailRecord.id)

      if (existingError) {
        console.error('获取现有邮件失败:', existingError)
      }

      // 创建数据库邮件的映射
      const existingMessagesMap = new Map()
      if (existingMessages) {
        existingMessages.forEach(msg => {
          const key = `${msg.subject}-${msg.from_address}-${msg.text_content}`
          existingMessagesMap.set(key, msg.id)
        })
      }

      // 创建IMAP邮件的映射
      const imapMessagesSet = new Set()
      emails.forEach(email => {
        const textContent = email.text || email.content || ''
        const key = `${email.subject || '无主题'}-${email.from || '未知发件人'}-${textContent}`
        imapMessagesSet.add(key)
      })

      let newMessagesCount = 0
      let deletedMessagesCount = 0

      // 处理从IMAP获取的每封邮件
      for (const [index, email] of emails.entries()) {
        try {
          console.log(`处理第 ${index + 1}/${emails.length} 封邮件: ${email.subject || '无主题'}`)

          // 检查邮件是否已存在（基于邮件内容的唯一性）
          const textContent = email.text || email.content || ''
          const { data: existingMessage } = await supabase
            .from('messages')
            .select('id')
            .eq('email_id', emailRecord.id)
            .eq('subject', email.subject || '无主题')
            .eq('from_address', email.from || '未知发件人')
            .eq('text_content', textContent)
            .single()

          if (existingMessage) {
            console.log(`邮件已存在，跳过: ${email.subject} (内容: ${textContent.substring(0, 50)}...)`)
            continue
          }

          console.log(`邮件不存在，准备保存: ${email.subject} (内容: ${textContent.substring(0, 50)}...)`)

          // 提取验证码
          const verificationCode = this.extractVerificationCode(textContent)

          // 保存邮件到数据库
          const receivedAt = email.date ? new Date(email.date).getTime() : Date.now()
          const fromAddress = email.from || '未知发件人'
          const toAddress = email.to || emailAddress

          console.log(`邮件地址信息 - 发件人: ${fromAddress}, 收件人: ${toAddress}`)

          const { error: insertError } = await supabase
            .from('messages')
            .insert({
              email_id: emailRecord.id,
              from_address: fromAddress,
              to_address: toAddress,
              subject: email.subject || '无主题',
              text_content: textContent,
              html_content: email.html || '',
              received_at: receivedAt,
              is_read: false,
              has_verification_code: !!verificationCode,
              verification_code: verificationCode
            })

          if (insertError) {
            console.error('保存邮件失败:', insertError)
          } else {
            newMessagesCount++
            console.log(`成功保存新邮件: ${email.subject}`)
          }
        } catch (error) {
          console.error('处理单封邮件时出错:', error)
        }
      }

      // 删除在IMAP中不存在但在数据库中存在的邮件
      console.log('检查需要删除的邮件...')
      for (const [key, messageId] of existingMessagesMap.entries()) {
        if (!imapMessagesSet.has(key)) {
          console.log(`邮件在IMAP中已删除，从数据库删除: ${key.split('-')[0]}`)

          const { error: deleteError } = await supabase
            .from('messages')
            .delete()
            .eq('id', messageId)

          if (deleteError) {
            console.error('删除邮件失败:', deleteError)
          } else {
            deletedMessagesCount++
            console.log(`成功删除邮件: ${messageId}`)
          }
        }
      }

      console.log(`邮件同步完成，新增 ${newMessagesCount} 封邮件，删除 ${deletedMessagesCount} 封邮件`)

      // 更新邮箱的未读计数
      if (newMessagesCount > 0 || deletedMessagesCount > 0) {
        await this.updateEmailUnreadCount(emailRecord.id)
      }

      console.log(`邮箱 ${emailAddress} 同步完成，新增 ${newMessagesCount} 条邮件，删除 ${deletedMessagesCount} 条邮件`)
      return { success: true, newMessages: newMessagesCount, deletedMessages: deletedMessagesCount }

    } catch (error) {
      console.error(`同步邮箱 ${emailAddress} 失败:`, error)
      return {
        success: false,
        newMessages: 0,
        deletedMessages: 0,
        error: error instanceof Error ? error.message : '同步失败'
      }
    }
  }

  /**
   * 同步所有邮箱的邮件
   */
  async syncAllEmails(): Promise<{
    success: boolean
    totalSynced: number
    results: Array<{ email: string; newMessages: number; success: boolean }>
  }> {
    try {
      // 获取所有活跃邮箱
      const { data: emails, error } = await supabase
        .from('emails')
        .select('address')
        .eq('is_active', true)

      if (error) {
        throw new Error('获取邮箱列表失败: ' + error.message)
      }

      if (!emails || emails.length === 0) {
        return { success: true, totalSynced: 0, results: [] }
      }

      const results = []
      let totalSynced = 0

      // 逐个同步邮箱（避免并发过多）
      for (const email of emails) {
        console.log(`开始同步邮箱: ${email.address}`)
        const result = await this.syncEmailMessages(email.address)
        
        results.push({
          email: email.address,
          newMessages: result.newMessages,
          success: result.success
        })

        if (result.success) {
          totalSynced += result.newMessages
        }

        // 每个邮箱之间间隔2秒，避免API请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 2000))
      }

      return { success: true, totalSynced, results }
    } catch (error) {
      console.error('批量同步失败:', error)
      return { 
        success: false, 
        totalSynced: 0, 
        results: []
      }
    }
  }

  /**
   * 提取验证码
   */
  private extractVerificationCode(text: string): string | null {
    const patterns = [
      /验证码[：:\s]*(\d{4,8})/i,
      /verification code[：:\s]*(\d{4,8})/i,
      /code[：:\s]*(\d{4,8})/i,
      /(\d{6})/g // 6位数字
    ]

    for (const pattern of patterns) {
      const match = text.match(pattern)
      if (match) {
        return match[1] || match[0]
      }
    }

    return null
  }

  /**
   * 更新邮箱未读计数
   */
  private async updateEmailUnreadCount(emailId: string) {
    try {
      const { count } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('email_id', emailId)
        .eq('is_read', false)

      await supabase
        .from('emails')
        .update({
          unread_count: count || 0,
          has_new_messages: (count || 0) > 0,
          updated_at: new Date().toISOString()
        })
        .eq('id', emailId)
    } catch (error) {
      console.error('更新未读计数失败:', error)
    }
  }
}

const emailSynchronizer = new SimpleEmailSynchronizer()

/**
 * 验证用户身份
 */
async function validateAuthentication(request: NextRequest): Promise<boolean> {
  try {
    // 从Authorization header获取token
    const authToken = extractTokenFromRequest(request)
    if (!authToken) {
      // 从cookie获取token
      const cookieToken = request.cookies.get('auth-token')?.value
      if (!cookieToken) {
        return false
      }

      // 验证JWT token
      const payload = await verifyJWT(cookieToken)
      if (!payload) {
        return false
      }

      // 检查token是否在黑名单中
      if (isTokenBlacklisted(cookieToken)) {
        return false
      }

      return true
    }

    // 验证JWT token
    const payload = await verifyJWT(authToken)
    if (!payload) {
      return false
    }

    // 检查token是否在黑名单中
    if (isTokenBlacklisted(authToken)) {
      return false
    }

    return true
  } catch (error) {
    console.error('身份验证失败:', error)
    return false
  }
}

/**
 * 邮件同步API
 * 需要身份验证才能访问
 */
export async function POST(request: NextRequest) {
  try {
    // 验证身份
    const isAuthenticated = await validateAuthentication(request)
    if (!isAuthenticated) {
      return NextResponse.json({
        success: false,
        error: '需要登录访问',
        code: 'UNAUTHORIZED'
      }, { status: 401 })
    }

    const body = await request.json()
    const { emailAddress, syncType = 'single' } = body

    if (syncType === 'single') {
      if (!emailAddress) {
        return NextResponse.json({
          success: false,
          error: '邮箱地址不能为空'
        }, { status: 400 })
      }

      const result = await emailSynchronizer.syncEmailMessages(emailAddress)
      
      return NextResponse.json({
        success: result.success,
        data: {
          emailAddress,
          newMessages: result.newMessages
        },
        error: result.error
      })
    } else if (syncType === 'all') {
      const result = await emailSynchronizer.syncAllEmails()
      
      return NextResponse.json({
        success: result.success,
        data: {
          totalSynced: result.totalSynced,
          results: result.results
        }
      })
    } else {
      return NextResponse.json({
        success: false,
        error: '无效的同步类型'
      }, { status: 400 })
    }
  } catch (error) {
    console.error('邮件同步失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '邮件同步失败'
    }, { status: 500 })
  }
}

/**
 * 获取同步状态
 * 需要身份验证才能访问
 */
export async function GET(request: NextRequest) {
  try {
    // 验证身份
    const isAuthenticated = await validateAuthentication(request)
    if (!isAuthenticated) {
      return NextResponse.json({
        success: false,
        error: '需要登录访问',
        code: 'UNAUTHORIZED'
      }, { status: 401 })
    }

    // 获取最近同步的统计信息
    const { data: recentMessages, error } = await supabase
      .from('messages')
      .select('created_at, email_id')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // 最近24小时
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error('获取同步状态失败: ' + error.message)
    }

    const last24Hours = recentMessages?.length || 0
    const lastSyncTime = recentMessages?.[0]?.created_at || null

    return NextResponse.json({
      success: true,
      data: {
        last24Hours,
        lastSyncTime,
        status: 'ready'
      }
    })
  } catch (error) {
    console.error('获取同步状态失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取同步状态失败'
    }, { status: 500 })
  }
}
