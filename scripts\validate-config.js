#!/usr/bin/env node

/**
 * 配置验证脚本
 * 用于验证环境变量配置是否正确
 */

const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 加载环境变量
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local')
  
  if (!fs.existsSync(envPath)) {
    colorLog('red', '❌ .env.local 文件不存在')
    colorLog('yellow', '💡 请复制 .env.local.example 为 .env.local 并配置相应的环境变量')
    return null
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8')
  const env = {}
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim()
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=')
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim()
      }
    }
  })
  
  return env
}

// 验证必需的环境变量
function validateRequiredVars(env) {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'ADMIN_USERNAME',
    'ADMIN_PASSWORD',
    'JWT_SECRET'
  ]
  
  const errors = []
  const warnings = []
  
  requiredVars.forEach(varName => {
    if (!env[varName]) {
      errors.push(`缺少必需的环境变量: ${varName}`)
    }
  })
  
  // 验证具体值
  if (env.ADMIN_USERNAME) {
    if (env.ADMIN_USERNAME.length < 3) {
      errors.push('ADMIN_USERNAME 至少需要3个字符')
    }
    if (env.ADMIN_USERNAME === 'admin') {
      warnings.push('建议使用更复杂的管理员用户名，避免使用默认的 "admin"')
    }
  }
  
  if (env.ADMIN_PASSWORD) {
    if (env.ADMIN_PASSWORD.length < 6) {
      errors.push('ADMIN_PASSWORD 至少需要6个字符')
    }
    if (env.ADMIN_PASSWORD === 'your_secure_password_here') {
      errors.push('请设置真实的管理员密码，不要使用默认值')
    }
    
    // 检查密码强度
    const password = env.ADMIN_PASSWORD
    const hasUpper = /[A-Z]/.test(password)
    const hasLower = /[a-z]/.test(password)
    const hasNumber = /\d/.test(password)
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password)
    
    if (!hasUpper || !hasLower || !hasNumber) {
      warnings.push('建议密码包含大写字母、小写字母和数字')
    }
    
    if (!hasSpecial) {
      warnings.push('建议密码包含特殊字符以提高安全性')
    }
  }
  
  if (env.JWT_SECRET) {
    if (env.JWT_SECRET.length < 32) {
      errors.push('JWT_SECRET 至少需要32个字符')
    }
    if (env.JWT_SECRET === 'your_jwt_secret_key_at_least_32_characters_long') {
      errors.push('请设置真实的JWT密钥，不要使用默认值')
    }
  }
  
  if (env.NEXT_PUBLIC_SUPABASE_URL) {
    if (!env.NEXT_PUBLIC_SUPABASE_URL.startsWith('https://')) {
      errors.push('NEXT_PUBLIC_SUPABASE_URL 必须以 https:// 开头')
    }
    if (env.NEXT_PUBLIC_SUPABASE_URL === 'your_supabase_project_url') {
      errors.push('请设置真实的Supabase项目URL')
    }
  }
  
  if (env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    if (env.NEXT_PUBLIC_SUPABASE_ANON_KEY.length < 20) {
      errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY 格式无效，长度至少20个字符')
    }
    if (env.NEXT_PUBLIC_SUPABASE_ANON_KEY === 'your_supabase_anon_key') {
      errors.push('请设置真实的Supabase匿名密钥')
    }
  }
  
  return { errors, warnings }
}

// 验证可选配置
function validateOptionalVars(env) {
  const warnings = []
  
  // 会话配置
  const sessionTimeout = parseInt(env.SESSION_TIMEOUT_HOURS || '24')
  if (sessionTimeout < 1) {
    warnings.push('SESSION_TIMEOUT_HOURS 过短，建议至少1小时')
  }
  if (sessionTimeout > 168) { // 7天
    warnings.push('SESSION_TIMEOUT_HOURS 过长，可能存在安全风险')
  }
  
  // 邮件配置
  if (env.IMAP_SERVER && env.IMAP_USER && !env.IMAP_PASSWORD) {
    warnings.push('IMAP服务器和用户已配置，但缺少密码')
  }
  
  // 生产环境配置
  if (env.NODE_ENV === 'production') {
    if (env.FORCE_HTTPS !== 'true') {
      warnings.push('生产环境建议启用HTTPS强制重定向 (FORCE_HTTPS=true)')
    }
    
    if (env.DEBUG_MODE === 'true') {
      warnings.push('生产环境不应启用调试模式 (DEBUG_MODE=false)')
    }
    
    if (!env.NEXTAUTH_URL) {
      warnings.push('生产环境建议设置NEXTAUTH_URL')
    }
  }
  
  return { warnings }
}

// 生成配置建议
function generateSuggestions() {
  const suggestions = [
    '💡 配置建议:',
    '',
    '1. 生成强密码:',
    '   node -e "console.log(require(\'crypto\').randomBytes(16).toString(\'hex\'))"',
    '',
    '2. 生成JWT密钥:',
    '   openssl rand -base64 32',
    '   # 或者',
    '   node -e "console.log(require(\'crypto\').randomBytes(32).toString(\'base64\'))"',
    '',
    '3. 检查Supabase配置:',
    '   - 登录 https://supabase.com',
    '   - 进入项目设置 > API',
    '   - 复制项目URL和anon key',
    '',
    '4. 安全提示:',
    '   - 不要在版本控制中提交 .env.local 文件',
    '   - 定期更换密码和密钥',
    '   - 在生产环境中使用强密码',
    ''
  ]
  
  suggestions.forEach(line => colorLog('cyan', line))
}

// 主函数
function main() {
  colorLog('blue', '🔍 开始验证环境变量配置...')
  console.log('')
  
  const env = loadEnvFile()
  if (!env) {
    generateSuggestions()
    process.exit(1)
  }
  
  colorLog('green', '✅ .env.local 文件存在')
  
  // 验证必需变量
  const requiredValidation = validateRequiredVars(env)
  
  // 验证可选变量
  const optionalValidation = validateOptionalVars(env)
  
  const allErrors = requiredValidation.errors
  const allWarnings = [...requiredValidation.warnings, ...optionalValidation.warnings]
  
  // 输出结果
  console.log('')
  if (allErrors.length === 0) {
    colorLog('green', '✅ 所有必需的环境变量配置正确')
  } else {
    colorLog('red', '❌ 发现配置错误:')
    allErrors.forEach(error => colorLog('red', `  - ${error}`))
  }
  
  if (allWarnings.length > 0) {
    console.log('')
    colorLog('yellow', '⚠️ 配置警告:')
    allWarnings.forEach(warning => colorLog('yellow', `  - ${warning}`))
  }
  
  console.log('')
  if (allErrors.length === 0) {
    colorLog('green', '🎉 配置验证通过！可以启动应用了。')
    if (allWarnings.length > 0) {
      colorLog('yellow', '💡 建议处理上述警告以提高安全性。')
    }
  } else {
    colorLog('red', '🚫 请修复上述错误后再启动应用。')
    console.log('')
    generateSuggestions()
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  main()
}

module.exports = { loadEnvFile, validateRequiredVars, validateOptionalVars }
