'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AuthTester, TestSuite, TestResult } from '@/lib/auth-test'
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Shield,
  RefreshCw,
  AlertTriangle
} from 'lucide-react'

/**
 * 身份验证测试页面
 * 用于测试和验证身份验证系统的各种功能
 */
export default function AuthTestPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [testSuite, setTestSuite] = useState<TestSuite | null>(null)
  const [currentTest, setCurrentTest] = useState<string>('')

  /**
   * 运行测试套件
   */
  const runTests = async () => {
    setIsRunning(true)
    setTestSuite(null)
    setCurrentTest('初始化测试...')

    try {
      const tester = new AuthTester()
      
      // 监听测试进度（模拟）
      const progressTests = [
        '测试无效登录...',
        '测试未授权访问...',
        '测试有效登录...',
        '测试授权状态...',
        '测试授权访问...',
        '测试登出功能...',
        '测试登出后状态...'
      ]

      for (let i = 0; i < progressTests.length; i++) {
        setCurrentTest(progressTests[i])
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      const results = await tester.runFullTestSuite()
      setTestSuite(results)
      setCurrentTest('测试完成')
      
      console.log(tester.formatTestResults(results))
    } catch (error) {
      console.error('测试执行失败:', error)
      setCurrentTest('测试执行失败')
    } finally {
      setIsRunning(false)
    }
  }

  /**
   * 获取测试状态图标
   */
  const getTestIcon = (test: TestResult) => {
    if (test.success) {
      return <CheckCircle className="h-4 w-4 text-green-600" />
    } else {
      return <XCircle className="h-4 w-4 text-red-600" />
    }
  }

  /**
   * 获取测试状态颜色
   */
  const getTestBadgeVariant = (test: TestResult) => {
    return test.success ? 'default' : 'destructive'
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center gap-3">
        <Shield className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold">身份验证系统测试</h1>
          <p className="text-gray-600">全面测试身份验证系统的各项功能</p>
        </div>
      </div>

      {/* 测试控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            测试控制
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button
              onClick={runTests}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  运行中...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  开始测试
                </>
              )}
            </Button>

            {isRunning && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                {currentTest}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 测试结果概览 */}
      {testSuite && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              测试结果概览
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {testSuite.totalTests}
                </div>
                <div className="text-sm text-gray-600">总测试数</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {testSuite.passedTests}
                </div>
                <div className="text-sm text-gray-600">通过</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {testSuite.failedTests}
                </div>
                <div className="text-sm text-gray-600">失败</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">
                  {testSuite.duration}ms
                </div>
                <div className="text-sm text-gray-600">总耗时</div>
              </div>
            </div>

            {/* 成功率指示器 */}
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm mb-2">
                <span>成功率</span>
                <span>{Math.round((testSuite.passedTests / testSuite.totalTests) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-500"
                  style={{ 
                    width: `${(testSuite.passedTests / testSuite.totalTests) * 100}%` 
                  }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 详细测试结果 */}
      {testSuite && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              详细测试结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testSuite.tests.map((test, index) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getTestIcon(test)}
                    <div>
                      <div className="font-medium">{test.name}</div>
                      {!test.success && (
                        <div className="text-sm text-red-600">{test.message}</div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant={getTestBadgeVariant(test)}>
                      {test.success ? '通过' : '失败'}
                    </Badge>
                    <span className="text-sm text-gray-500">{test.duration}ms</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 测试说明 */}
      <Card>
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>测试覆盖范围：</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>无效登录凭据测试</li>
              <li>未授权API访问测试</li>
              <li>有效登录流程测试</li>
              <li>身份验证状态检查</li>
              <li>受保护资源访问测试</li>
              <li>邮件同步权限控制测试</li>
              <li>登出功能测试</li>
              <li>登出后状态清理验证</li>
            </ul>
            
            <p className="mt-4"><strong>注意事项：</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>测试使用配置的管理员凭据</li>
              <li>测试会验证API响应格式和状态码</li>
              <li>测试包含正面和负面场景</li>
              <li>所有测试都是非破坏性的</li>
              <li>测试覆盖登录、登出、会话管理等核心功能</li>
              <li>验证访问控制和权限管理</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
