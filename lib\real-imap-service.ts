/**
 * 真实IMAP邮件服务
 * 基于project2的Python实现，使用Node.js实现IMAP连接
 */

import { createConnection } from 'net'
import { createSecureContext, connect as tlsConnect } from 'tls'

interface IMAPConfig {
  host: string
  port: number
  user: string
  password: string
  secure: boolean
}

interface EmailMessage {
  id: string
  from: string
  to: string
  subject: string
  text: string
  date: string
  uid: number
}

export class RealIMAPService {
  private config: IMAPConfig
  private socket: any = null
  private isConnected = false
  private tagCounter = 0

  constructor() {
    this.config = {
      host: process.env.IMAP_SERVER || 'imap.qq.com',
      port: parseInt(process.env.IMAP_PORT || '993'),
      user: process.env.IMAP_USER || '',
      password: process.env.IMAP_PASS || '',
      secure: true
    }
  }

  /**
   * 生成IMAP命令标签
   */
  private getTag(): string {
    return `A${String(++this.tagCounter).padStart(3, '0')}`
  }

  /**
   * 发送IMAP命令
   */
  private async sendCommand(command: string): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'))
        return
      }

      const tag = this.getTag()
      const fullCommand = `${tag} ${command}\r\n`
      
      console.log(`发送IMAP命令: ${fullCommand.trim()}`)
      
      let response = ''
      const onData = (data: Buffer) => {
        response += data.toString()
        
        // 检查是否收到完整响应
        if (response.includes(`${tag} OK`) || response.includes(`${tag} NO`) || response.includes(`${tag} BAD`)) {
          if (this.socket && !this.socket.destroyed) {
            this.socket.removeListener('data', onData)
          }
          clearTimeout(timeout)
          console.log(`收到IMAP响应: ${response.trim()}`)
          resolve(response)
        }
      }

      this.socket.on('data', onData)
      this.socket.write(fullCommand)

      // 设置超时
      const timeout = setTimeout(() => {
        if (this.socket && !this.socket.destroyed) {
          this.socket.removeListener('data', onData)
        }
        reject(new Error('IMAP command timeout'))
      }, 30000)
    })
  }

  /**
   * 连接到IMAP服务器
   */
  async connect(): Promise<boolean> {
    try {
      console.log(`连接到IMAP服务器: ${this.config.host}:${this.config.port}`)
      
      return new Promise((resolve, reject) => {
        if (this.config.secure) {
          // 使用TLS连接
          this.socket = tlsConnect({
            host: this.config.host,
            port: this.config.port,
            rejectUnauthorized: false
          }, () => {
            console.log('TLS连接建立成功')
            this.isConnected = true
            resolve(true)
          })
        } else {
          // 使用普通连接
          this.socket = createConnection({
            host: this.config.host,
            port: this.config.port
          }, () => {
            console.log('TCP连接建立成功')
            this.isConnected = true
            resolve(true)
          })
        }

        this.socket.on('error', (error: Error) => {
          console.error('IMAP连接错误:', error)
          reject(error)
        })

        this.socket.on('close', () => {
          console.log('IMAP连接关闭')
          this.isConnected = false
        })

        // 等待服务器欢迎消息
        this.socket.once('data', (data: Buffer) => {
          console.log('服务器欢迎消息:', data.toString().trim())
        })
      })
    } catch (error) {
      console.error('IMAP连接失败:', error)
      return false
    }
  }

  /**
   * 登录IMAP服务器
   */
  async login(): Promise<boolean> {
    try {
      const response = await this.sendCommand(`LOGIN ${this.config.user} ${this.config.password}`)
      return response.includes('OK')
    } catch (error) {
      console.error('IMAP登录失败:', error)
      return false
    }
  }

  /**
   * 选择邮箱文件夹
   */
  async selectFolder(folder: string = 'INBOX'): Promise<boolean> {
    try {
      const response = await this.sendCommand(`SELECT ${folder}`)
      return response.includes('OK')
    } catch (error) {
      console.error('选择文件夹失败:', error)
      return false
    }
  }

  /**
   * 搜索包含特定内容的邮件
   */
  async searchEmails(criteria: string): Promise<number[]> {
    try {
      const response = await this.sendCommand(`SEARCH ${criteria}`)
      
      if (response.includes('OK')) {
        // 解析搜索结果
        const lines = response.split('\r\n')
        for (const line of lines) {
          if (line.startsWith('* SEARCH')) {
            const uids = line.replace('* SEARCH', '').trim().split(' ')
            return uids.filter(uid => uid && !isNaN(parseInt(uid))).map(uid => parseInt(uid))
          }
        }
      }
      
      return []
    } catch (error) {
      console.error('搜索邮件失败:', error)
      return []
    }
  }

  /**
   * 获取邮件内容
   */
  async fetchEmail(uid: number): Promise<EmailMessage | null> {
    try {
      const response = await this.sendCommand(`FETCH ${uid} (ENVELOPE BODY[TEXT])`)
      
      if (response.includes('OK')) {
        // 简化的邮件解析
        const email: EmailMessage = {
          id: `imap_${uid}`,
          from: this.extractFromResponse(response, 'FROM'),
          to: this.extractFromResponse(response, 'TO'),
          subject: this.extractFromResponse(response, 'SUBJECT'),
          text: this.extractBodyFromResponse(response),
          date: new Date().toISOString(),
          uid
        }
        
        return email
      }
      
      return null
    } catch (error) {
      console.error('获取邮件失败:', error)
      return null
    }
  }

  /**
   * 从响应中提取字段
   */
  private extractFromResponse(response: string, field: string): string {
    try {
      console.log(`开始解析${field}字段`)
      console.log(`响应内容: ${response.substring(0, 500)}...`)

      // 查找ENVELOPE部分 - 使用更精确的方法
      const envelopeStart = response.indexOf('ENVELOPE (')
      if (envelopeStart === -1) {
        console.log('未找到ENVELOPE部分')
        return ''
      }

      // 从ENVELOPE开始位置找到完整的括号内容
      let envelopeData = ''
      let depth = 0
      let start = envelopeStart + 'ENVELOPE ('.length

      for (let i = start; i < response.length; i++) {
        const char = response[i]
        if (char === '(') {
          depth++
        } else if (char === ')') {
          if (depth === 0) {
            break
          }
          depth--
        }
        envelopeData += char
      }

      console.log(`提取的ENVELOPE数据: ${envelopeData.substring(0, 300)}...`)

      // 简化的字段提取 - 直接使用正则表达式
      switch (field) {
        case 'SUBJECT':
          // 主题是第二个字段，格式："=?utf-8?B?MTIz?="
          const subjectMatch = envelopeData.match(/"([^"]*)" \(/)
          if (subjectMatch) {
            const subject = subjectMatch[1]
            console.log(`提取的主题: ${subject}`)
            return this.decodeBase64Subject(subject)
          }
          return ''

        case 'FROM':
          // 发件人格式：(("name" NIL "user" "domain"))
          const fromMatch = envelopeData.match(/\(\("([^"]*)" NIL "([^"]+)" "([^"]+)"\)\)/)
          if (fromMatch) {
            const email = `${fromMatch[2]}@${fromMatch[3]}`
            console.log(`提取的发件人: ${email}`)
            return email
          }
          return ''

        case 'TO':
          // 收件人在后面的位置
          const toMatch = envelopeData.match(/\(\("([^"]*)" NIL "([^"]+)" "([^"]+)"\)\)[^(]*\(\("([^"]*)" NIL "([^"]+)" "([^"]+)"\)\)/)
          if (toMatch) {
            const email = `${toMatch[5]}@${toMatch[6]}`
            console.log(`提取的收件人: ${email}`)
            return email
          }
          return ''

        default:
          return ''
      }
    } catch (error) {
      console.error(`解析${field}失败:`, error)
      return ''
    }
  }

  /**
   * 解析ENVELOPE部分
   */
  private parseEnvelopeParts(envelopeData: string): string[] {
    const parts: string[] = []
    let current = ''
    let depth = 0
    let inQuotes = false

    for (let i = 0; i < envelopeData.length; i++) {
      const char = envelopeData[i]

      if (char === '"' && envelopeData[i-1] !== '\\') {
        inQuotes = !inQuotes
        current += char
      } else if (!inQuotes) {
        if (char === '(') {
          depth++
          current += char
        } else if (char === ')') {
          depth--
          current += char
        } else if (char === ' ' && depth === 0) {
          if (current.trim()) {
            parts.push(current.trim())
            current = ''
          }
        } else {
          current += char
        }
      } else {
        current += char
      }
    }

    if (current.trim()) {
      parts.push(current.trim())
    }

    return parts
  }

  /**
   * 解析邮箱地址
   */
  private parseEmailAddress(addressPart: string): string {
    try {
      // 格式：(("name" NIL "user" "domain"))
      const match = addressPart.match(/\(\(".*?" NIL "([^"]+)" "([^"]+)"\)\)/)
      if (match) {
        return `${match[1]}@${match[2]}`
      }

      // 简化格式：((NIL NIL "user" "domain"))
      const simpleMatch = addressPart.match(/\(\(NIL NIL "([^"]+)" "([^"]+)"\)\)/)
      if (simpleMatch) {
        return `${simpleMatch[1]}@${simpleMatch[2]}`
      }

      return ''
    } catch (error) {
      console.error('解析邮箱地址失败:', error)
      return ''
    }
  }

  /**
   * 解码Base64主题
   */
  private decodeBase64Subject(subject: string): string {
    try {
      // 移除引号
      subject = subject.replace(/^"|"$/g, '')

      // 检查是否是Base64编码
      if (subject.startsWith('=?utf-8?B?') && subject.endsWith('?=')) {
        const base64Part = subject.substring(10, subject.length - 2)
        return Buffer.from(base64Part, 'base64').toString('utf-8')
      }

      return subject
    } catch (error) {
      console.error('解码主题失败:', error)
      return subject
    }
  }

  /**
   * 从响应中提取邮件正文
   */
  private extractBodyFromResponse(response: string): string {
    // 查找BODY[TEXT]部分
    const bodyStart = response.indexOf('BODY[TEXT]')
    if (bodyStart !== -1) {
      const bodyContent = response.substring(bodyStart)
      // 简化的正文提取
      const lines = bodyContent.split('\r\n')
      return lines.slice(1, -2).join('\n') // 去掉第一行和最后两行
    }
    return ''
  }

  /**
   * 删除邮件
   */
  async deleteEmail(uid: number): Promise<boolean> {
    try {
      await this.sendCommand(`STORE ${uid} +FLAGS (\\Deleted)`)
      const response = await this.sendCommand('EXPUNGE')
      return response.includes('OK')
    } catch (error) {
      console.error('删除邮件失败:', error)
      return false
    }
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    try {
      if (this.isConnected && this.socket) {
        await this.sendCommand('LOGOUT')
        this.socket.end()
      }
    } catch (error) {
      console.error('关闭IMAP连接失败:', error)
    } finally {
      this.isConnected = false
      this.socket = null
    }
  }

  /**
   * 获取转发邮件中的验证码
   */
  async getVerificationCode(tempEmail: string): Promise<{
    code: string | null
    emails: EmailMessage[]
    message: string
  }> {
    const emails: EmailMessage[] = []
    
    try {
      // 连接和登录
      const connected = await this.connect()
      if (!connected) {
        throw new Error('无法连接到IMAP服务器')
      }

      const loggedIn = await this.login()
      if (!loggedIn) {
        throw new Error('IMAP登录失败')
      }

      const selected = await this.selectFolder('INBOX')
      if (!selected) {
        throw new Error('无法选择INBOX文件夹')
      }

      // 搜索包含临时邮箱地址的邮件
      const uids = await this.searchEmails(`TO "${tempEmail}"`)
      console.log(`找到 ${uids.length} 封相关邮件`)

      // 获取邮件内容
      for (const uid of uids.slice(-5)) { // 只获取最新的5封邮件
        const email = await this.fetchEmail(uid)
        if (email) {
          emails.push(email)
          
          // 检查是否包含验证码
          const code = this.extractVerificationCode(email.text, tempEmail)
          if (code) {
            // 删除已处理的邮件
            await this.deleteEmail(uid)
            await this.close()
            
            return {
              code,
              emails,
              message: `验证码获取成功: ${code}`
            }
          }
        }
      }

      await this.close()
      
      return {
        code: null,
        emails,
        message: '未找到验证码'
      }

    } catch (error) {
      await this.close()
      console.error('获取验证码失败:', error)
      
      return {
        code: null,
        emails,
        message: `获取验证码失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 提取验证码
   */
  private extractVerificationCode(text: string, tempEmail: string): string | null {
    // 移除临时邮箱地址避免误识别
    let cleanText = text.replace(new RegExp(tempEmail, 'g'), '')
    
    // 查找6位数字验证码
    const codeMatch = cleanText.match(/(?<![a-zA-Z@.])\b\d{6}\b/)
    return codeMatch ? codeMatch[0] : null
  }
}
