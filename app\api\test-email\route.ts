import { NextRequest, NextResponse } from 'next/server'

/**
 * 临时邮箱测试API
 * 基于project2的邮箱验证功能
 */

interface EmailTestResult {
  email: string
  domain: string
  status: 'active' | 'inactive' | 'error'
  canReceive: boolean
  lastChecked: string
  provider: string
  testMessage?: string
  error?: string
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址不能为空'
      }, { status: 400 })
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        success: false,
        error: '邮箱格式不正确'
      }, { status: 400 })
    }

    const domain = email.split('@')[1]
    const customDomain = process.env.NEXT_PUBLIC_TEMP_MAIL_DOMAIN || 'mcpserver.sbs'

    // 检查是否是我们的自定义域名
    if (domain === customDomain) {
      // 这里应该集成project2中的真实邮箱验证逻辑
      // 目前先返回模拟结果
      const result: EmailTestResult = {
        email,
        domain,
        status: 'active',
        canReceive: true,
        lastChecked: new Date().toISOString(),
        provider: 'Custom Domain (mcpserver.sbs)',
        testMessage: '自定义域名邮箱，支持接收邮件'
      }

      return NextResponse.json({
        success: true,
        data: result
      })
    } else {
      // 其他域名的邮箱测试
      const result: EmailTestResult = {
        email,
        domain,
        status: 'inactive',
        canReceive: false,
        lastChecked: new Date().toISOString(),
        provider: 'Unknown',
        testMessage: '非自定义域名，无法验证接收能力'
      }

      return NextResponse.json({
        success: true,
        data: result
      })
    }

  } catch (error) {
    console.error('Email test error:', error)
    return NextResponse.json({
      success: false,
      error: '测试邮箱时发生错误'
    }, { status: 500 })
  }
}

/**
 * 获取邮箱配置信息
 */
export async function GET() {
  try {
    const customDomain = process.env.NEXT_PUBLIC_TEMP_MAIL_DOMAIN || 'mcpserver.sbs'
    
    return NextResponse.json({
      success: true,
      data: {
        customDomain,
        supportedDomains: [customDomain],
        testInstructions: [
          '1. 生成使用自定义域名的邮箱地址',
          '2. 点击测试按钮验证邮箱接收能力',
          '3. 查看测试结果和状态信息',
          '4. 复制邮箱信息用于其他用途'
        ],
        notes: [
          '当前使用的是project2配置的自定义域名',
          '只有使用自定义域名的邮箱才能正常接收邮件',
          '其他域名的邮箱仅用于演示，无法实际接收邮件'
        ]
      }
    })
  } catch (error) {
    console.error('Get email config error:', error)
    return NextResponse.json({
      success: false,
      error: '获取邮箱配置时发生错误'
    }, { status: 500 })
  }
}
