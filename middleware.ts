import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT, extractTokenFromRequest } from './lib/jwt'
import { isTokenBlacklisted } from './lib/token-blacklist'
import {
  <PERSON><PERSON>hecker,
  RequestUtils,
  RedirectUtils,
  MiddlewareLogger
} from './lib/middleware-utils'

/**
 * Next.js 中间件 - 全站访问控制
 * 保护所有页面，确保未登录用户重定向到登录页面
 */



/**
 * 从请求中获取token（支持多种方式）
 */
function getTokenFromRequest(request: NextRequest): string | null {
  // 1. 从Authorization header获取
  const authToken = extractTokenFromRequest(request)
  if (authToken) {
    return authToken
  }
  
  // 2. 从cookie获取
  const cookieToken = request.cookies.get('auth-token')?.value
  if (cookieToken) {
    return cookieToken
  }
  
  return null
}

/**
 * 验证用户身份
 */
async function validateAuthentication(request: NextRequest): Promise<boolean> {
  try {
    // 检查是否为内部请求（来自定时任务或系统内部调用）
    const internalRequest = request.headers.get('X-Internal-Request')
    if (internalRequest === 'true') {
      return true // 内部请求直接通过
    }

    const token = getTokenFromRequest(request)

    if (!token) {
      return false
    }

    // 验证JWT token
    const payload = await verifyJWT(token)
    if (!payload) {
      return false
    }

    // 检查token是否在黑名单中
    if (isTokenBlacklisted(token)) {
      return false
    }

    return true
  } catch (error) {
    console.error('身份验证失败:', error)
    return false
  }
}

/**
 * 创建重定向响应
 */
function createRedirectResponse(request: NextRequest, redirectPath: string): NextResponse {
  let redirectUrl: string

  if (redirectPath === '/login') {
    redirectUrl = RedirectUtils.buildLoginRedirectUrl(request)
  } else {
    redirectUrl = RedirectUtils.buildPostLoginRedirectUrl(request)
  }

  MiddlewareLogger.logRedirect(request, request.nextUrl.pathname, redirectPath)
  return NextResponse.redirect(redirectUrl)
}

/**
 * 创建未授权API响应
 */
function createUnauthorizedResponse(): NextResponse {
  return NextResponse.json(
    { 
      success: false, 
      error: '需要登录访问',
      code: 'UNAUTHORIZED'
    },
    { status: 401 }
  )
}

/**
 * 主中间件函数
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 1. 静态资源直接通过
  if (PathChecker.isStaticPath(pathname)) {
    return NextResponse.next()
  }

  // 2. 公开路径直接通过
  if (PathChecker.isPublicPath(pathname)) {
    return NextResponse.next()
  }

  // 3. 验证用户身份
  const isAuthenticated = await validateAuthentication(request)

  // 记录访问日志
  MiddlewareLogger.logAccess(request, isAuthenticated, 'ACCESS_ATTEMPT')

  // 4. 处理API路径
  if (PathChecker.isApiPath(pathname)) {
    if (PathChecker.isProtectedApiPath(pathname) && !isAuthenticated) {
      MiddlewareLogger.logAuthFailure(request, 'API_ACCESS_DENIED')
      return createUnauthorizedResponse()
    }
    return NextResponse.next()
  }

  // 5. 处理页面路径
  if (!isAuthenticated) {
    // 未登录用户重定向到登录页面
    MiddlewareLogger.logAuthFailure(request, 'PAGE_ACCESS_DENIED')
    return createRedirectResponse(request, '/login')
  }

  // 6. 已登录用户访问登录页面，重定向到首页
  if (PathChecker.isLoginPage(pathname)) {
    return createRedirectResponse(request, '/')
  }

  // 7. 已登录用户正常访问
  MiddlewareLogger.logAccess(request, true, 'ACCESS_GRANTED')
  return NextResponse.next()
}

/**
 * 中间件配置
 * 指定哪些路径需要运行中间件
 */
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - 其他静态资源
     */
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
}
