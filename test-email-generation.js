// 测试邮箱生成逻辑
// 运行: node test-email-generation.js

const { EmailGenerator } = require('./lib/email-generator.ts')

// 创建邮箱生成器实例
const generator = new EmailGenerator('mcpserver.sbs')

console.log('🧪 测试邮箱生成逻辑...\n')

// 测试1: 单个邮箱生成
console.log('📧 测试1: 单个邮箱生成')
for (let i = 0; i < 5; i++) {
  const email = generator.generateEmail()
  console.log(`  ${i + 1}. ${email.email}`)
}

console.log('\n📧 测试2: 批量邮箱生成 (10个)')
const batchEmails = generator.generateBatchEmails(10)
batchEmails.forEach((email, index) => {
  console.log(`  ${index + 1}. ${email.email}`)
})

// 检查重复
console.log('\n🔍 测试3: 重复检查')
const allEmails = batchEmails.map(e => e.email)
const uniqueEmails = [...new Set(allEmails)]
console.log(`  生成邮箱数: ${allEmails.length}`)
console.log(`  唯一邮箱数: ${uniqueEmails.length}`)
console.log(`  重复率: ${((allEmails.length - uniqueEmails.length) / allEmails.length * 100).toFixed(2)}%`)

// 测试前缀多样性
console.log('\n🎨 测试4: 前缀多样性分析')
const prefixes = allEmails.map(email => email.split('@')[0])
const prefixPatterns = prefixes.map(prefix => {
  // 分析前缀模式
  if (/^[a-z]+[0-9]+[a-z0-9]+$/.test(prefix)) return 'name+num+chars'
  if (/^[a-z]+[a-z]+[0-9]+$/.test(prefix)) return 'name+name+num'
  if (/^[a-z]+[a-z0-9]+[0-9]+$/.test(prefix)) return 'prefix+chars+num'
  return 'other'
})

const patternCounts = {}
prefixPatterns.forEach(pattern => {
  patternCounts[pattern] = (patternCounts[pattern] || 0) + 1
})

console.log('  前缀模式分布:')
Object.entries(patternCounts).forEach(([pattern, count]) => {
  console.log(`    ${pattern}: ${count} (${(count / prefixes.length * 100).toFixed(1)}%)`)
})

console.log('\n✅ 测试完成!')
