"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Database,
  Server,
  Clock,
  Home,
  Settings,
  Info
} from "lucide-react"
import Link from "next/link"

interface DiagnosticResult {
  success: boolean
  timestamp: string
  totalTime: number
  checks: {
    environment: {
      supabaseUrl: boolean
      supabaseKey: boolean
      urlValue: string
      keyValue: string
    }
    connection: {
      success: boolean
      error: string | null
      responseTime: number
    }
    table: {
      exists: boolean
      error: string | null
      columns: string[]
    }
    data: {
      count: number
      error: string | null
      sampleData: any[] | null
    }
    api: {
      success: boolean
      error: string | null
      responseTime: number
    }
  }
  recommendations: Array<{
    type: 'success' | 'warning' | 'error'
    title: string
    description: string
    action: string
  }>
}

export default function DebugPage() {
  const [result, setResult] = useState<DiagnosticResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [initLoading, setInitLoading] = useState(false)
  const [initResult, setInitResult] = useState<any>(null)
  const [testDataLoading, setTestDataLoading] = useState(false)
  const [testDataResult, setTestDataResult] = useState<any>(null)

  const runDiagnostic = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/debug/database')
      const data = await response.json()
      
      if (data.success) {
        setResult(data)
      } else {
        setError(data.error || '诊断失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络请求失败')
    } finally {
      setLoading(false)
    }
  }

  const initDatabase = async () => {
    setInitLoading(true)
    setInitResult(null)

    try {
      const response = await fetch('/api/debug/init-database', {
        method: 'PUT'
      })
      const data = await response.json()
      setInitResult(data)

      // 初始化后重新运行诊断
      if (data.success) {
        setTimeout(() => {
          runDiagnostic()
        }, 1000)
      }
    } catch (err) {
      setInitResult({
        success: false,
        error: err instanceof Error ? err.message : '初始化失败'
      })
    } finally {
      setInitLoading(false)
    }
  }

  const createTestData = async () => {
    setTestDataLoading(true)
    setTestDataResult(null)

    try {
      const response = await fetch('/api/debug/create-test-data', {
        method: 'POST'
      })
      const data = await response.json()
      setTestDataResult(data)

      // 创建测试数据后重新运行诊断
      if (data.success) {
        setTimeout(() => {
          runDiagnostic()
        }, 1000)
      }
    } catch (err) {
      setTestDataResult({
        success: false,
        error: err instanceof Error ? err.message : '创建测试数据失败'
      })
    } finally {
      setTestDataLoading(false)
    }
  }

  useEffect(() => {
    runDiagnostic()
  }, [])

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    )
  }

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">系统诊断</h1>
            <p className="text-lg text-gray-600">检查数据库连接和系统状态</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={runDiagnostic} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              {loading ? '诊断中...' : '重新诊断'}
            </Button>
            <Link href="/">
              <Button variant="outline">
                <Home className="mr-2 h-4 w-4" />
                返回首页
              </Button>
            </Link>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <Alert className="mb-6" variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertTitle>诊断失败</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* 诊断结果 */}
        {result && (
          <div className="space-y-6">
            {/* 概览 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  诊断概览
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {result.totalTime}ms
                    </div>
                    <div className="text-sm text-gray-500">总耗时</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {result.checks.data.count}
                    </div>
                    <div className="text-sm text-gray-500">数据条数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">
                      {new Date(result.timestamp).toLocaleString('zh-CN')}
                    </div>
                    <div className="text-sm text-gray-500">检查时间</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 详细检查结果 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 环境变量检查 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    环境变量
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>Supabase URL</span>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.checks.environment.supabaseUrl)}
                      <Badge variant={result.checks.environment.supabaseUrl ? "default" : "destructive"}>
                        {result.checks.environment.supabaseUrl ? "已配置" : "未配置"}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Supabase Key</span>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.checks.environment.supabaseKey)}
                      <Badge variant={result.checks.environment.supabaseKey ? "default" : "destructive"}>
                        {result.checks.environment.supabaseKey ? "已配置" : "未配置"}
                      </Badge>
                    </div>
                  </div>
                  {result.checks.environment.supabaseUrl && (
                    <div className="text-xs text-gray-500 mt-2">
                      URL: {result.checks.environment.urlValue}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 数据库连接检查 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Server className="h-5 w-5" />
                    数据库连接
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>连接状态</span>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.checks.connection.success)}
                      <Badge variant={result.checks.connection.success ? "default" : "destructive"}>
                        {result.checks.connection.success ? "正常" : "失败"}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>响应时间</span>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{result.checks.connection.responseTime}ms</span>
                    </div>
                  </div>
                  {result.checks.connection.error && (
                    <div className="text-xs text-red-500 mt-2">
                      错误: {result.checks.connection.error}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* 数据表和API检查 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 数据表检查 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    数据表状态
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>表是否存在</span>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.checks.table.exists)}
                      <Badge variant={result.checks.table.exists ? "default" : "destructive"}>
                        {result.checks.table.exists ? "存在" : "不存在"}
                      </Badge>
                    </div>
                  </div>
                  {result.checks.table.exists && (
                    <div className="text-xs text-gray-500">
                      字段数量: {result.checks.table.columns.length}
                    </div>
                  )}
                  {result.checks.table.error && (
                    <div className="text-xs text-red-500">
                      错误: {result.checks.table.error}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* API接口检查 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Server className="h-5 w-5" />
                    API接口
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>接口状态</span>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.checks.api.success)}
                      <Badge variant={result.checks.api.success ? "default" : "destructive"}>
                        {result.checks.api.success ? "正常" : "异常"}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>响应时间</span>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{result.checks.api.responseTime}ms</span>
                    </div>
                  </div>
                  {result.checks.api.error && (
                    <div className="text-xs text-red-500">
                      错误: {result.checks.api.error}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* 样本数据 */}
            {result.checks.data.sampleData && result.checks.data.sampleData.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="h-5 w-5" />
                    样本数据
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {result.checks.data.sampleData.map((item, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium">{item.full_name}</div>
                        <div className="text-xs text-gray-500">{item.email}</div>
                        <div className="text-xs text-gray-400">
                          {new Date(item.created_at).toLocaleString('zh-CN')}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 建议和解决方案 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  建议和解决方案
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {result.recommendations.map((rec, index) => (
                    <Alert key={index} variant={rec.type === 'error' ? 'destructive' : 'default'}>
                      <div className="flex items-start gap-3">
                        {getRecommendationIcon(rec.type)}
                        <div className="flex-1">
                          <AlertTitle>{rec.title}</AlertTitle>
                          <AlertDescription className="mt-1">
                            <div>{rec.description}</div>
                            <div className="mt-2 text-sm font-medium">
                              解决方案: {rec.action}
                            </div>
                          </AlertDescription>
                        </div>
                      </div>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 数据库初始化结果 */}
            {initResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {initResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    数据库初始化结果
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-sm">
                      {initResult.message}
                    </div>
                    {initResult.sql && (
                      <div>
                        <div className="text-sm font-medium mb-2">SQL语句：</div>
                        <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                          {initResult.sql}
                        </pre>
                      </div>
                    )}
                    {initResult.instructions && (
                      <div>
                        <div className="text-sm font-medium mb-2">操作步骤：</div>
                        <ul className="text-sm space-y-1">
                          {initResult.instructions.map((instruction: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-blue-500">•</span>
                              {instruction}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 测试数据创建结果 */}
            {testDataResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {testDataResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    测试数据创建结果
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-sm">
                      {testDataResult.message}
                    </div>
                    {testDataResult.count && (
                      <div className="text-sm text-gray-600">
                        创建了 {testDataResult.count} 条测试记录
                      </div>
                    )}
                    {testDataResult.error && (
                      <div className="text-sm text-red-600">
                        错误: {testDataResult.error}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle>快速操作</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  <Button onClick={initDatabase} disabled={initLoading} variant="outline">
                    <Database className={`mr-2 h-4 w-4 ${initLoading ? 'animate-spin' : ''}`} />
                    {initLoading ? '初始化中...' : '初始化数据库'}
                  </Button>
                  <Button onClick={createTestData} disabled={testDataLoading} variant="outline">
                    <Settings className={`mr-2 h-4 w-4 ${testDataLoading ? 'animate-spin' : ''}`} />
                    {testDataLoading ? '创建中...' : '创建测试数据'}
                  </Button>
                  <Link href="/saved-data">
                    <Button variant="outline">
                      <Database className="mr-2 h-4 w-4" />
                      查看已保存数据
                    </Button>
                  </Link>
                  <Link href="/">
                    <Button variant="outline">
                      <Home className="mr-2 h-4 w-4" />
                      生成新数据
                    </Button>
                  </Link>
                  <Button onClick={runDiagnostic} variant="outline">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    重新诊断
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
