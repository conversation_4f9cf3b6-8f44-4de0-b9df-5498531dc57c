# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js 构建输出
.next/
out/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log
logs/

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
.nyc_output

# 依赖锁定文件 (保留一个)
# package-lock.json
# yarn.lock

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 微服务
.pnp
.pnp.js

# 测试覆盖率
coverage
*.lcov

# 编辑器目录和文件
.vscode/
.idea/
*.swp
*.swo
*~

# OS 生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# 文档
README.md
CHANGELOG.md
LICENSE
*.md

# 开发工具
.prettierrc
.eslintrc*
jest.config.js
*.config.js

# 临时文件
tmp/
temp/

# SSL 证书
ssl/
*.pem
*.key
*.crt

# 备份文件
*.bak
*.backup
