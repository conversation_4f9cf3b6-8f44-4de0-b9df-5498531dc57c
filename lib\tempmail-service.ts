/**
 * Tempmail.plus 临时邮箱服务
 * 基于project2中的tempmail.plus API集成
 */

interface TempMailAccount {
  email: string
  password: string
  epin?: string
  extension?: string
  username?: string
  domain: string
  created: number
  expires?: number
}

interface TempMailMessage {
  id: string
  from: string
  to: string
  subject: string
  text: string
  html?: string
  date: string
  attachments?: any[]
}

interface TempMailResponse {
  success: boolean
  result?: any
  error?: string
  first_id?: string
}

export class TempMailService {
  private apiBase: string
  private customDomain: string

  constructor() {
    this.apiBase = process.env.NEXT_PUBLIC_TEMPMAIL_PLUS_API_BASE || 'https://tempmail.plus/api'
    this.customDomain = process.env.NEXT_PUBLIC_TEMP_MAIL_DOMAIN || 'mcpserver.sbs'
  }

  /**
   * 创建临时邮箱账户
   */
  async createTempMail(): Promise<TempMailAccount> {
    try {
      // 生成随机用户名
      const username = this.generateRandomUsername()
      const email = `${username}@${this.customDomain}`
      
      // 生成随机密码
      const password = this.generateRandomPassword()
      
      // 创建邮箱账户
      const account: TempMailAccount = {
        email,
        password,
        username,
        domain: this.customDomain,
        created: Date.now(),
        expires: Date.now() + (24 * 60 * 60 * 1000) // 24小时后过期
      }

      return account
    } catch (error) {
      throw new Error(`创建临时邮箱失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取邮箱收件箱
   */
  async getInbox(email: string, epin?: string): Promise<TempMailMessage[]> {
    try {
      const [username, domain] = email.split('@')
      const extension = `@${domain}`
      
      const url = `${this.apiBase}/mails?email=${username}${extension}&limit=20${epin ? `&epin=${epin}` : ''}`
      
      const response = await fetch(url)
      const data: TempMailResponse = await response.json()
      
      if (!data.success || !data.result) {
        return []
      }

      return data.result.map((mail: any) => ({
        id: mail.id,
        from: mail.from,
        to: mail.to,
        subject: mail.subject,
        text: mail.text,
        html: mail.html,
        date: mail.date,
        attachments: mail.attachments || []
      }))
    } catch (error) {
      console.error('获取收件箱失败:', error)
      return []
    }
  }

  /**
   * 获取特定邮件内容
   */
  async getMail(mailId: string, email: string, epin?: string): Promise<TempMailMessage | null> {
    try {
      const [username, domain] = email.split('@')
      const extension = `@${domain}`
      
      const url = `${this.apiBase}/mails/${mailId}?email=${username}${extension}${epin ? `&epin=${epin}` : ''}`
      
      const response = await fetch(url)
      const data: TempMailResponse = await response.json()
      
      if (!data.success || !data.result) {
        return null
      }

      const mail = data.result
      return {
        id: mail.id,
        from: mail.from,
        to: mail.to,
        subject: mail.subject,
        text: mail.text,
        html: mail.html,
        date: mail.date,
        attachments: mail.attachments || []
      }
    } catch (error) {
      console.error('获取邮件失败:', error)
      return null
    }
  }

  /**
   * 删除邮件
   */
  async deleteMail(mailId: string, email: string, epin?: string): Promise<boolean> {
    try {
      const [username, domain] = email.split('@')
      const extension = `@${domain}`
      
      const response = await fetch(`${this.apiBase}/mails/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          email: `${username}${extension}`,
          first_id: mailId,
          ...(epin && { epin })
        })
      })

      const data: TempMailResponse = await response.json()
      return data.success && data.result === true
    } catch (error) {
      console.error('删除邮件失败:', error)
      return false
    }
  }

  /**
   * 测试邮箱是否可用
   */
  async testEmail(email: string): Promise<{
    available: boolean
    canReceive: boolean
    domain: string
    message: string
  }> {
    try {
      const [username, domain] = email.split('@')
      
      // 检查是否是我们的自定义域名
      if (domain === this.customDomain) {
        return {
          available: true,
          canReceive: true,
          domain,
          message: '自定义域名邮箱，支持接收邮件'
        }
      } else {
        // 尝试获取收件箱来测试邮箱是否可用
        const inbox = await this.getInbox(email)
        return {
          available: true,
          canReceive: inbox !== null,
          domain,
          message: inbox !== null ? '邮箱可用，支持接收邮件' : '邮箱不可用或无法接收邮件'
        }
      }
    } catch (error) {
      return {
        available: false,
        canReceive: false,
        domain: email.split('@')[1] || '',
        message: `测试失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 生成随机用户名
   */
  private generateRandomUsername(): string {
    const names = [
      'john', 'jane', 'mike', 'sarah', 'david', 'lisa', 'tom', 'mary',
      'james', 'anna', 'robert', 'emma', 'william', 'olivia', 'richard', 'sophia',
      'charles', 'isabella', 'joseph', 'charlotte', 'thomas', 'amelia', 'daniel', 'mia'
    ]
    
    const name = names[Math.floor(Math.random() * names.length)]
    const timestamp = Date.now().toString().slice(-4)
    const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')
    
    return `${name}${timestamp}${random}`
  }

  /**
   * 生成随机密码（至少12位，包含大小写字母、数字和特殊符号）
   */
  private generateRandomPassword(): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const specialChars = '!@#$%^&*'
    const allChars = lowercase + uppercase + numbers + specialChars

    let password = ''

    // 确保包含每种类型的字符
    password += lowercase[Math.floor(Math.random() * lowercase.length)]
    password += uppercase[Math.floor(Math.random() * uppercase.length)]
    password += numbers[Math.floor(Math.random() * numbers.length)]
    password += specialChars[Math.floor(Math.random() * specialChars.length)]

    // 填充剩余位数（至少8位，总共至少12位）
    const remainingLength = 8 + Math.floor(Math.random() * 4) // 8-11位
    for (let i = 0; i < remainingLength; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)]
    }

    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('')
  }

  /**
   * 从邮件内容中提取验证码
   */
  extractVerificationCode(mailText: string): string | null {
    // 避免域名中的数字被误识别为验证码
    const cleanText = mailText.replace(/@[a-zA-Z0-9.-]+/g, '')
    
    // 查找6位数字验证码
    const codeMatch = cleanText.match(/(?<![a-zA-Z@.])\b\d{6}\b/)
    
    return codeMatch ? codeMatch[0] : null
  }

  /**
   * 等待接收验证码邮件
   */
  async waitForVerificationCode(
    email: string, 
    maxRetries: number = 10, 
    retryInterval: number = 30000,
    epin?: string
  ): Promise<string | null> {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const inbox = await this.getInbox(email, epin)
        
        // 查找最新的邮件
        for (const mail of inbox) {
          const code = this.extractVerificationCode(mail.text)
          if (code) {
            // 找到验证码后删除邮件
            await this.deleteMail(mail.id, email, epin)
            return code
          }
        }
        
        // 如果没有找到验证码，等待后重试
        if (attempt < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, retryInterval))
        }
      } catch (error) {
        console.error(`获取验证码失败 (第${attempt + 1}次尝试):`, error)
        if (attempt < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, retryInterval))
        }
      }
    }
    
    return null
  }
}

// 导出默认实例
export const tempMailService = new TempMailService()

// 导出类型
export type { TempMailAccount, TempMailMessage, TempMailResponse }
