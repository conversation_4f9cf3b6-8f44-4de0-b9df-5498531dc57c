import { NextRequest, NextResponse } from 'next/server'
import { getActiveSessionCount, getActiveSessions, getSessionStats } from '@/lib/session-manager'

/**
 * GET /api/debug/sessions
 * 调试接口：获取活跃会话信息
 */
export async function GET(request: NextRequest) {
  try {
    const sessionCount = getActiveSessionCount()
    const sessions = getActiveSessions()
    const stats = getSessionStats()

    return NextResponse.json({
      success: true,
      data: {
        count: sessionCount,
        sessions: sessions.map(session => ({
          sessionId: session.sessionId,
          username: session.username,
          loginTime: new Date(session.loginTime).toLocaleString('zh-CN'),
          lastActivity: new Date(session.lastActivity).toLocaleString('zh-CN'),
          token: session.token.substring(0, 20) + '...' // 只显示token前20个字符
        })),
        stats
      }
    })
  } catch (error) {
    console.error('获取会话信息失败:', error)
    return NextResponse.json({
      success: false,
      error: '获取会话信息失败'
    }, { status: 500 })
  }
}
