"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Copy, Check, Mail, Plus, RefreshCw, Search, Trash2, Clock, User, X, Bell, CheckSquare, Square, Download } from "lucide-react"
import Link from "next/link"
import { BatchEmailCreate } from "@/components/batch-email-create"
import { EmailStats, DetailedEmailStats } from "@/components/email-stats"
import ImportEmailsDialog from "@/components/import-emails-dialog"
import EmailSyncControl from "@/components/email-sync-control"

import { MessageView } from "@/components/emails/message-view"
import { MessageList } from "@/components/emails/message-list"

interface Email {
  id: string
  address: string
  password: string
  type: string
  config?: string
  createdAt: number
  isActive: boolean
  unreadCount?: number
  hasNewMessages?: boolean
}

interface Message {
  id: string
  emailId: string
  fromAddress: string
  toAddress: string
  subject: string
  textContent?: string
  htmlContent?: string
  receivedAt: number
  isRead: boolean
  hasVerificationCode: boolean
  verificationCode?: string
}

export default function EmailManagerPage() {
  const [emails, setEmails] = useState<Email[]>([])
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingMessages, setIsLoadingMessages] = useState(false)
  const [copiedEmails, setCopiedEmails] = useState<Set<string>>(new Set())
  const [copiedField, setCopiedField] = useState<string | null>(null)
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [refreshCountdown, setRefreshCountdown] = useState(10)
  const [deleteDialog, setDeleteDialog] = useState<{open: boolean, email: Email | null}>({open: false, email: null})
  const [selectedEmails, setSelectedEmails] = useState<Set<string>>(new Set())
  const [batchDeleteDialog, setBatchDeleteDialog] = useState(false)
  const autoRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [stats, setStats] = useState({
    totalEmails: 0,
    totalMessages: 0,
    verificationMessages: 0,
    activeEmails: 0
  })
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState<"all">("all")
  const [importDialogOpen, setImportDialogOpen] = useState(false)
  const { toast } = useToast()

  // 获取邮箱列表
  const fetchEmails = async () => {
    try {
      const response = await fetch('/api/emails')
      const result = await response.json()

      if (result.success) {
        // 处理新的数据结构
        const emailsData = result.data.emails || result.data
        setEmails(emailsData)
        // 更新统计信息
        updateStats(emailsData)
      } else {
        toast({
          title: "获取邮箱列表失败",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "获取邮箱列表失败",
        description: "网络错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 更新统计信息
  const updateStats = async (emailList: Email[]) => {
    try {
      const statsResponse = await fetch('/api/email-stats')
      const statsResult = await statsResponse.json()

      if (statsResult.success) {
        setStats({
          totalEmails: statsResult.data.emails.total,
          totalMessages: statsResult.data.messages.total,
          verificationMessages: statsResult.data.messages.withVerificationCode,
          activeEmails: statsResult.data.emails.active
        })
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
      // 如果API调用失败，使用本地数据作为备用
      setStats({
        totalEmails: emailList.length,
        totalMessages: 0,
        verificationMessages: 0,
        activeEmails: emailList.filter(email => email.isActive).length
      })
    }
  }

  // 获取邮箱的消息列表
  const fetchMessages = async (emailId: string) => {
    // 如果已经在加载中，忽略新的请求
    if (isLoadingMessages) {
      return
    }

    setIsLoadingMessages(true)
    setSelectedMessage(null) // 清空当前选中的消息

    try {
      const response = await fetch(`/api/emails/${emailId}`)
      const result = await response.json()

      if (result.success) {
        setMessages(result.data.messages || [])
      } else {
        setMessages([])
        toast({
          title: "获取消息列表失败",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      setMessages([])
      toast({
        title: "获取消息列表失败",
        description: "网络错误",
        variant: "destructive",
      })
    } finally {
      setIsLoadingMessages(false)
    }
  }

  // 创建新邮箱
  const createNewEmail = async () => {
    try {
      // 首先创建临时邮箱
      const tempMailResponse = await fetch('/api/create-tempmail', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const tempMailResult = await tempMailResponse.json()

      if (tempMailResult.success) {
        // 然后保存到数据库
        const saveResponse = await fetch('/api/emails', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            address: tempMailResult.data.email,
            password: tempMailResult.data.epin || 'tempmail',
            type: 'tempmail',
            config: {
              domain: tempMailResult.data.domain,
              epin: tempMailResult.data.epin
            }
          }),
        })

        const saveResult = await saveResponse.json()

        if (saveResult.success) {
          toast({
            title: "邮箱创建成功",
            description: `邮箱: ${tempMailResult.data.email}`,
          })
          fetchEmails() // 刷新邮箱列表
        } else {
          toast({
            title: "保存邮箱失败",
            description: saveResult.error,
            variant: "destructive",
          })
        }
      } else {
        toast({
          title: "创建邮箱失败",
          description: tempMailResult.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "创建邮箱失败",
        description: "网络错误",
        variant: "destructive",
      })
    }
  }

  // 批量创建邮箱
  const createBatchEmails = async (count: number) => {
    try {
      let successCount = 0
      let failCount = 0

      for (let i = 0; i < count; i++) {
        try {
          // 首先创建临时邮箱
          const tempMailResponse = await fetch('/api/create-tempmail', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          })

          const tempMailResult = await tempMailResponse.json()

          if (tempMailResult.success) {
            // 然后保存到数据库
            const response = await fetch('/api/emails', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                address: tempMailResult.data.email,
                password: tempMailResult.data.epin || 'tempmail',
                type: 'tempmail',
                config: {
                  domain: tempMailResult.data.domain,
                  epin: tempMailResult.data.epin
                }
              }),
            })

            const result = await response.json()

            if (result.success) {
              successCount++
            } else {
              failCount++
            }
          } else {
            failCount++
          }
        } catch (error) {
          failCount++
        }
      }

      if (successCount > 0) {
        toast({
          title: "批量创建完成",
          description: `成功创建 ${successCount} 个邮箱${failCount > 0 ? `，失败 ${failCount} 个` : ''}`,
        })
        fetchEmails() // 刷新邮箱列表
      } else {
        toast({
          title: "批量创建失败",
          description: "所有邮箱创建失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "批量创建失败",
        description: "网络错误",
        variant: "destructive",
      })
    }
  }

  // 打开删除确认对话框
  const openDeleteDialog = (email: Email) => {
    setDeleteDialog({open: true, email})
  }

  // 批量选择处理
  const toggleEmailSelection = (emailId: string) => {
    const newSelected = new Set(selectedEmails)
    if (newSelected.has(emailId)) {
      newSelected.delete(emailId)
    } else {
      newSelected.add(emailId)
    }
    setSelectedEmails(newSelected)
  }

  const toggleSelectAll = () => {
    if (selectedEmails.size === filteredEmails.length) {
      setSelectedEmails(new Set())
    } else {
      setSelectedEmails(new Set(filteredEmails.map(email => email.id)))
    }
  }

  // 批量删除邮箱
  const batchDeleteEmails = async () => {
    try {
      const deletePromises = Array.from(selectedEmails).map(emailId =>
        fetch(`/api/emails/${emailId}`, { method: 'DELETE' })
      )

      await Promise.all(deletePromises)

      // 刷新邮箱列表
      await fetchEmails()

      // 清空选择
      setSelectedEmails(new Set())
      setBatchDeleteDialog(false)

      toast({
        title: "批量删除成功",
        description: `已删除 ${selectedEmails.size} 个邮箱`,
      })
    } catch (error) {
      toast({
        title: "批量删除失败",
        description: "删除过程中发生错误",
        variant: "destructive",
      })
    }
  }

  // 确认删除邮箱
  const confirmDeleteEmail = async () => {
    const email = deleteDialog.email
    if (!email) return

    try {
      const response = await fetch(`/api/emails/${email.id}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "邮箱删除成功",
          description: `邮箱 ${email.address} 已删除`,
        })

        // 如果删除的是当前选中的邮箱，清空选择
        if (selectedEmail?.id === email.id) {
          setSelectedEmail(null)
          setMessages([])
          setSelectedMessage(null)
        }

        fetchEmails() // 刷新邮箱列表
      } else {
        toast({
          title: "删除邮箱失败",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "删除邮箱失败",
        description: "网络错误",
        variant: "destructive",
      })
    } finally {
      setDeleteDialog({open: false, email: null})
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string, field: string, itemId?: string) => {
    try {
      await navigator.clipboard.writeText(text)

      if (itemId) {
        // 对于邮箱地址，使用独立状态管理
        setCopiedEmails(prev => new Set([...prev, itemId]))
        setTimeout(() => {
          setCopiedEmails(prev => {
            const newSet = new Set(prev)
            newSet.delete(itemId)
            return newSet
          })
        }, 2000)
      } else {
        // 对于其他字段（如验证码），使用原有逻辑
        setCopiedField(field)
        setTimeout(() => setCopiedField(null), 2000)
      }

      toast({
        title: "复制成功",
        description: `已复制 ${field}`,
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  // 刷新邮件（从数据库获取最新邮件）
  const refreshEmails = async (emailAddress: string) => {
    // 如果正在加载消息，跳过刷新
    if (isLoadingMessages) {
      return
    }

    // 检查是否还是当前选中的邮箱，避免切换邮箱后执行旧邮箱的刷新
    if (!selectedEmail || selectedEmail.address !== emailAddress) {
      console.log(`跳过刷新：邮箱已切换 (${emailAddress} -> ${selectedEmail?.address || 'none'})`)
      return
    }

    try {
      // 触发邮件同步（从IMAP获取新邮件到数据库）
      const syncResponse = await fetch('/api/email-sync-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailAddress: emailAddress,
          syncType: 'single'
        }),
      })

      const syncResult = await syncResponse.json()

      // 无论同步是否成功，都从数据库刷新邮件列表
      if (selectedEmail && selectedEmail.address === emailAddress && !isLoadingMessages) {
        await fetchMessages(selectedEmail.id)
      }

      // 刷新邮箱列表以更新未读计数
      setTimeout(async () => {
        await fetchEmails()
        await updateStats(emails) // 更新统计信息
      }, 500)

      if (syncResult.success) {
        toast({
          title: "邮件刷新成功",
          description: `同步了 ${syncResult.data?.newMessages || 0} 条新邮件`,
        })
      } else {
        // 即使同步失败，也显示从数据库获取的邮件
        toast({
          title: "邮件已刷新",
          description: "显示数据库中的邮件（同步可能失败）",
        })
      }
    } catch (error) {
      // 即使网络错误，也尝试从数据库获取邮件
      if (selectedEmail && selectedEmail.address === emailAddress && !isLoadingMessages) {
        await fetchMessages(selectedEmail.id)
      }

      toast({
        title: "邮件已刷新",
        description: "显示数据库中的邮件（网络错误）",
      })
    }
  }

  // 选择邮箱
  const selectEmail = (email: Email) => {
    // 如果正在加载消息，则忽略点击
    if (isLoadingMessages) {
      return
    }

    // 立即清除所有定时器，确保切换邮箱时取消原来的刷新
    if (autoRefreshTimeoutRef.current) {
      clearTimeout(autoRefreshTimeoutRef.current)
      autoRefreshTimeoutRef.current = null
    }

    // 立即重置定时刷新倒计时
    setRefreshCountdown(10)

    setSelectedEmail(email)
    setSelectedMessage(null)
    fetchMessages(email.id)

    // 设置2秒后自动刷新（为新选择的邮箱）
    autoRefreshTimeoutRef.current = setTimeout(() => {
      refreshEmails(email.address)
    }, 2000)
  }

  // 选择消息
  const selectMessage = async (message: Message) => {
    setSelectedMessage(message)

    // 如果消息未读，标记为已读
    if (!message.isRead) {
      try {
        const response = await fetch(`/api/messages/${message.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ markAsRead: true }),
        })

        if (response.ok) {
          // 更新本地状态
          setMessages(prevMessages =>
            prevMessages.map(m =>
              m.id === message.id ? { ...m, isRead: true } : m
            )
          )

          // 更新选中的消息状态
          setSelectedMessage(prev =>
            prev?.id === message.id ? { ...prev, isRead: true } : prev
          )
        }
      } catch (error) {
        console.error('标记邮件为已读失败:', error)
      }
    }
  }

  // 导入完成处理
  const handleImportComplete = (result: any) => {
    setImportDialogOpen(false)
    fetchEmails() // 刷新邮箱列表

    toast({
      title: "导入完成",
      description: `成功导入 ${result.success} 个邮件，跳过 ${result.skipped} 个重复邮件`,
    })
  }

  // 过滤邮箱列表
  const filteredEmails = emails.filter(email => {
    const matchesSearch = email.address.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesSearch
  })

  useEffect(() => {
    fetchEmails()
  }, [])

  // 定时刷新功能
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    let countdownInterval: NodeJS.Timeout | null = null

    // 清理函数，确保切换邮箱时立即清理所有定时器
    const cleanup = () => {
      if (interval) {
        clearInterval(interval)
        interval = null
      }
      if (countdownInterval) {
        clearInterval(countdownInterval)
        countdownInterval = null
      }
    }

    if (autoRefresh && selectedEmail && !isLoadingMessages) {
      // 重置倒计时
      setRefreshCountdown(10)

      // 设置倒计时
      countdownInterval = setInterval(() => {
        setRefreshCountdown((prev) => {
          if (prev <= 1) {
            // 执行刷新（只有在不加载时才刷新）
            if (!isLoadingMessages) {
              refreshEmails(selectedEmail.address)
            }
            return 10 // 重置倒计时
          }
          return prev - 1
        })
      }, 1000)

      // 设置主刷新定时器
      interval = setInterval(() => {
        // 只有在不加载时才刷新
        if (!isLoadingMessages) {
          refreshEmails(selectedEmail.address)
        }
      }, 10000)
    } else {
      // 如果关闭自动刷新或没有选中邮箱，重置倒计时
      setRefreshCountdown(10)
    }

    return cleanup
  }, [autoRefresh, selectedEmail, isLoadingMessages])

  // 清理自动刷新定时器
  useEffect(() => {
    return () => {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current)
      }
    }
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto p-4">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                邮件管理系统
              </h1>
              <p className="text-gray-600">
                管理邮箱、查看邮件历史、提取验证码
              </p>
            </div>
            <div className="flex gap-2">
              <Link href="/email-test">
                <Button variant="outline">
                  邮件测试
                </Button>
              </Link>
              <Link href="/">
                <Button variant="outline">
                  返回主页
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <EmailStats
          totalEmails={stats.totalEmails}
          totalMessages={stats.totalMessages}
          verificationMessages={stats.verificationMessages}
          activeEmails={stats.activeEmails}
        />

        {/* 详细统计 */}
        {stats.totalEmails > 0 && (
          <DetailedEmailStats
            totalEmails={stats.totalEmails}
            totalMessages={stats.totalMessages}
            verificationMessages={stats.verificationMessages}
            activeEmails={stats.activeEmails}
          />
        )}

        {/* 邮件同步控制 */}
        <EmailSyncControl
          onSyncComplete={() => {
            fetchEmails()
            updateStats(emails)
          }}
        />



        {/* 三栏布局 */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 h-[calc(100vh-200px)]">
          {/* 左侧：邮箱列表 */}
          <div className="lg:col-span-3">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between mb-4">
                  <CardTitle className="text-lg">邮箱列表</CardTitle>
                  <div className="flex gap-2">
                    <Button
                      onClick={createNewEmail}
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      新建
                    </Button>
                    <Button
                      onClick={() => setImportDialogOpen(true)}
                      size="sm"
                      variant="outline"
                      className="border-green-200 text-green-700 hover:bg-green-50"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      导入
                    </Button>
                    <BatchEmailCreate
                      onBatchCreate={createBatchEmails}
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* 搜索和过滤 */}
                <div className="space-y-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索邮箱地址..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="flex gap-2 flex-wrap">
                    <Button
                      variant={filterType === "all" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setFilterType("all")}
                      className="text-xs"
                    >
                      全部 ({stats.totalEmails})
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={toggleSelectAll}
                      className="text-xs border-dashed"
                    >
                      {selectedEmails.size === filteredEmails.length && filteredEmails.length > 0 ? (
                        <>
                          <CheckSquare className="h-3 w-3 mr-1" />
                          取消全选
                        </>
                      ) : (
                        <>
                          <Square className="h-3 w-3 mr-1" />
                          全选
                        </>
                      )}
                    </Button>
                  </div>

                  {/* 批量操作工具栏 */}
                  {selectedEmails.size > 0 && (
                    <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg mt-3">
                      <span className="text-sm text-blue-700">
                        已选择 {selectedEmails.size} 个邮箱
                      </span>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedEmails(new Set())}
                        >
                          取消选择
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => setBatchDeleteDialog(true)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          批量删除
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[calc(100vh-300px)] overflow-y-auto">
                  {isLoading ? (
                    <div className="p-4 text-center text-gray-500">
                      <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                      加载中...
                    </div>
                  ) : filteredEmails.length > 0 ? (
                    <div className="space-y-1 p-2">
                      {filteredEmails.map((email) => (
                        <div
                          key={email.id}
                          className={`p-3 rounded-lg transition-colors ${
                            selectedEmail?.id === email.id
                              ? 'bg-blue-100 border-blue-300 border'
                              : email.hasNewMessages
                              ? 'bg-green-50 hover:bg-green-100 border-green-200 border'
                              : 'hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-1">
                            {/* 复选框 */}
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  toggleEmailSelection(email.id)
                                }}
                                className="h-6 w-6 p-0 hover:bg-gray-100"
                              >
                                {selectedEmails.has(email.id) ? (
                                  <CheckSquare className="h-4 w-4 text-blue-600" />
                                ) : (
                                  <Square className="h-4 w-4 text-gray-400" />
                                )}
                              </Button>
                              <div
                                className={`flex items-center gap-2 flex-1 cursor-pointer ${
                                  isLoadingMessages && selectedEmail?.id === email.id
                                    ? 'opacity-50 pointer-events-none'
                                    : ''
                                }`}
                                onClick={() => selectEmail(email)}
                              >
                              {isLoadingMessages && selectedEmail?.id === email.id ? (
                                <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
                              ) : email.hasNewMessages ? (
                                <Bell className="h-4 w-4 text-green-600" />
                              ) : (
                                <Mail className="h-4 w-4 text-blue-600" />
                              )}
                              <span className="text-sm font-medium truncate">
                                {email.address}
                              </span>
                              {email.hasNewMessages && email.unreadCount > 0 && (
                                <Badge
                                  variant="destructive"
                                  className="bg-red-500 hover:bg-red-600 text-white text-xs px-1.5 py-0.5 min-w-[20px] h-5 flex items-center justify-center"
                                >
                                  {email.unreadCount > 99 ? '99+' : email.unreadCount}
                                </Badge>
                              )}
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  copyToClipboard(email.address, "邮箱地址", email.id)
                                }}
                                className="h-6 w-6 p-0 hover:bg-blue-100"
                              >
                                {copiedEmails.has(email.id) ?
                                  <Check className="h-3 w-3 text-green-600" /> :
                                  <Copy className="h-3 w-3 text-gray-500" />
                                }
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  openDeleteDialog(email)
                                }}
                                className="h-6 w-6 p-0 hover:bg-red-100"
                              >
                                <X className="h-3 w-3 text-red-500" />
                              </Button>
                            </div>
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(email.createdAt).toLocaleDateString()}
                          </div>
                          <Badge variant="secondary" className="text-xs mt-1">
                            {email.type}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      <Mail className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p>{searchQuery ? "没有找到匹配的邮箱" : "暂无邮箱"}</p>
                      <p className="text-xs">
                        {searchQuery ? "尝试调整搜索条件" : "点击\"新建\"创建邮箱"}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 中间：消息列表 */}
          <div className="lg:col-span-4">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {selectedEmail ? `${selectedEmail.address} 的邮件` : '选择邮箱查看邮件'}
                  </CardTitle>
                  {selectedEmail && (
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => setAutoRefresh(!autoRefresh)}
                        size="sm"
                        variant={autoRefresh ? "default" : "outline"}
                        className={autoRefresh ? "bg-green-600 hover:bg-green-700" : ""}
                      >
                        <Clock className="h-4 w-4 mr-1" />
                        {autoRefresh ? `${refreshCountdown}s` : '定时'}
                      </Button>
                      <Button
                        onClick={() => refreshEmails(selectedEmail.address)}
                        size="sm"
                        variant="outline"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[calc(100vh-300px)]">
                  {!selectedEmail ? (
                    <div className="p-4 text-center text-gray-500">
                      <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p>请选择一个邮箱</p>
                      <p className="text-xs">查看邮件列表</p>
                    </div>
                  ) : (
                    <MessageList
                      messages={messages}
                      loading={isLoadingMessages}
                      onMessageSelect={selectMessage}
                      selectedMessageId={selectedMessage?.id}
                      onRefresh={() => selectedEmail && refreshEmails(selectedEmail.address)}
                      refreshing={false}
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：消息详情 */}
          <div className="lg:col-span-5">
            <Card className="h-full">
              <CardContent className="p-0">
                <div className="h-[calc(100vh-300px)]">
                  <MessageView
                    message={selectedMessage}
                    loading={isLoadingMessages}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({open, email: null})}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              确认删除邮箱
            </DialogTitle>
            <DialogDescription>
              您确定要删除邮箱 <span className="font-semibold text-gray-900">{deleteDialog.email?.address}</span> 吗？
              <br />
              <span className="text-red-600">此操作不可撤销，将同时删除该邮箱的所有邮件！</span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({open: false, email: null})}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteEmail}
              className="bg-red-600 hover:bg-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 批量删除确认对话框 */}
      <Dialog open={batchDeleteDialog} onOpenChange={setBatchDeleteDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              确认批量删除邮箱
            </DialogTitle>
            <DialogDescription>
              您确定要删除选中的 <span className="font-semibold text-gray-900">{selectedEmails.size}</span> 个邮箱吗？
              <br />
              <span className="text-red-600">此操作不可撤销，将同时删除这些邮箱的所有邮件！</span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setBatchDeleteDialog(false)}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={batchDeleteEmails}
              className="bg-red-600 hover:bg-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              确认删除 ({selectedEmails.size})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导入邮件对话框 */}
      <ImportEmailsDialog
        open={importDialogOpen}
        onOpenChange={setImportDialogOpen}
        onImportComplete={handleImportComplete}
      />
    </div>
  )
}
