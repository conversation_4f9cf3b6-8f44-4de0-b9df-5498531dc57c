#!/usr/bin/env node

/**
 * 身份验证系统性能测试脚本
 * 测试系统在并发访问下的性能表现
 */

const https = require('https')
const http = require('http')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 测试配置
const BASE_URL = 'http://localhost:3002'
const TEST_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
}

// HTTP请求工具
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const isHttps = urlObj.protocol === 'https:'
    const client = isHttps ? https : http
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    const req = client.request(requestOptions, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            size: data.length
          })
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
            size: data.length
          })
        }
      })
    })

    req.on('error', reject)

    if (options.body) {
      req.write(JSON.stringify(options.body))
    }

    req.end()
  })
}

// 性能测试类
class PerformanceTester {
  constructor() {
    this.results = []
    this.token = null
  }

  async login() {
    const startTime = Date.now()
    const response = await makeRequest(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      body: TEST_CREDENTIALS
    })
    const duration = Date.now() - startTime

    if (response.status === 200 && response.data.success) {
      this.token = response.data.token
      return { success: true, duration, response }
    } else {
      throw new Error(`登录失败: ${response.data.error || response.status}`)
    }
  }

  async testConcurrentLogins(concurrency = 10) {
    colorLog('blue', `🔄 测试并发登录 (${concurrency} 个并发请求)`)
    
    const promises = []
    const startTime = Date.now()

    for (let i = 0; i < concurrency; i++) {
      promises.push(
        makeRequest(`${BASE_URL}/api/auth/login`, {
          method: 'POST',
          body: TEST_CREDENTIALS
        }).then(response => ({
          requestId: i,
          status: response.status,
          success: response.data.success,
          duration: Date.now() - startTime
        })).catch(error => ({
          requestId: i,
          error: error.message,
          duration: Date.now() - startTime
        }))
      )
    }

    const results = await Promise.all(promises)
    const totalDuration = Date.now() - startTime
    
    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => r.error || !r.success).length
    
    return {
      concurrency,
      totalDuration,
      successful,
      failed,
      avgDuration: totalDuration / concurrency,
      requestsPerSecond: (concurrency / totalDuration) * 1000,
      results
    }
  }

  async testConcurrentApiAccess(concurrency = 20) {
    if (!this.token) {
      await this.login()
    }

    colorLog('blue', `🔄 测试并发API访问 (${concurrency} 个并发请求)`)
    
    const promises = []
    const startTime = Date.now()

    for (let i = 0; i < concurrency; i++) {
      promises.push(
        makeRequest(`${BASE_URL}/api/saved-data`, {
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        }).then(response => ({
          requestId: i,
          status: response.status,
          success: response.status === 200,
          duration: Date.now() - startTime,
          size: response.size
        })).catch(error => ({
          requestId: i,
          error: error.message,
          duration: Date.now() - startTime
        }))
      )
    }

    const results = await Promise.all(promises)
    const totalDuration = Date.now() - startTime
    
    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => r.error || !r.success).length
    const totalSize = results.reduce((sum, r) => sum + (r.size || 0), 0)
    
    return {
      concurrency,
      totalDuration,
      successful,
      failed,
      avgDuration: totalDuration / concurrency,
      requestsPerSecond: (concurrency / totalDuration) * 1000,
      totalDataTransferred: totalSize,
      avgResponseSize: totalSize / successful,
      results
    }
  }

  async testUnauthorizedAccess(concurrency = 15) {
    colorLog('blue', `🔄 测试未授权访问处理 (${concurrency} 个并发请求)`)
    
    const promises = []
    const startTime = Date.now()

    for (let i = 0; i < concurrency; i++) {
      promises.push(
        makeRequest(`${BASE_URL}/api/saved-data`).then(response => ({
          requestId: i,
          status: response.status,
          unauthorized: response.status === 401,
          duration: Date.now() - startTime
        })).catch(error => ({
          requestId: i,
          error: error.message,
          duration: Date.now() - startTime
        }))
      )
    }

    const results = await Promise.all(promises)
    const totalDuration = Date.now() - startTime
    
    const unauthorized = results.filter(r => r.unauthorized).length
    const errors = results.filter(r => r.error).length
    
    return {
      concurrency,
      totalDuration,
      unauthorized,
      errors,
      avgDuration: totalDuration / concurrency,
      requestsPerSecond: (concurrency / totalDuration) * 1000,
      results
    }
  }

  async testSessionValidation(iterations = 50) {
    if (!this.token) {
      await this.login()
    }

    colorLog('blue', `🔄 测试会话验证性能 (${iterations} 次迭代)`)
    
    const results = []
    const startTime = Date.now()

    for (let i = 0; i < iterations; i++) {
      const iterationStart = Date.now()
      
      try {
        const response = await makeRequest(`${BASE_URL}/api/auth/status`, {
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        })
        
        results.push({
          iteration: i,
          success: response.status === 200,
          duration: Date.now() - iterationStart
        })
      } catch (error) {
        results.push({
          iteration: i,
          error: error.message,
          duration: Date.now() - iterationStart
        })
      }
    }

    const totalDuration = Date.now() - startTime
    const successful = results.filter(r => r.success).length
    const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / iterations
    
    return {
      iterations,
      totalDuration,
      successful,
      failed: iterations - successful,
      avgDuration,
      minDuration: Math.min(...results.map(r => r.duration)),
      maxDuration: Math.max(...results.map(r => r.duration)),
      requestsPerSecond: (iterations / totalDuration) * 1000
    }
  }

  printResults(testName, results) {
    console.log('\n' + '='.repeat(60))
    colorLog('cyan', `📊 ${testName} - 测试结果`)
    console.log('='.repeat(60))
    
    if (results.concurrency) {
      colorLog('blue', `并发数: ${results.concurrency}`)
    }
    if (results.iterations) {
      colorLog('blue', `迭代次数: ${results.iterations}`)
    }
    
    colorLog('green', `成功: ${results.successful || results.unauthorized || 0}`)
    colorLog('red', `失败: ${results.failed || results.errors || 0}`)
    colorLog('magenta', `总耗时: ${results.totalDuration}ms`)
    colorLog('yellow', `平均耗时: ${Math.round(results.avgDuration)}ms`)
    colorLog('cyan', `请求/秒: ${results.requestsPerSecond.toFixed(2)}`)
    
    if (results.totalDataTransferred) {
      colorLog('blue', `数据传输: ${(results.totalDataTransferred / 1024).toFixed(2)} KB`)
      colorLog('blue', `平均响应大小: ${Math.round(results.avgResponseSize)} bytes`)
    }
    
    if (results.minDuration !== undefined) {
      colorLog('green', `最快响应: ${results.minDuration}ms`)
      colorLog('red', `最慢响应: ${results.maxDuration}ms`)
    }
  }

  async runFullPerformanceTest() {
    colorLog('cyan', '🚀 开始身份验证系统性能测试')
    console.log('='.repeat(60))

    try {
      // 1. 并发登录测试
      const loginResults = await this.testConcurrentLogins(10)
      this.printResults('并发登录测试', loginResults)

      // 2. 并发API访问测试
      const apiResults = await this.testConcurrentApiAccess(20)
      this.printResults('并发API访问测试', apiResults)

      // 3. 未授权访问测试
      const unauthorizedResults = await this.testUnauthorizedAccess(15)
      this.printResults('未授权访问处理测试', unauthorizedResults)

      // 4. 会话验证性能测试
      const sessionResults = await this.testSessionValidation(50)
      this.printResults('会话验证性能测试', sessionResults)

      // 总结
      console.log('\n' + '='.repeat(60))
      colorLog('green', '🎉 性能测试完成！')
      colorLog('cyan', '📈 系统性能总结:')
      console.log(`- 登录处理能力: ${loginResults.requestsPerSecond.toFixed(2)} 请求/秒`)
      console.log(`- API访问能力: ${apiResults.requestsPerSecond.toFixed(2)} 请求/秒`)
      console.log(`- 未授权处理: ${unauthorizedResults.requestsPerSecond.toFixed(2)} 请求/秒`)
      console.log(`- 会话验证: ${sessionResults.requestsPerSecond.toFixed(2)} 请求/秒`)

    } catch (error) {
      colorLog('red', `性能测试失败: ${error.message}`)
      throw error
    }
  }
}

// 运行性能测试
if (require.main === module) {
  const tester = new PerformanceTester()
  
  tester.runFullPerformanceTest()
    .then(() => {
      colorLog('green', '\n✅ 所有性能测试完成')
      process.exit(0)
    })
    .catch(error => {
      colorLog('red', `\n❌ 性能测试失败: ${error.message}`)
      process.exit(1)
    })
}

module.exports = { PerformanceTester }
