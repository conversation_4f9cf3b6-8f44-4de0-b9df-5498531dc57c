import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import ExcelJS from 'exceljs'
import { ServerEmailGenerator } from '@/lib/email-generator-server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { search = '', sortBy = 'created_at', sortOrder = 'desc', replaceEmails = true } = body

    // 获取所有数据（不分页）
    let query = supabase
      .from('saved_person_data')
      .select('*')

    // 搜索功能
    if (search) {
      query = query.or(`full_name.ilike.%${search}%,email.ilike.%${search}%,city.ilike.%${search}%,state.ilike.%${search}%`)
    }

    // 排序
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    const { data, error } = await query

    if (error) {
      console.error('Supabase error:', error)
      return NextResponse.json(
        { error: '获取数据失败', details: error.message },
        { status: 500 }
      )
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: '没有数据可导出' },
        { status: 400 }
      )
    }

    // 如果需要替换邮箱，生成新的邮箱地址
    let generatedEmails: any[] = []
    if (replaceEmails) {
      const emailGenerator = new ServerEmailGenerator()
      generatedEmails = emailGenerator.generateBatchEmails(data.length)
    }

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook()

    // 设置工作簿属性
    workbook.creator = '美国地址生成器'
    workbook.lastModifiedBy = '美国地址生成器'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建主要数据工作表
    const worksheet = workbook.addWorksheet('已保存数据')
    
    // 定义列标题和对应的数据字段 - 重要字段优先
    const columns = [
      { header: '邮箱', key: 'email', width: 30 },
      { header: '密码', key: 'password', width: 20 },
      { header: '全名', key: 'full_name', width: 20 },
      { header: '名', key: 'first_name', width: 15 },
      { header: '姓', key: 'last_name', width: 15 },
      { header: '性别', key: 'gender', width: 10 },
      { header: '生日', key: 'birthday', width: 15 },
      { header: '社会安全号', key: 'ssn', width: 18 },
      { header: '完整地址', key: 'full_address', width: 50 },
      { header: '街道', key: 'street', width: 30 },
      { header: '城市', key: 'city', width: 20 },
      { header: '州', key: 'state', width: 15 },
      { header: '州全名', key: 'state_full_name', width: 25 },
      { header: '邮编', key: 'zip_code', width: 12 },
      // 其他字段
      { header: '序号', key: 'index', width: 8 },
      { header: '创建时间', key: 'created_at', width: 20 },
      { header: '电话', key: 'phone', width: 18 },
      { header: '称谓', key: 'title', width: 10 },
      { header: '发色', key: 'hair_color', width: 12 },
      { header: '国家', key: 'country', width: 12 },
      { header: '职业', key: 'occupation', width: 20 },
      { header: '公司', key: 'company', width: 30 },
      { header: '公司规模', key: 'company_size', width: 15 },
      { header: '行业', key: 'industry', width: 20 },
      { header: '工作状态', key: 'status', width: 15 },
      { header: '薪资', key: 'salary', width: 15 },
      { header: '信用卡类型', key: 'card_type', width: 15 },
      { header: '信用卡号', key: 'card_number', width: 20 },
      { header: 'CVV', key: 'cvv', width: 8 },
      { header: '到期日期', key: 'expiry', width: 12 },
      { header: '用户名', key: 'username', width: 20 },
      { header: '身高', key: 'height', width: 12 },
      { header: '体重', key: 'weight', width: 12 },
      { header: '血型', key: 'blood_type', width: 10 },
      { header: '操作系统', key: 'os', width: 20 },
      { header: 'GUID', key: 'guid', width: 40 },
      { header: '用户代理', key: 'user_agent', width: 50 },
      { header: '教育程度', key: 'education', width: 20 },
      { header: '个人网站', key: 'website', width: 30 },
      { header: '安全问题', key: 'security_question', width: 40 },
      { header: '安全答案', key: 'security_answer', width: 30 },
      { header: '高中名称', key: 'school_name', width: 30 },
      { header: '高中城市', key: 'school_city', width: 20 },
      { header: '高中州', key: 'school_state', width: 15 },
      { header: '大学名称', key: 'university_name', width: 30 },
      { header: '大学城市', key: 'university_city', width: 20 },
      { header: '大学州', key: 'university_state', width: 15 },
    ]

    // 设置列
    worksheet.columns = columns

    // 设置标题行样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' }
    headerRow.height = 25

    // 添加数据行
    data.forEach((item, index) => {
      // 使用生成的邮箱或原始邮箱
      const emailToUse = replaceEmails && generatedEmails[index]
        ? generatedEmails[index].email
        : item.email
      const passwordToUse = replaceEmails && generatedEmails[index]
        ? generatedEmails[index].password
        : item.password

      const rowData = {
        email: emailToUse,
        password: passwordToUse,
        full_name: item.full_name,
        first_name: item.first_name,
        last_name: item.last_name,
        gender: item.gender,
        birthday: item.birthday,
        ssn: item.ssn,
        full_address: item.full_address,
        street: item.street,
        city: item.city,
        state: item.state,
        state_full_name: item.state_full_name,
        zip_code: item.zip_code,
        // 其他字段
        index: index + 1,
        created_at: new Date(item.created_at).toLocaleString('zh-CN'),
        phone: item.phone,
        title: item.title,
        hair_color: item.hair_color,
        country: item.country,
        occupation: item.occupation,
        company: item.company,
        company_size: item.company_size,
        industry: item.industry,
        status: item.status,
        salary: item.salary,
        card_type: item.card_type,
        card_number: item.card_number,
        cvv: item.cvv,
        expiry: item.expiry,
        username: item.username,
        height: item.height,
        weight: item.weight,
        blood_type: item.blood_type,
        os: item.os,
        guid: item.guid,
        user_agent: item.user_agent,
        education: item.education,
        website: item.website,
        security_question: item.security_question,
        security_answer: item.security_answer,
        school_name: item.school_name,
        school_city: item.school_city,
        school_state: item.school_state,
        university_name: item.university_name,
        university_city: item.university_city,
        university_state: item.university_state,
      }
      
      const row = worksheet.addRow(rowData)
      
      // 设置数据行样式
      row.alignment = { vertical: 'middle', wrapText: true }
      row.height = 20
      
      // 交替行颜色
      if (index % 2 === 1) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F8F9FA' }
        }
      }
    })

    // 添加边框
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 生成Excel文件
    const buffer = await workbook.xlsx.writeBuffer()

    // 返回文件
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="已保存数据_${data.length}条_${new Date().toISOString().split('T')[0]}.xlsx"`,
      },
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: '服务器错误', details: error instanceof Error ? error.message : '未知错误' },
      { status: 500 }
    )
  }
}
