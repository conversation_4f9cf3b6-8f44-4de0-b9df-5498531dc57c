"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Mail, Shield, Clock, TrendingUp, Users, MessageSquare } from "lucide-react"

interface EmailStatsProps {
  totalEmails: number
  totalMessages: number
  verificationMessages: number
  activeEmails: number
}

export function EmailStats({ 
  totalEmails, 
  totalMessages, 
  verificationMessages, 
  activeEmails 
}: EmailStatsProps) {
  const verificationRate = totalMessages > 0 ? (verificationMessages / totalMessages * 100) : 0
  const messagesPerEmail = totalEmails > 0 ? (totalMessages / totalEmails) : 0

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {/* 总邮箱数 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Mail className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">总邮箱数</p>
              <p className="text-2xl font-bold text-gray-900">{totalEmails}</p>
              <div className="flex items-center gap-1 mt-1">
                <Badge variant="secondary" className="text-xs">
                  活跃: {activeEmails}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 总消息数 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <MessageSquare className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">总消息数</p>
              <p className="text-2xl font-bold text-gray-900">{totalMessages}</p>
              <div className="flex items-center gap-1 mt-1">
                <Badge variant="outline" className="text-xs">
                  平均: {messagesPerEmail.toFixed(1)}/邮箱
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 验证码消息 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Shield className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">验证码消息</p>
              <p className="text-2xl font-bold text-gray-900">{verificationMessages}</p>
              <div className="flex items-center gap-1 mt-1">
                <Badge 
                  variant={verificationRate > 50 ? "default" : "secondary"} 
                  className="text-xs"
                >
                  {verificationRate.toFixed(1)}%
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 系统状态 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">系统状态</p>
              <p className="text-2xl font-bold text-gray-900">
                {totalEmails > 0 ? "运行中" : "待启动"}
              </p>
              <div className="flex items-center gap-1 mt-1">
                <div className={`w-2 h-2 rounded-full ${totalEmails > 0 ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className="text-xs text-gray-500">
                  {totalEmails > 0 ? "正常" : "无数据"}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// 详细统计组件
export function DetailedEmailStats({ 
  totalEmails, 
  totalMessages, 
  verificationMessages, 
  activeEmails 
}: EmailStatsProps) {
  const inactiveEmails = totalEmails - activeEmails
  const regularMessages = totalMessages - verificationMessages
  const verificationRate = totalMessages > 0 ? (verificationMessages / totalMessages * 100) : 0

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          详细统计
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 邮箱分析 */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              邮箱分析
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">活跃邮箱</span>
                <Badge variant="default">{activeEmails}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">非活跃邮箱</span>
                <Badge variant="secondary">{inactiveEmails}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">活跃率</span>
                <Badge variant="outline">
                  {totalEmails > 0 ? ((activeEmails / totalEmails) * 100).toFixed(1) : 0}%
                </Badge>
              </div>
            </div>
          </div>

          {/* 消息分析 */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-green-600" />
              消息分析
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">普通消息</span>
                <Badge variant="default">{regularMessages}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">验证码消息</span>
                <Badge variant="secondary">{verificationMessages}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">验证码率</span>
                <Badge variant="outline">{verificationRate.toFixed(1)}%</Badge>
              </div>
            </div>
          </div>

          {/* 效率分析 */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-600" />
              效率分析
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">平均消息/邮箱</span>
                <Badge variant="default">
                  {totalEmails > 0 ? (totalMessages / totalEmails).toFixed(1) : 0}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">验证码/邮箱</span>
                <Badge variant="secondary">
                  {totalEmails > 0 ? (verificationMessages / totalEmails).toFixed(1) : 0}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">系统利用率</span>
                <Badge variant="outline">
                  {totalEmails > 0 && totalMessages > 0 ? "高" : "低"}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
