@echo off
echo.
echo ==========================================
echo Docker Build Troubleshooting Tool
echo ==========================================
echo.

echo [INFO] Checking Docker environment...

REM Check Docker installation
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed or not in PATH
    echo [INFO] Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo [OK] Docker is installed
docker --version

REM Check Docker daemon
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker daemon is not running
    echo [INFO] Please start Docker Desktop
    pause
    exit /b 1
)

echo [OK] Docker daemon is running

echo.
echo [INFO] Checking project files...

REM Check essential files
if not exist "package.json" (
    echo [ERROR] package.json not found
    echo [INFO] Please run this script from project root directory
    pause
    exit /b 1
)
echo [OK] package.json found

if not exist "Dockerfile" (
    echo [ERROR] Dockerfile not found
    pause
    exit /b 1
)
echo [OK] Dockerfile found

if exist "pnpm-lock.yaml" (
    echo [OK] pnpm-lock.yaml found
    set PACKAGE_MANAGER=pnpm
) else if exist "package-lock.json" (
    echo [OK] package-lock.json found
    set PACKAGE_MANAGER=npm
) else (
    echo [WARN] No lock file found, will use npm
    set PACKAGE_MANAGER=npm
)

echo [INFO] Package manager: %PACKAGE_MANAGER%

echo.
echo [INFO] Testing Docker build options...

echo.
echo ==========================================
echo Build Option 1: Original Dockerfile (pnpm)
echo ==========================================

echo [INFO] Attempting to build with original Dockerfile...
docker build -t us-fake-gen-ui:test-pnpm . 2>build-error-pnpm.log
if %errorlevel% equ 0 (
    echo [SUCCESS] Build with pnpm successful!
    echo [INFO] You can now run: docker run -p 3000:3000 us-fake-gen-ui:test-pnpm
    goto :success
) else (
    echo [ERROR] Build with pnpm failed
    echo [INFO] Error log saved to: build-error-pnpm.log
    echo [INFO] Trying alternative approach...
)

echo.
echo ==========================================
echo Build Option 2: NPM Dockerfile
echo ==========================================

if exist "Dockerfile.npm" (
    echo [INFO] Attempting to build with npm Dockerfile...
    docker build -f Dockerfile.npm -t us-fake-gen-ui:test-npm . 2>build-error-npm.log
    if %errorlevel% equ 0 (
        echo [SUCCESS] Build with npm successful!
        echo [INFO] You can now run: docker run -p 3000:3000 us-fake-gen-ui:test-npm
        goto :success
    ) else (
        echo [ERROR] Build with npm also failed
        echo [INFO] Error log saved to: build-error-npm.log
    )
) else (
    echo [ERROR] Dockerfile.npm not found
)

echo.
echo ==========================================
echo Build Option 3: Test Compose
echo ==========================================

if exist "docker-compose.test.yml" (
    echo [INFO] Attempting to build with test compose...
    docker-compose -f docker-compose.test.yml build 2>build-error-compose.log
    if %errorlevel% equ 0 (
        echo [SUCCESS] Build with compose successful!
        echo [INFO] You can now run: docker-compose -f docker-compose.test.yml up
        goto :success
    ) else (
        echo [ERROR] Build with compose also failed
        echo [INFO] Error log saved to: build-error-compose.log
    )
) else (
    echo [ERROR] docker-compose.test.yml not found
)

echo.
echo ==========================================
echo All build attempts failed
echo ==========================================

echo [ERROR] All Docker build attempts have failed
echo.
echo [INFO] Troubleshooting suggestions:
echo.
echo 1. Check error logs:
if exist "build-error-pnpm.log" echo    - build-error-pnpm.log
if exist "build-error-npm.log" echo    - build-error-npm.log
if exist "build-error-compose.log" echo    - build-error-compose.log
echo.
echo 2. Common solutions:
echo    - Ensure stable internet connection
echo    - Clear Docker cache: docker system prune -a
echo    - Increase Docker memory limit in Docker Desktop settings
echo    - Try building without cache: docker build --no-cache
echo.
echo 3. Alternative deployment:
echo    - Use local development: npm run dev
echo    - Deploy to cloud platforms: Vercel, Netlify, etc.
echo.

goto :end

:success
echo.
echo ==========================================
echo Build Successful!
echo ==========================================
echo.
echo [SUCCESS] Docker image built successfully
echo.
echo [INFO] Next steps:
echo 1. Test the container:
echo    docker run -p 3000:3000 [image-name]
echo.
echo 2. Access the application:
echo    http://localhost:3000
echo.
echo 3. For production deployment:
echo    docker-compose up -d
echo.

:end
echo [INFO] Troubleshooting complete
pause
