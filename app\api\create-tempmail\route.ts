import { NextRequest, NextResponse } from 'next/server'
import { tempMailService } from '@/lib/tempmail-service'

/**
 * 创建临时邮箱API
 * 基于tempmail.plus服务创建真正可用的临时邮箱
 */

export async function POST(request: NextRequest) {
  try {
    // 创建临时邮箱账户
    const account = await tempMailService.createTempMail()
    
    // 测试邮箱是否可用
    const testResult = await tempMailService.testEmail(account.email)
    
    return NextResponse.json({
      success: true,
      data: {
        ...account,
        testResult
      }
    })
  } catch (error) {
    console.error('创建临时邮箱失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '创建临时邮箱时发生未知错误'
    }, { status: 500 })
  }
}

/**
 * 获取邮箱收件箱
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    const epin = searchParams.get('epin')
    
    if (!email) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址不能为空'
      }, { status: 400 })
    }
    
    const inbox = await tempMailService.getInbox(email, epin || undefined)
    
    return NextResponse.json({
      success: true,
      data: {
        email,
        inbox,
        count: inbox.length
      }
    })
  } catch (error) {
    console.error('获取收件箱失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取收件箱时发生未知错误'
    }, { status: 500 })
  }
}
