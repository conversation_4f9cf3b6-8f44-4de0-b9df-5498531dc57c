#!/bin/bash

# 创建Linux系统服务脚本
# 将美国地址生成器注册为系统服务，支持开机自启

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=========================================="
echo "  创建系统服务"
echo "==========================================${NC}"

# 获取当前用户和项目路径
CURRENT_USER=$(whoami)
PROJECT_PATH=$(pwd)
SERVICE_NAME="us-fake-gen"

echo -e "${BLUE}[INFO]${NC} 当前用户: $CURRENT_USER"
echo -e "${BLUE}[INFO]${NC} 项目路径: $PROJECT_PATH"

# 检查是否为root用户或有sudo权限
if [[ $EUID -eq 0 ]]; then
    SUDO=""
elif sudo -n true 2>/dev/null; then
    SUDO="sudo"
else
    echo -e "${YELLOW}[WARNING]${NC} 需要sudo权限来创建系统服务"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo -e "${YELLOW}[ERROR]${NC} Node.js未安装，请先安装Node.js"
    exit 1
fi

NODE_PATH=$(which node)
NPM_PATH=$(which npm)

echo -e "${BLUE}[INFO]${NC} Node.js路径: $NODE_PATH"
echo -e "${BLUE}[INFO]${NC} NPM路径: $NPM_PATH"

# 创建systemd服务文件
echo -e "${BLUE}[INFO]${NC} 创建systemd服务文件..."

$SUDO tee /etc/systemd/system/${SERVICE_NAME}.service > /dev/null << EOF
[Unit]
Description=美国地址生成器 - US Fake Gen UI
Documentation=https://github.com/your-repo/us-fake-gen-ui
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$PROJECT_PATH
Environment=NODE_ENV=production
Environment=PATH=/usr/bin:/usr/local/bin:$PATH
ExecStart=$NODE_PATH $PROJECT_PATH/node_modules/.bin/next start -p 3002
ExecReload=/bin/kill -s HUP \$MAINPID
KillMode=mixed
KillSignal=SIGINT
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$PROJECT_PATH
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

echo -e "${GREEN}[SUCCESS]${NC} 系统服务文件已创建: /etc/systemd/system/${SERVICE_NAME}.service"

# 重新加载systemd
echo -e "${BLUE}[INFO]${NC} 重新加载systemd..."
$SUDO systemctl daemon-reload

# 启用服务
echo -e "${BLUE}[INFO]${NC} 启用服务..."
$SUDO systemctl enable ${SERVICE_NAME}

echo -e "${GREEN}[SUCCESS]${NC} 服务已启用，开机自启动已配置"

# 询问是否立即启动服务
read -p "是否立即启动服务? (y/n): " start_now
if [[ $start_now =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}[INFO]${NC} 启动服务..."
    $SUDO systemctl start ${SERVICE_NAME}
    
    # 等待服务启动
    sleep 3
    
    # 检查服务状态
    if $SUDO systemctl is-active --quiet ${SERVICE_NAME}; then
        echo -e "${GREEN}[SUCCESS]${NC} 服务启动成功!"
        echo
        echo -e "${BLUE}[INFO]${NC} 服务状态:"
        $SUDO systemctl status ${SERVICE_NAME} --no-pager -l
        echo
        echo -e "${BLUE}[INFO]${NC} 访问地址: http://localhost:3002"
    else
        echo -e "${YELLOW}[ERROR]${NC} 服务启动失败"
        echo -e "${BLUE}[INFO]${NC} 查看错误日志:"
        $SUDO journalctl -u ${SERVICE_NAME} --no-pager -l
    fi
fi

echo
echo -e "${BLUE}=========================================="
echo "  服务管理命令"
echo "==========================================${NC}"
echo
echo "启动服务:   sudo systemctl start ${SERVICE_NAME}"
echo "停止服务:   sudo systemctl stop ${SERVICE_NAME}"
echo "重启服务:   sudo systemctl restart ${SERVICE_NAME}"
echo "查看状态:   sudo systemctl status ${SERVICE_NAME}"
echo "查看日志:   sudo journalctl -u ${SERVICE_NAME} -f"
echo "禁用服务:   sudo systemctl disable ${SERVICE_NAME}"
echo "删除服务:   sudo rm /etc/systemd/system/${SERVICE_NAME}.service && sudo systemctl daemon-reload"
echo
echo -e "${GREEN}[SUCCESS]${NC} 系统服务创建完成!"
