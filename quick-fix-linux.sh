#!/bin/bash

# 快速修复Linux Docker构建问题

echo "🔧 快速修复Linux Docker构建问题..."

# 1. 生成package-lock.json
echo "📦 生成package-lock.json..."
npm install --package-lock-only

# 2. 创建.npmrc配置
echo "⚙️ 创建npm配置..."
cat > .npmrc << 'EOF'
registry=https://registry.npmjs.org/
legacy-peer-deps=true
audit=false
fund=false
EOF

# 3. 创建简化的Dockerfile
echo "🐳 创建修复版Dockerfile..."
cat > Dockerfile.simple << 'EOF'
FROM node:20-alpine

RUN apk add --no-cache libc6-compat python3 make g++ git curl

WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci --only=production --ignore-scripts

COPY package.json ./
RUN npm install --ignore-scripts

COPY . .
RUN npm run build

EXPOSE 3002
ENV PORT=3002

CMD ["npm", "start"]
EOF

# 4. 测试构建
echo "🧪 测试构建..."
if docker build -f Dockerfile.simple -t us-fake-gen-ui:simple . ; then
    echo "✅ 构建成功！"
    echo "🚀 运行命令: docker run -p 3000:3002 us-fake-gen-ui:simple"
else
    echo "❌ 构建失败，请检查错误信息"
fi
