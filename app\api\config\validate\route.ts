import { NextRequest, NextResponse } from 'next/server'
import { validateConfig, getSafeConfig, generateSecureKey, checkPasswordStrength } from '@/lib/config'
import { verifyJWT, extractTokenFromRequest } from '@/lib/jwt'
import { isTokenBlacklisted } from '@/lib/token-blacklist'

/**
 * 配置验证API
 * 提供配置状态检查和安全建议
 */

/**
 * 验证用户身份（仅管理员可访问）
 */
async function validateAuthentication(request: NextRequest): Promise<boolean> {
  try {
    // 从Authorization header获取token
    const authToken = extractTokenFromRequest(request)
    if (!authToken) {
      // 从cookie获取token
      const cookieToken = request.cookies.get('auth-token')?.value
      if (!cookieToken) {
        return false
      }
      
      // 验证JWT token
      const payload = await verifyJWT(cookieToken)
      if (!payload) {
        return false
      }
      
      // 检查token是否在黑名单中
      if (isTokenBlacklisted(cookieToken)) {
        return false
      }
      
      return true
    }
    
    // 验证JWT token
    const payload = await verifyJWT(authToken)
    if (!payload) {
      return false
    }
    
    // 检查token是否在黑名单中
    if (isTokenBlacklisted(authToken)) {
      return false
    }
    
    return true
  } catch (error) {
    console.error('身份验证失败:', error)
    return false
  }
}

/**
 * 获取配置验证状态
 */
export async function GET(request: NextRequest) {
  try {
    // 验证身份
    const isAuthenticated = await validateAuthentication(request)
    if (!isAuthenticated) {
      return NextResponse.json({
        success: false,
        error: '需要管理员权限访问',
        code: 'UNAUTHORIZED'
      }, { status: 401 })
    }

    // 验证配置
    const validation = validateConfig()
    const safeConfig = getSafeConfig()

    return NextResponse.json({
      success: true,
      data: {
        validation: {
          isValid: validation.isValid,
          errors: validation.errors,
          warnings: validation.warnings,
          summary: validation.isValid 
            ? '配置验证通过' 
            : `发现 ${validation.errors.length} 个错误，${validation.warnings.length} 个警告`
        },
        config: safeConfig,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('配置验证失败:', error)
    return NextResponse.json({
      success: false,
      error: '配置验证失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 生成安全配置建议
 */
export async function POST(request: NextRequest) {
  try {
    // 验证身份
    const isAuthenticated = await validateAuthentication(request)
    if (!isAuthenticated) {
      return NextResponse.json({
        success: false,
        error: '需要管理员权限访问',
        code: 'UNAUTHORIZED'
      }, { status: 401 })
    }

    const body = await request.json()
    const { action, password } = body

    if (action === 'generate-jwt-secret') {
      // 生成新的JWT密钥
      const newSecret = generateSecureKey(64)
      
      return NextResponse.json({
        success: true,
        data: {
          jwtSecret: newSecret,
          length: newSecret.length,
          suggestion: '请将此密钥复制到 .env.local 文件中的 JWT_SECRET 变量'
        }
      })
    }

    if (action === 'generate-password') {
      // 生成强密码
      const newPassword = generateSecureKey(16)
      const strength = checkPasswordStrength(newPassword)
      
      return NextResponse.json({
        success: true,
        data: {
          password: newPassword,
          strength: {
            score: strength.score,
            level: strength.score >= 4 ? '强' : strength.score >= 3 ? '中' : '弱',
            feedback: strength.feedback
          },
          suggestion: '请将此密码复制到 .env.local 文件中的 ADMIN_PASSWORD 变量'
        }
      })
    }

    if (action === 'check-password-strength' && password) {
      // 检查密码强度
      const strength = checkPasswordStrength(password)
      
      return NextResponse.json({
        success: true,
        data: {
          strength: {
            score: strength.score,
            level: strength.score >= 4 ? '强' : strength.score >= 3 ? '中' : '弱',
            feedback: strength.feedback,
            recommendations: strength.score < 4 ? [
              '使用至少8个字符',
              '包含大写和小写字母',
              '包含数字',
              '包含特殊字符(!@#$%^&*等)'
            ] : ['密码强度良好']
          }
        }
      })
    }

    return NextResponse.json({
      success: false,
      error: '不支持的操作',
      supportedActions: ['generate-jwt-secret', 'generate-password', 'check-password-strength']
    }, { status: 400 })

  } catch (error) {
    console.error('配置操作失败:', error)
    return NextResponse.json({
      success: false,
      error: '配置操作失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
