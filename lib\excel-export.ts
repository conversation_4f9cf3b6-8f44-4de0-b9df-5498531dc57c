import ExcelJS from 'exceljs'
import { PersonData, SchoolData, UniversityData } from '@/app/page'
import { generateBatchEmails, GeneratedEmail } from './email-generator'

interface ExportData {
  personData: PersonData
  schoolData: SchoolData | null
  universityData: UniversityData | null
}

export const exportToExcel = async (
  data: ExportData | ExportData[],
  filename?: string,
  replaceEmails: boolean = true
) => {
  const workbook = new ExcelJS.Workbook()
  
  // 设置工作簿属性
  workbook.creator = '美国地址生成器'
  workbook.lastModifiedBy = '美国地址生成器'
  workbook.created = new Date()
  workbook.modified = new Date()

  // 判断是单个数据还是批量数据
  const dataArray = Array.isArray(data) ? data : [data]

  // 如果需要替换邮箱，生成新的邮箱地址
  let generatedEmails: GeneratedEmail[] = []
  if (replaceEmails) {
    generatedEmails = generateBatchEmails(dataArray.length)
  }

  // 创建主要数据工作表
  const worksheet = workbook.addWorksheet('个人信息')
  
  // 定义列标题和对应的数据字段 - 重要字段优先
  const columns = [
    { header: '邮箱', key: 'email', width: 30 },
    { header: '密码', key: 'password', width: 20 },
    { header: '全名', key: 'fullName', width: 20 },
    { header: '名', key: 'firstName', width: 15 },
    { header: '姓', key: 'lastName', width: 15 },
    { header: '性别', key: 'gender', width: 10 },
    { header: '生日', key: 'birthday', width: 15 },
    { header: '社会安全号', key: 'ssn', width: 18 },
    { header: '完整地址', key: 'fullAddress', width: 50 },
    { header: '街道', key: 'street', width: 30 },
    { header: '城市', key: 'city', width: 20 },
    { header: '州', key: 'state', width: 15 },
    { header: '州全名', key: 'stateFullName', width: 25 },
    { header: '邮编', key: 'zipCode', width: 12 },
    // 其他字段
    { header: '序号', key: 'index', width: 8 },
    { header: '电话', key: 'phone', width: 18 },
    { header: '称谓', key: 'title', width: 10 },
    { header: '发色', key: 'hairColor', width: 12 },
    { header: '国家', key: 'country', width: 12 },
    { header: '职业', key: 'occupation', width: 20 },
    { header: '公司', key: 'company', width: 30 },
    { header: '公司规模', key: 'companySize', width: 15 },
    { header: '行业', key: 'industry', width: 20 },
    { header: '工作状态', key: 'status', width: 15 },
    { header: '薪资', key: 'salary', width: 15 },
    { header: '信用卡类型', key: 'cardType', width: 15 },
    { header: '信用卡号', key: 'cardNumber', width: 20 },
    { header: 'CVV', key: 'cvv', width: 8 },
    { header: '到期日期', key: 'expiry', width: 12 },
    { header: '用户名', key: 'username', width: 20 },
    { header: '身高', key: 'height', width: 12 },
    { header: '体重', key: 'weight', width: 12 },
    { header: '血型', key: 'bloodType', width: 10 },
    { header: '操作系统', key: 'os', width: 20 },
    { header: 'GUID', key: 'guid', width: 40 },
    { header: '用户代理', key: 'userAgent', width: 50 },
    { header: '教育程度', key: 'education', width: 20 },
    { header: '个人网站', key: 'website', width: 30 },
    { header: '安全问题', key: 'securityQuestion', width: 40 },
    { header: '安全答案', key: 'securityAnswer', width: 30 },
  ]

  // 设置列
  worksheet.columns = columns

  // 设置标题行样式
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '4472C4' }
  }
  headerRow.alignment = { horizontal: 'center', vertical: 'middle' }
  headerRow.height = 25

  // 添加数据行
  dataArray.forEach((item, index) => {
    // 使用生成的邮箱或原始邮箱
    const emailToUse = replaceEmails && generatedEmails[index]
      ? generatedEmails[index].email
      : item.personData.email
    const passwordToUse = replaceEmails && generatedEmails[index]
      ? generatedEmails[index].password
      : item.personData.password

    const rowData = {
      email: emailToUse,
      password: passwordToUse,
      fullName: item.personData.fullName,
      firstName: item.personData.firstName,
      lastName: item.personData.lastName,
      gender: item.personData.gender,
      birthday: item.personData.birthday,
      ssn: item.personData.ssn,
      fullAddress: item.personData.fullAddress,
      street: item.personData.street,
      city: item.personData.city,
      state: item.personData.state,
      stateFullName: item.personData.stateFullName,
      zipCode: item.personData.zipCode,
      // 其他字段
      index: index + 1,
      phone: item.personData.phone,
      title: item.personData.title,
      hairColor: item.personData.hairColor,
      country: item.personData.country,
      occupation: item.personData.occupation,
      company: item.personData.company,
      companySize: item.personData.companySize,
      industry: item.personData.industry,
      status: item.personData.status,
      salary: item.personData.salary,
      cardType: item.personData.cardType,
      cardNumber: item.personData.cardNumber,
      cvv: item.personData.cvv,
      expiry: item.personData.expiry,
      username: item.personData.username,
      height: item.personData.height,
      weight: item.personData.weight,
      bloodType: item.personData.bloodType,
      os: item.personData.os,
      guid: item.personData.guid,
      userAgent: item.personData.userAgent,
      education: item.personData.education,
      website: item.personData.website,
      securityQuestion: item.personData.securityQuestion,
      securityAnswer: item.personData.securityAnswer,
    }
    
    const row = worksheet.addRow(rowData)
    
    // 设置数据行样式
    row.alignment = { vertical: 'middle', wrapText: true }
    row.height = 20
    
    // 交替行颜色
    if (index % 2 === 1) {
      row.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'F8F9FA' }
      }
    }
  })

  // 添加边框
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    })
  })

  // 如果有学校数据，创建学校信息工作表
  const schoolDataArray = dataArray.filter(item => item.schoolData)
  if (schoolDataArray.length > 0) {
    const schoolWorksheet = workbook.addWorksheet('高中信息')
    
    const schoolColumns = [
      { header: '序号', key: 'index', width: 8 },
      { header: '学生姓名', key: 'studentName', width: 20 },
      { header: '学校名称', key: 'name', width: 40 },
      { header: '学校ID', key: 'ncesid', width: 15 },
      { header: '邮编', key: 'zip', width: 12 },
      { header: '网站', key: 'website', width: 40 },
      { header: '地址', key: 'address', width: 40 },
      { header: '城市', key: 'city', width: 20 },
      { header: '州', key: 'state', width: 15 },
      { header: '电话', key: 'telephone', width: 18 },
      { header: '年级范围', key: 'grades', width: 15 },
    ]

    schoolWorksheet.columns = schoolColumns

    // 设置标题行样式
    const schoolHeaderRow = schoolWorksheet.getRow(1)
    schoolHeaderRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    schoolHeaderRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '70AD47' }
    }
    schoolHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' }
    schoolHeaderRow.height = 25

    // 添加学校数据
    schoolDataArray.forEach((item, index) => {
      if (item.schoolData) {
        const schoolRowData = {
          index: index + 1,
          studentName: item.personData.fullName,
          name: item.schoolData.name,
          ncesid: item.schoolData.ncesid,
          zip: item.schoolData.zip,
          website: item.schoolData.website,
          address: item.schoolData.address,
          city: item.schoolData.city,
          state: item.schoolData.state,
          telephone: item.schoolData.telephone,
          grades: `${item.schoolData.st_grade}-${item.schoolData.end_grade}年级`,
        }
        
        const row = schoolWorksheet.addRow(schoolRowData)
        row.alignment = { vertical: 'middle', wrapText: true }
        row.height = 20
        
        if (index % 2 === 1) {
          row.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F8F9FA' }
          }
        }
      }
    })

    // 添加边框
    schoolWorksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })
  }

  // 如果有大学数据，创建大学信息工作表
  const universityDataArray = dataArray.filter(item => item.universityData)
  if (universityDataArray.length > 0) {
    const universityWorksheet = workbook.addWorksheet('大学信息')
    
    const universityColumns = [
      { header: '序号', key: 'index', width: 8 },
      { header: '学生姓名', key: 'studentName', width: 20 },
      { header: '大学名称', key: 'name', width: 40 },
      { header: '大学ID', key: 'ipedsid', width: 15 },
      { header: '邮编', key: 'zip', width: 12 },
      { header: '网站', key: 'website', width: 40 },
      { header: '地址', key: 'address', width: 40 },
      { header: '城市', key: 'city', width: 20 },
      { header: '州', key: 'state', width: 15 },
      { header: '电话', key: 'telephone', width: 18 },
      { header: '类型', key: 'type', width: 15 },
    ]

    universityWorksheet.columns = universityColumns

    // 设置标题行样式
    const universityHeaderRow = universityWorksheet.getRow(1)
    universityHeaderRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    universityHeaderRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E74C3C' }
    }
    universityHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' }
    universityHeaderRow.height = 25

    // 添加大学数据
    universityDataArray.forEach((item, index) => {
      if (item.universityData) {
        const typeMap: Record<string, string> = {
          "1": "公立大学",
          "2": "私立非营利大学",
          "3": "私立营利大学",
        }

        const universityRowData = {
          index: index + 1,
          studentName: item.personData.fullName,
          name: item.universityData.name,
          ipedsid: item.universityData.ipedsid,
          zip: item.universityData.zip,
          website: item.universityData.website,
          address: item.universityData.address,
          city: item.universityData.city,
          state: item.universityData.state,
          telephone: item.universityData.telephone,
          type: typeMap[item.universityData.type] || item.universityData.type,
        }
        
        const row = universityWorksheet.addRow(universityRowData)
        row.alignment = { vertical: 'middle', wrapText: true }
        row.height = 20
        
        if (index % 2 === 1) {
          row.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F8F9FA' }
          }
        }
      }
    })

    // 添加边框
    universityWorksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })
  }

  // 生成文件名
  const defaultFilename = Array.isArray(data) 
    ? `批量数据_${data.length}条_${new Date().toISOString().split('T')[0]}.xlsx`
    : `个人数据_${data.personData.fullName}_${new Date().toISOString().split('T')[0]}.xlsx`
  
  const finalFilename = filename || defaultFilename

  // 导出文件
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = finalFilename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
