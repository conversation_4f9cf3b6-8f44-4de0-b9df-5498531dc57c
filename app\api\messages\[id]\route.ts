import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

/**
 * 更新消息状态（如标记为已读）
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { markAsRead } = body

    if (markAsRead === true) {
      const { data, error } = await supabase
        .from('messages')
        .update({ is_read: true })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('标记消息为已读失败:', error)
        return NextResponse.json({
          success: false,
          error: '标记消息为已读失败: ' + error.message
        }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        data: {
          id: data.id,
          emailId: data.email_id,
          fromAddress: data.from_address,
          toAddress: data.to_address,
          subject: data.subject,
          textContent: data.text_content,
          htmlContent: data.html_content,
          receivedAt: data.received_at,
          isRead: data.is_read,
          hasVerificationCode: data.has_verification_code,
          verificationCode: data.verification_code
        },
        message: '消息已标记为已读'
      })
    }

    return NextResponse.json({
      success: false,
      error: '无效的操作'
    }, { status: 400 })

  } catch (error) {
    console.error('更新消息失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '更新消息失败'
    }, { status: 500 })
  }
}

/**
 * 删除消息
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const { error } = await supabase
      .from('messages')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('删除消息失败:', error)
      return NextResponse.json({
        success: false,
        error: '删除消息失败: ' + error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: '消息已删除'
    })

  } catch (error) {
    console.error('删除消息失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除消息失败'
    }, { status: 500 })
  }
}
