import { NextRequest, NextResponse } from 'next/server'

/**
 * IMAP邮件API - 直接从IMAP服务器获取邮件内容
 * 这个API用于邮件测试页面，直接连接IMAP服务器获取邮件，不查询数据库
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tempEmail = searchParams.get('tempEmail')

    if (!tempEmail) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址不能为空'
      }, { status: 400 })
    }

    console.log(`直接从IMAP获取邮箱 ${tempEmail} 的邮件`)

    // 直接使用SimpleIMAP连接真实邮箱获取邮件
    const { SimpleIMAP } = await import('@/lib/simple-imap')
    const imap = new SimpleIMAP()

    // 获取该临时邮箱的所有邮件
    const imapResult = await imap.getVerificationCode(tempEmail)

    if (!imapResult.emails || imapResult.emails.length === 0) {
      console.log(`IMAP中未找到 ${tempEmail} 的邮件`)
      return NextResponse.json({
        success: true,
        data: {
          emails: [],
          total: 0,
          message: '未找到相关邮件'
        }
      })
    }

    console.log(`从IMAP获取到 ${imapResult.emails.length} 封邮件`)

    // 转换为前端期望的格式
    const emails = imapResult.emails.map((email, index) => ({
      id: `imap-${Date.now()}-${index}`,
      from: email.from || '未知发件人',
      fromAddress: email.from || '未知发件人',
      to: email.to || tempEmail,
      subject: email.subject || '无主题',
      content: email.text || email.content || '',
      text: email.text || email.content || '',
      textContent: email.text || email.content || '',
      htmlContent: email.html || '',
      date: email.date || new Date().toISOString(),
      receivedAt: email.date ? new Date(email.date).getTime() : Date.now(),
      isRead: false,
      hasVerificationCode: false,
      verificationCode: null,
      originalTo: tempEmail,
      isForwarded: true, // 标记为转发邮件
      source: 'imap' // 标记数据源
    }))

    return NextResponse.json({
      success: true,
      data: {
        emails,
        total: emails.length,
        source: 'imap',
        message: `从IMAP服务器获取到 ${emails.length} 封邮件`
      }
    })

  } catch (error) {
    console.error('从IMAP获取邮件失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '从IMAP获取邮件时发生错误'
    }, { status: 500 })
  }
}

/**
 * 手动刷新IMAP邮件
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { tempEmail } = body

    if (!tempEmail) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址不能为空'
      }, { status: 400 })
    }

    console.log(`手动刷新IMAP邮件: ${tempEmail}`)

    // 直接使用SimpleIMAP连接真实邮箱获取最新邮件
    const { SimpleIMAP } = await import('@/lib/simple-imap')
    const imap = new SimpleIMAP()

    // 获取该临时邮箱的所有邮件
    const imapResult = await imap.getVerificationCode(tempEmail)

    if (!imapResult.emails || imapResult.emails.length === 0) {
      console.log(`IMAP中未找到 ${tempEmail} 的邮件`)
      return NextResponse.json({
        success: true,
        data: {
          emails: [],
          total: 0,
          message: '未找到相关邮件'
        }
      })
    }

    console.log(`从IMAP刷新获取到 ${imapResult.emails.length} 封邮件`)

    // 转换为前端期望的格式
    const emails = imapResult.emails.map((email, index) => ({
      id: `imap-refresh-${Date.now()}-${index}`,
      from: email.from || '未知发件人',
      fromAddress: email.from || '未知发件人',
      to: email.to || tempEmail,
      subject: email.subject || '无主题',
      content: email.text || email.content || '',
      text: email.text || email.content || '',
      textContent: email.text || email.content || '',
      htmlContent: email.html || '',
      date: email.date || new Date().toISOString(),
      receivedAt: email.date ? new Date(email.date).getTime() : Date.now(),
      isRead: false,
      hasVerificationCode: false,
      verificationCode: null,
      originalTo: tempEmail,
      isForwarded: true,
      source: 'imap'
    }))

    return NextResponse.json({
      success: true,
      data: {
        emails,
        total: emails.length,
        source: 'imap',
        message: `刷新完成，从IMAP服务器获取到 ${emails.length} 封邮件`
      }
    })

  } catch (error) {
    console.error('刷新IMAP邮件失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '刷新IMAP邮件时发生错误'
    }, { status: 500 })
  }
}
