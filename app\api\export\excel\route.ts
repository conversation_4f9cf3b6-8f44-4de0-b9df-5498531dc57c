import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import ExcelJS from 'exceljs'

/**
 * 导出Excel文件
 */
export async function POST(request: NextRequest) {
  try {
    const { limit = 1000, offset = 0 } = await request.json()

    // 从Supabase获取数据
    const { data, error } = await supabase
      .from('saved_person_data')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      return NextResponse.json({
        success: false,
        error: '获取数据失败: ' + error.message
      }, { status: 500 })
    }
    
    if (data.length === 0) {
      return NextResponse.json({
        success: false,
        error: '没有数据可导出'
      }, { status: 400 })
    }
    
    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Generated Data')
    
    // 定义列（按用户偏好的顺序）
    const columns = [
      { header: 'Email', key: 'emailAddress', width: 25 },
      { header: 'Password', key: 'emailPassword', width: 15 },
      { header: 'Full Name', key: 'fullName', width: 20 },
      { header: 'First Name', key: 'firstName', width: 15 },
      { header: 'Last Name', key: 'lastName', width: 15 },
      { header: 'Gender', key: 'gender', width: 10 },
      { header: 'Birthday', key: 'birthday', width: 12 },
      { header: 'SSN', key: 'ssn', width: 15 },
      { header: 'Full Address', key: 'fullAddress', width: 40 },
      { header: 'Street', key: 'street', width: 25 },
      { header: 'City', key: 'city', width: 15 },
      { header: 'State', key: 'state', width: 10 },
      { header: 'State Full Name', key: 'stateFullName', width: 20 },
      { header: 'Zip Code', key: 'zipCode', width: 10 },
    ]
    
    worksheet.columns = columns
    
    // 设置表头样式
    worksheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '366092' }
      }
      cell.alignment = { vertical: 'middle', horizontal: 'center' }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    })
    
    // 添加数据行
    data.forEach((item, index) => {
      const row = worksheet.addRow({
        emailAddress: item.email || '',
        emailPassword: item.password || '',
        fullName: item.full_name,
        firstName: item.first_name,
        lastName: item.last_name,
        gender: item.gender,
        birthday: item.birthday,
        ssn: item.ssn,
        fullAddress: item.full_address,
        street: item.street,
        city: item.city,
        state: item.state,
        stateFullName: item.state_full_name,
        zipCode: item.zip_code,
      })
      
      // 设置数据行样式
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
        cell.alignment = { vertical: 'middle' }
      })
      
      // 交替行颜色
      if (index % 2 === 1) {
        row.eachCell((cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F8F9FA' }
          }
        })
      }
    })
    
    // 自动调整列宽
    worksheet.columns.forEach((column) => {
      if (column.eachCell) {
        let maxLength = 0
        column.eachCell({ includeEmpty: true }, (cell) => {
          const columnLength = cell.value ? cell.value.toString().length : 10
          if (columnLength > maxLength) {
            maxLength = columnLength
          }
        })
        column.width = Math.min(Math.max(maxLength + 2, 10), 50)
      }
    })
    
    // 生成Excel文件
    const buffer = await workbook.xlsx.writeBuffer()
    
    // 生成文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const filename = `generated-data-${timestamp}.xlsx`
    
    // 返回文件
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': buffer.length.toString(),
      },
    })
    
  } catch (error) {
    console.error('Excel导出失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Excel导出时发生未知错误'
    }, { status: 500 })
  }
}

/**
 * 获取可导出数据的统计信息
 */
export async function GET() {
  try {
    const { count, error } = await supabase
      .from('saved_person_data')
      .select('*', { count: 'exact', head: true })

    if (error) {
      return NextResponse.json({
        success: false,
        error: '获取统计失败: ' + error.message
      }, { status: 500 })
    }

    // 获取有邮箱的数据数量
    const { count: withEmailCount, error: emailError } = await supabase
      .from('saved_person_data')
      .select('*', { count: 'exact', head: true })
      .not('email', 'is', null)
      .neq('email', '')

    const total = count || 0
    const withEmail = withEmailCount || 0
    const withoutEmail = total - withEmail

    return NextResponse.json({
      success: true,
      data: {
        total,
        withEmail,
        withoutEmail,
        canExport: total > 0
      }
    })
  } catch (error) {
    console.error('获取导出统计失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取导出统计时发生未知错误'
    }, { status: 500 })
  }
}
