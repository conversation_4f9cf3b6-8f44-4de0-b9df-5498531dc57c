import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

/**
 * 检查个人邮箱是否在邮件数据库中，并返回相关邮件信息
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址不能为空'
      }, { status: 400 })
    }

    console.log(`检查邮箱 ${email} 是否在数据库中`)

    // 1. 检查邮箱是否在emails表中
    const { data: emailRecord, error: emailError } = await supabase
      .from('emails')
      .select('*')
      .eq('address', email)
      .single()

    if (emailError && emailError.code !== 'PGRST116') {
      console.error('查询邮箱记录失败:', emailError)
      return NextResponse.json({
        success: false,
        error: '查询邮箱记录失败'
      }, { status: 500 })
    }

    if (!emailRecord) {
      console.log(`邮箱 ${email} 不在数据库中`)
      return NextResponse.json({
        success: true,
        data: {
          hasEmail: false,
          email: email,
          message: '该邮箱不在邮件数据库中'
        }
      })
    }

    console.log(`找到邮箱记录: ${emailRecord.id}`)

    // 2. 获取该邮箱的所有邮件消息
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('*')
      .eq('email_id', emailRecord.id)
      .order('received_at', { ascending: false })
      .limit(50) // 限制返回最近50条邮件

    if (messagesError) {
      console.error('获取邮件消息失败:', messagesError)
      return NextResponse.json({
        success: false,
        error: '获取邮件消息失败'
      }, { status: 500 })
    }

    // 3. 转换为前端期望的格式
    const emailMessages = (messages || []).map(message => ({
      id: message.id,
      from: message.from_address,
      fromAddress: message.from_address,
      to: message.to_address,
      subject: message.subject || '无主题',
      content: message.text_content || '',
      textContent: message.text_content || '',
      htmlContent: message.html_content || '',
      date: new Date(message.received_at).toISOString(),
      receivedAt: message.received_at,
      isRead: message.is_read,
      hasVerificationCode: message.has_verification_code,
      verificationCode: message.verification_code,
      createdAt: message.created_at
    }))

    console.log(`找到 ${emailMessages.length} 封邮件`)

    return NextResponse.json({
      success: true,
      data: {
        hasEmail: true,
        email: email,
        emailRecord: {
          id: emailRecord.id,
          address: emailRecord.address,
          type: emailRecord.type,
          isActive: emailRecord.is_active,
          createdAt: emailRecord.created_at,
          config: emailRecord.config
        },
        messages: emailMessages,
        totalMessages: emailMessages.length,
        verificationMessages: emailMessages.filter(msg => msg.hasVerificationCode).length,
        message: `找到 ${emailMessages.length} 封相关邮件`
      }
    })

  } catch (error) {
    console.error('检查个人邮箱失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '检查邮箱时发生未知错误'
    }, { status: 500 })
  }
}
