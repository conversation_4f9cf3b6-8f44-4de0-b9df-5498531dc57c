/**
 * 中间件工具函数
 * 提供中间件相关的辅助功能
 */

import { NextRequest } from 'next/server'

/**
 * 路径配置
 */
export const MIDDLEWARE_CONFIG = {
  // 公开路径（不需要身份验证）
  PUBLIC_PATHS: [
    '/login',
    '/api/auth/login',
    '/api/auth/logout', 
    '/api/auth/status'
  ],
  
  // 静态资源路径
  STATIC_PATHS: [
    '/_next',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
    '/images',
    '/icons'
  ],
  
  // 受保护的API路径模式
  PROTECTED_API_PATTERNS: [
    '/api/email-sync',
    '/api/check-person-emails',
    '/api/saved-data',
    '/api/export-excel'
  ]
}

/**
 * 路径检查工具
 */
export class PathChecker {
  /**
   * 检查是否为公开路径
   */
  static isPublicPath(pathname: string): boolean {
    return MIDDLEWARE_CONFIG.PUBLIC_PATHS.some(path => 
      pathname === path || pathname.startsWith(path)
    )
  }
  
  /**
   * 检查是否为静态资源路径
   */
  static isStaticPath(pathname: string): boolean {
    return MIDDLEWARE_CONFIG.STATIC_PATHS.some(path => 
      pathname.startsWith(path)
    )
  }
  
  /**
   * 检查是否为API路径
   */
  static isApiPath(pathname: string): boolean {
    return pathname.startsWith('/api')
  }
  
  /**
   * 检查是否为受保护的API路径
   */
  static isProtectedApiPath(pathname: string): boolean {
    // 身份验证API不需要额外保护
    if (pathname.startsWith('/api/auth/')) {
      return false
    }
    
    // 检查是否匹配受保护的API模式
    return MIDDLEWARE_CONFIG.PROTECTED_API_PATTERNS.some(pattern =>
      pathname.startsWith(pattern)
    )
  }
  
  /**
   * 检查是否为登录页面
   */
  static isLoginPage(pathname: string): boolean {
    return pathname === '/login'
  }
}

/**
 * 请求信息提取工具
 */
export class RequestUtils {
  /**
   * 获取客户端IP地址
   */
  static getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    const remoteAddr = request.headers.get('x-vercel-forwarded-for')
    
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    
    if (realIP) {
      return realIP
    }
    
    if (remoteAddr) {
      return remoteAddr
    }
    
    return 'unknown'
  }
  
  /**
   * 获取用户代理信息
   */
  static getUserAgent(request: NextRequest): string {
    return request.headers.get('user-agent') || 'unknown'
  }
  
  /**
   * 获取请求来源
   */
  static getReferer(request: NextRequest): string {
    return request.headers.get('referer') || 'direct'
  }
  
  /**
   * 检查是否为AJAX请求
   */
  static isAjaxRequest(request: NextRequest): boolean {
    const xRequestedWith = request.headers.get('x-requested-with')
    const accept = request.headers.get('accept')
    
    return xRequestedWith === 'XMLHttpRequest' || 
           (accept && accept.includes('application/json'))
  }
}

/**
 * 重定向工具
 */
export class RedirectUtils {
  /**
   * 构建登录重定向URL
   */
  static buildLoginRedirectUrl(request: NextRequest): string {
    const loginUrl = new URL('/login', request.url)
    
    // 保存原始路径用于登录后重定向
    if (request.nextUrl.pathname !== '/login') {
      loginUrl.searchParams.set('redirect', request.nextUrl.pathname)
      
      // 如果有查询参数，也保存下来
      if (request.nextUrl.search) {
        loginUrl.searchParams.set('search', request.nextUrl.search)
      }
    }
    
    return loginUrl.toString()
  }
  
  /**
   * 构建登录后重定向URL
   */
  static buildPostLoginRedirectUrl(request: NextRequest): string {
    const redirectPath = request.nextUrl.searchParams.get('redirect')
    const searchParams = request.nextUrl.searchParams.get('search')
    
    if (redirectPath) {
      const redirectUrl = new URL(redirectPath, request.url)
      
      // 恢复原始查询参数
      if (searchParams) {
        const originalParams = new URLSearchParams(searchParams)
        for (const [key, value] of originalParams) {
          redirectUrl.searchParams.set(key, value)
        }
      }
      
      return redirectUrl.toString()
    }
    
    return new URL('/', request.url).toString()
  }
}

/**
 * 日志工具
 */
export class MiddlewareLogger {
  /**
   * 记录访问日志
   */
  static logAccess(request: NextRequest, authenticated: boolean, action: string) {
    const ip = RequestUtils.getClientIP(request)
    const userAgent = RequestUtils.getUserAgent(request)
    const timestamp = new Date().toISOString()
    
    console.log(`[${timestamp}] ${action} - ${request.nextUrl.pathname} - IP: ${ip} - Auth: ${authenticated} - UA: ${userAgent.substring(0, 100)}`)
  }
  
  /**
   * 记录身份验证失败
   */
  static logAuthFailure(request: NextRequest, reason: string) {
    const ip = RequestUtils.getClientIP(request)
    const timestamp = new Date().toISOString()
    
    console.warn(`[${timestamp}] AUTH_FAILURE - ${request.nextUrl.pathname} - IP: ${ip} - Reason: ${reason}`)
  }
  
  /**
   * 记录重定向
   */
  static logRedirect(request: NextRequest, from: string, to: string) {
    const ip = RequestUtils.getClientIP(request)
    const timestamp = new Date().toISOString()
    
    console.log(`[${timestamp}] REDIRECT - ${from} -> ${to} - IP: ${ip}`)
  }
}
