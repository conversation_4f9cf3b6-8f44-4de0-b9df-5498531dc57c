"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Save, X, User, Mail, MapPin, Download, FileSpreadsheet } from "lucide-react"
import { PersonData, SchoolData, UniversityData } from "@/app/page"
import { exportToExcel } from "@/lib/excel-export"
import { toast } from "@/hooks/use-toast"

interface BatchResultsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  batchData: Array<{
    personData: PersonData
    schoolData: SchoolData | null
    universityData: UniversityData | null
  }>
  onSaveBatch: () => void
  saving: boolean
}

export default function BatchResultsDialog({ 
  open, 
  onOpenChange, 
  batchData, 
  onSaveBatch, 
  saving 
}: BatchResultsDialogProps) {
  
  const exportBatchAsJson = () => {
    const dataToExport = batchData.map((item, index) => ({
      id: index + 1,
      person: item.personData,
      school: item.schoolData,
      university: item.universityData
    }))

    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
      type: 'application/json'
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `batch-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const exportBatchAsExcel = async () => {
    try {
      await exportToExcel(batchData, undefined, true) // 启用邮箱替换
      toast({
        title: "导出成功",
        description: `已导出 ${batchData.length} 条数据到Excel文件（邮箱已替换为生成的邮箱）`,
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "导出Excel时发生错误",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold text-gray-900">
              批量生成结果 ({batchData.length} 条数据)
            </DialogTitle>
            <div className="flex gap-2">
              <Button
                onClick={exportBatchAsJson}
                variant="outline"
                size="sm"
              >
                <Download className="mr-2 h-4 w-4" />
                导出JSON
              </Button>
              <Button
                onClick={exportBatchAsExcel}
                variant="outline"
                size="sm"
                className="bg-emerald-50 hover:bg-emerald-100 border-emerald-200"
              >
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                导出Excel
              </Button>
              <Button
                onClick={onSaveBatch}
                disabled={saving || batchData.length === 0}
                className="bg-green-600 hover:bg-green-700"
                size="sm"
              >
                {saving ? (
                  <>
                    <Save className="mr-2 h-4 w-4 animate-spin" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    全部保存到数据库
                  </>
                )}
              </Button>
              <Button
                onClick={() => onOpenChange(false)}
                variant="ghost"
                size="sm"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <ScrollArea className="h-[70vh] pr-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {batchData.map((item, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-gray-800 flex items-center justify-between">
                    <span className="truncate">{item.personData.fullName}</span>
                    <Badge variant="outline" className="text-xs">
                      #{index + 1}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* 基本信息 */}
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <User className="mr-2 h-4 w-4" />
                      {item.personData.gender} • {item.personData.birthday}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Mail className="mr-2 h-4 w-4" />
                      <span className="truncate">{item.personData.email}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="mr-2 h-4 w-4" />
                      {item.personData.city}, {item.personData.state}
                    </div>
                  </div>

                  {/* 工作信息 */}
                  {item.personData.occupation && (
                    <div className="pt-2 border-t">
                      <div className="text-xs text-gray-500 mb-1">职业信息</div>
                      <div className="text-sm font-medium text-gray-800">
                        {item.personData.occupation}
                      </div>
                      {item.personData.company && (
                        <div className="text-xs text-gray-600">
                          {item.personData.company}
                        </div>
                      )}
                    </div>
                  )}

                  {/* 学校信息 */}
                  {item.schoolData && (
                    <div className="pt-2 border-t">
                      <div className="text-xs text-gray-500 mb-1">高中</div>
                      <div className="text-sm font-medium text-gray-800 truncate">
                        {item.schoolData.name}
                      </div>
                      <div className="text-xs text-gray-600">
                        {item.schoolData.city}, {item.schoolData.state}
                      </div>
                    </div>
                  )}

                  {/* 大学信息 */}
                  {item.universityData && (
                    <div className="pt-2 border-t">
                      <div className="text-xs text-gray-500 mb-1">大学</div>
                      <div className="text-sm font-medium text-gray-800 truncate">
                        {item.universityData.name}
                      </div>
                      <div className="text-xs text-gray-600">
                        {item.universityData.city}, {item.universityData.state}
                      </div>
                    </div>
                  )}

                  {/* 联系方式 */}
                  <div className="pt-2 border-t">
                    <div className="text-xs text-gray-500 mb-1">联系方式</div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>电话: {item.personData.phone}</div>
                      <div className="truncate">地址: {item.personData.fullAddress}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {batchData.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">暂无批量生成数据</p>
            </div>
          )}
        </ScrollArea>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-gray-600">
            共生成 {batchData.length} 条数据
          </div>
          <div className="flex gap-2">
            <Button
              onClick={() => onOpenChange(false)}
              variant="outline"
            >
              关闭
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
