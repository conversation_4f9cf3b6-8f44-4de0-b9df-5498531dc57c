"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Users } from "lucide-react"

interface BatchGenerateControlProps {
  onGenerate: (count: number) => void
  loading: boolean
  generating: boolean
  defaultCount?: number
}

export default function BatchGenerateControl({ 
  onGenerate, 
  loading, 
  generating, 
  defaultCount = 10 
}: BatchGenerateControlProps) {
  const [count, setCount] = useState(defaultCount)

  const handleCountChange = (value: string) => {
    const num = parseInt(value)
    if (isNaN(num) || num < 1) {
      setCount(1)
    } else if (num > 99) {
      setCount(99)
    } else {
      setCount(num)
    }
  }

  const handleGenerate = () => {
    onGenerate(count)
  }

  return (
    <div className="flex items-center gap-2 bg-orange-50 p-2 rounded-lg border border-orange-200">
      <Button 
        onClick={handleGenerate} 
        disabled={loading || generating} 
        className="bg-orange-600 hover:bg-orange-700 text-white"
        size="sm"
      >
        {generating ? (
          <>
            <Users className="mr-2 h-4 w-4 animate-spin" />
            生成中...
          </>
        ) : (
          <>
            <Users className="mr-2 h-4 w-4" />
            批量生成
          </>
        )}
      </Button>
      
      <div className="flex items-center gap-1">
        <Input
          type="number"
          min="1"
          max="99"
          value={count}
          onChange={(e) => handleCountChange(e.target.value)}
          className="w-16 h-8 text-center text-sm border-orange-300 focus:border-orange-500"
          disabled={loading || generating}
        />
        <span className="text-sm text-gray-600 font-medium">个</span>
      </div>
      
      <div className="text-xs text-gray-500 ml-2">
        (最多99个)
      </div>
    </div>
  )
}
