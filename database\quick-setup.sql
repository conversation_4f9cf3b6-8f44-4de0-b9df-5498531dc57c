-- =====================================================
-- 快速数据库设置 - 仅创建必要的表和索引
-- 复制此内容到 Supabase SQL 编辑器中执行
-- =====================================================

-- 1. 个人数据表
CREATE TABLE IF NOT EXISTS saved_person_data (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  full_name TEXT NOT NULL,
  first_name TEXT,
  last_name TEXT,
  gender TEXT,
  birthday TEXT,
  title TEXT,
  hair_color TEXT,
  country TEXT,
  street TEXT,
  city TEXT,
  state TEXT,
  state_full_name TEXT,
  zip_code TEXT,
  phone TEXT,
  email TEXT,
  full_address TEXT,
  occupation TEXT,
  company TEXT,
  company_size TEXT,
  industry TEXT,
  status TEXT,
  salary TEXT,
  ssn TEXT,
  card_type TEXT,
  card_number TEXT,
  cvv INTEGER,
  expiry TEXT,
  username TEXT,
  password TEXT,
  security_question TEXT,
  security_answer TEXT,
  height TEXT,
  weight TEXT,
  blood_type TEXT,
  os TEXT,
  guid TEXT,
  user_agent TEXT,
  education TEXT,
  website TEXT,
  school_name TEXT,
  school_id TEXT,
  school_zip TEXT,
  school_website TEXT,
  school_address TEXT,
  school_city TEXT,
  school_state TEXT,
  school_phone TEXT,
  school_grades TEXT,
  university_name TEXT,
  university_id TEXT,
  university_zip TEXT,
  university_website TEXT,
  university_address TEXT,
  university_city TEXT,
  university_state TEXT,
  university_phone TEXT,
  university_type TEXT,
  generation_seed INTEGER,
  generation_params JSONB
);

-- 2. 邮箱表
CREATE TABLE IF NOT EXISTS emails (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  address TEXT NOT NULL UNIQUE,
  password TEXT,
  type TEXT DEFAULT 'tempmail',
  config JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at_timestamp BIGINT DEFAULT EXTRACT(epoch FROM NOW()) * 1000,
  unread_count INTEGER DEFAULT 0,
  has_new_messages BOOLEAN DEFAULT false
);

-- 3. 消息表
CREATE TABLE IF NOT EXISTS messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  email_id UUID REFERENCES emails(id) ON DELETE CASCADE,
  from_address TEXT NOT NULL,
  to_address TEXT NOT NULL,
  subject TEXT,
  text_content TEXT,
  html_content TEXT,
  received_at BIGINT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  has_verification_code BOOLEAN DEFAULT false,
  verification_code TEXT
);

-- 4. 基本索引
CREATE INDEX IF NOT EXISTS idx_saved_person_data_created_at ON saved_person_data(created_at);
CREATE INDEX IF NOT EXISTS idx_saved_person_data_email ON saved_person_data(email);
CREATE INDEX IF NOT EXISTS idx_emails_address ON emails(address);
CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_email_id ON messages(email_id);
CREATE INDEX IF NOT EXISTS idx_messages_received_at ON messages(received_at);

-- 5. 安全策略
ALTER TABLE saved_person_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE emails ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY IF NOT EXISTS "Allow all operations on saved_person_data" ON saved_person_data FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Allow all operations on emails" ON emails FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Allow all operations on messages" ON messages FOR ALL USING (true);
