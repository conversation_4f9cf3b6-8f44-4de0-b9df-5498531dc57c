"use client"

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { authService, AuthState, User, LoginCredentials, AuthResponse } from '@/lib/auth'

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<AuthResponse>
  logout: () => Promise<void>
  validateSession: () => Promise<boolean>
  refreshAuth: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: true,
  })

  /**
   * 初始化认证状态
   */
  const initializeAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }))

      // 检查本地存储的认证信息
      const isAuthenticated = authService.isAuthenticated()
      const user = authService.getCurrentUser()

      if (isAuthenticated && user) {
        // 验证服务器端会话
        const isValid = await authService.validateSession()
        
        if (isValid) {
          setAuthState({
            isAuthenticated: true,
            user,
            loading: false,
          })
        } else {
          setAuthState({
            isAuthenticated: false,
            user: null,
            loading: false,
          })
        }
      } else {
        setAuthState({
          isAuthenticated: false,
          user: null,
          loading: false,
        })
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      setAuthState({
        isAuthenticated: false,
        user: null,
        loading: false,
      })
    }
  }

  /**
   * 用户登录
   */
  const login = async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }))

      const response = await authService.login(credentials)

      if (response.success && response.user) {
        setAuthState({
          isAuthenticated: true,
          user: response.user,
          loading: false,
        })
      } else {
        setAuthState(prev => ({
          ...prev,
          loading: false,
        }))
      }

      return response
    } catch (error) {
      console.error('登录失败:', error)
      setAuthState(prev => ({
        ...prev,
        loading: false,
      }))
      
      return {
        success: false,
        error: '登录过程中发生错误'
      }
    }
  }

  /**
   * 用户登出
   */
  const logout = async (): Promise<void> => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }))
      
      await authService.logout()
      
      setAuthState({
        isAuthenticated: false,
        user: null,
        loading: false,
      })
    } catch (error) {
      console.error('登出失败:', error)
      // 即使登出失败，也要清除本地状态
      setAuthState({
        isAuthenticated: false,
        user: null,
        loading: false,
      })
    }
  }

  /**
   * 验证会话
   */
  const validateSession = async (): Promise<boolean> => {
    try {
      const isValid = await authService.validateSession()
      
      if (!isValid) {
        setAuthState({
          isAuthenticated: false,
          user: null,
          loading: false,
        })
      }
      
      return isValid
    } catch (error) {
      console.error('会话验证失败:', error)
      setAuthState({
        isAuthenticated: false,
        user: null,
        loading: false,
      })
      return false
    }
  }

  /**
   * 刷新认证状态
   */
  const refreshAuth = () => {
    initializeAuth()
  }

  // 组件挂载时初始化认证状态
  useEffect(() => {
    initializeAuth()
  }, [])

  // 监听存储变化（多标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth-token' || e.key === 'auth-user') {
        initializeAuth()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  // 定期验证会话（每5分钟）
  useEffect(() => {
    if (!authState.isAuthenticated) return

    const interval = setInterval(() => {
      validateSession()
    }, 5 * 60 * 1000) // 5分钟

    return () => clearInterval(interval)
  }, [authState.isAuthenticated])

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    validateSession,
    refreshAuth,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

/**
 * 使用认证上下文的Hook
 */
export function useAuthContext(): AuthContextType {
  const context = useContext(AuthContext)
  
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider')
  }
  
  return context
}

export { AuthContext }
