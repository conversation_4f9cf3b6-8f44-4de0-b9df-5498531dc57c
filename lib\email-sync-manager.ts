/**
 * 邮件同步状态管理器
 * 管理邮件同步的启动和停止，与用户登录状态关联
 */

interface SyncState {
  isRunning: boolean
  interval: number
  lastStartTime: string | null
  userInitiated: boolean
}

class EmailSyncManager {
  private static instance: EmailSyncManager
  private syncState: SyncState = {
    isRunning: false,
    interval: 5,
    lastStartTime: null,
    userInitiated: false
  }

  private constructor() {}

  static getInstance(): EmailSyncManager {
    if (!EmailSyncManager.instance) {
      EmailSyncManager.instance = new EmailSyncManager()
    }
    return EmailSyncManager.instance
  }

  /**
   * 启动邮件同步（用户登录时）
   */
  async startSync(interval: number = 5): Promise<{ success: boolean; message: string }> {
    try {
      // 直接调用定时任务函数，避免HTTP请求
      const { startSyncSchedule } = await import('@/app/api/email-sync/schedule/route')

      startSyncSchedule(interval)

      this.syncState = {
        isRunning: true,
        interval,
        lastStartTime: new Date().toISOString(),
        userInitiated: true
      }

      console.log(`邮件同步已启动，间隔 ${interval} 分钟`)
      return { success: true, message: `邮件同步已启动，间隔 ${interval} 分钟` }
    } catch (error) {
      console.error('启动邮件同步失败:', error)
      return { success: false, message: '启动邮件同步失败' }
    }
  }

  /**
   * 停止邮件同步（用户登出时）
   */
  async stopSync(): Promise<{ success: boolean; message: string }> {
    try {
      // 直接调用定时任务函数，避免HTTP请求
      const { stopSyncSchedule } = await import('@/app/api/email-sync/schedule/route')

      stopSyncSchedule()

      this.syncState = {
        isRunning: false,
        interval: this.syncState.interval,
        lastStartTime: this.syncState.lastStartTime,
        userInitiated: false
      }

      console.log('邮件同步已停止')
      return { success: true, message: '邮件同步已停止' }
    } catch (error) {
      console.error('停止邮件同步失败:', error)
      return { success: false, message: '停止邮件同步失败' }
    }
  }

  /**
   * 获取同步状态
   */
  getSyncState(): SyncState {
    return { ...this.syncState }
  }

  /**
   * 检查同步是否正在运行
   */
  isRunning(): boolean {
    return this.syncState.isRunning
  }

  /**
   * 重启邮件同步
   */
  async restartSync(interval?: number): Promise<{ success: boolean; message: string }> {
    try {
      const syncInterval = interval || this.syncState.interval

      // 直接调用定时任务函数，避免HTTP请求
      const { stopSyncSchedule, startSyncSchedule } = await import('@/app/api/email-sync/schedule/route')

      stopSyncSchedule()
      startSyncSchedule(syncInterval)

      this.syncState = {
        isRunning: true,
        interval: syncInterval,
        lastStartTime: new Date().toISOString(),
        userInitiated: true
      }

      console.log(`邮件同步已重启，间隔 ${syncInterval} 分钟`)
      return { success: true, message: `邮件同步已重启，间隔 ${syncInterval} 分钟` }
    } catch (error) {
      console.error('重启邮件同步失败:', error)
      return { success: false, message: '重启邮件同步失败' }
    }
  }

  /**
   * 获取远程同步状态
   */
  async getRemoteSyncStatus(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 直接获取定时任务状态，避免HTTP请求
      const { syncSchedule } = await import('@/app/api/email-sync/schedule/route')

      // 更新本地状态
      this.syncState.isRunning = syncSchedule.isRunning
      this.syncState.interval = syncSchedule.interval

      return {
        success: true,
        data: {
          ...syncSchedule,
          status: syncSchedule.isRunning ? 'running' : 'stopped',
          intervalText: `${syncSchedule.interval} 分钟`,
          lastRunText: syncSchedule.lastRun
            ? new Date(syncSchedule.lastRun).toLocaleString('zh-CN')
            : '从未运行',
          nextRunText: syncSchedule.nextRun
            ? new Date(syncSchedule.nextRun).toLocaleString('zh-CN')
            : '未计划'
        }
      }
    } catch (error) {
      console.error('获取远程同步状态失败:', error)
      return { success: false, error: '获取远程同步状态失败' }
    }
  }
}

// 导出单例实例
export const emailSyncManager = EmailSyncManager.getInstance()

/**
 * 用户登录时启动邮件同步
 */
export async function startEmailSyncOnLogin(interval: number = 5) {
  console.log('用户登录，启动邮件同步...')
  return await emailSyncManager.startSync(interval)
}

/**
 * 用户登出时停止邮件同步
 */
export async function stopEmailSyncOnLogout() {
  console.log('用户登出，停止邮件同步...')
  return await emailSyncManager.stopSync()
}

/**
 * 检查邮件同步是否正在运行
 */
export function isEmailSyncRunning(): boolean {
  return emailSyncManager.isRunning()
}

/**
 * 获取邮件同步状态
 */
export function getEmailSyncState(): SyncState {
  return emailSyncManager.getSyncState()
}
