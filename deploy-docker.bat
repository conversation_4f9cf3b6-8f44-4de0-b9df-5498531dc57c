@echo off
echo.
echo ==========================================
echo 美国地址生成器 - 一键Docker部署
echo ==========================================
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker未运行，请启动Docker Desktop
    pause
    exit /b 1
)

echo [信息] Docker运行正常

REM 检查必要文件
if not exist "package.json" (
    echo [错误] 请在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist ".env.local" (
    echo [警告] .env.local文件不存在
    echo [信息] 正在从模板创建...
    if exist ".env.local.example" (
        copy .env.local.example .env.local
        echo [成功] 已创建.env.local文件
        echo [重要] 请编辑.env.local文件配置您的环境变量
        echo.
        echo 必需配置:
        echo - NEXT_PUBLIC_SUPABASE_URL
        echo - NEXT_PUBLIC_SUPABASE_ANON_KEY
        echo - ADMIN_USERNAME
        echo - ADMIN_PASSWORD
        echo - JWT_SECRET
        echo.
        set /p continue="配置完成后按回车继续，或输入n退出: "
        if /i "%continue%"=="n" exit /b 0
    ) else (
        echo [错误] .env.local.example文件不存在
        pause
        exit /b 1
    )
)

echo [信息] 开始Docker部署...

REM 停止现有容器
echo [步骤1] 停止现有容器...
docker-compose down 2>nul

REM 清理旧镜像
echo [步骤2] 清理旧镜像...
docker image prune -f

REM 构建新镜像
echo [步骤3] 构建Docker镜像...
docker-compose build --no-cache

if %errorlevel% neq 0 (
    echo [错误] Docker镜像构建失败
    echo [建议] 请检查:
    echo 1. 网络连接是否正常
    echo 2. Docker Desktop内存设置是否足够 (建议4GB+)
    echo 3. 磁盘空间是否充足
    pause
    exit /b 1
)

echo [成功] Docker镜像构建完成

REM 启动服务
echo [步骤4] 启动服务...
docker-compose up -d

if %errorlevel% neq 0 (
    echo [错误] 服务启动失败
    echo [信息] 查看错误日志:
    docker-compose logs
    pause
    exit /b 1
)

echo [成功] 服务启动完成

REM 等待服务就绪
echo [步骤5] 等待服务就绪...
timeout /t 10 /nobreak >nul

REM 检查服务状态
docker-compose ps | findstr "Up" >nul
if %errorlevel% neq 0 (
    echo [错误] 服务未正常启动
    echo [信息] 服务状态:
    docker-compose ps
    echo.
    echo [信息] 错误日志:
    docker-compose logs --tail=20
    pause
    exit /b 1
)

echo.
echo ==========================================
echo 🎉 部署成功！
echo ==========================================
echo.
echo 📱 应用访问地址:
echo   主页: http://localhost:3000
echo   健康检查: http://localhost:3000/api/health
echo.
echo 🔧 管理命令:
echo   查看日志: docker-compose logs -f
echo   停止服务: docker-compose down
echo   重启服务: docker-compose restart
echo.
echo 📊 服务状态:
docker-compose ps
echo.

REM 尝试打开浏览器
set /p open="是否打开浏览器访问应用? (y/n): "
if /i "%open%"=="y" (
    start http://localhost:3000
)

echo.
echo [信息] 部署完成，按任意键退出
pause
