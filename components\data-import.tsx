"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON>,
  <PERSON>bsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Upload, FileText, Download, RefreshCw, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface DataImportProps {
  onImportComplete: () => void
  disabled?: boolean
}

export function DataImport({ onImportComplete, disabled = false }: DataImportProps) {
  const [open, setOpen] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [createEmails, setCreateEmails] = useState(false)
  const [jsonData, setJsonData] = useState("")
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const { toast } = useToast()

  // 下载模板
  const downloadTemplate = async () => {
    try {
      const response = await fetch('/api/import/data')
      const result = await response.json()
      
      if (result.success) {
        const blob = new Blob([JSON.stringify(result.data, null, 2)], { type: 'application/json' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = 'import-template.json'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        toast({
          title: "模板下载成功",
          description: "请参考模板格式准备您的数据",
        })
      }
    } catch (error) {
      toast({
        title: "下载模板失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 处理JSON导入
  const handleJsonImport = async () => {
    if (!jsonData.trim()) {
      toast({
        title: "导入失败",
        description: "请输入JSON数据",
        variant: "destructive",
      })
      return
    }

    try {
      const data = JSON.parse(jsonData)
      await performImport(data)
    } catch (error) {
      toast({
        title: "JSON格式错误",
        description: "请检查JSON格式是否正确",
        variant: "destructive",
      })
    }
  }

  // 处理CSV导入
  const handleCsvImport = async () => {
    if (!csvFile) {
      toast({
        title: "导入失败",
        description: "请选择CSV文件",
        variant: "destructive",
      })
      return
    }

    try {
      const text = await csvFile.text()
      const lines = text.split('\n').filter(line => line.trim())
      
      if (lines.length < 2) {
        throw new Error('CSV文件至少需要包含表头和一行数据')
      }
      
      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
      const data = lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.trim().replace(/"/g, ''))
        const obj: any = {}
        headers.forEach((header, index) => {
          obj[header] = values[index] || ''
        })
        return obj
      })
      
      await performImport(data)
    } catch (error) {
      toast({
        title: "CSV解析失败",
        description: error instanceof Error ? error.message : "请检查CSV格式",
        variant: "destructive",
      })
    }
  }

  // 执行导入
  const performImport = async (data: any[]) => {
    setIsImporting(true)
    try {
      const response = await fetch('/api/import/data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data,
          createEmails
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "导入成功",
          description: result.message,
        })
        setOpen(false)
        setJsonData("")
        setCsvFile(null)
        onImportComplete()
      } else {
        toast({
          title: "导入失败",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "导入失败",
        description: "网络错误",
        variant: "destructive",
      })
    } finally {
      setIsImporting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          className="bg-green-50 hover:bg-green-100 border-green-200"
        >
          <Upload className="h-4 w-4 mr-2" />
          导入数据
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5 text-green-600" />
            导入数据
          </DialogTitle>
          <DialogDescription>
            支持JSON和CSV格式的数据导入，可选择是否同时创建邮箱账户。
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 选项 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="createEmails"
              checked={createEmails}
              onCheckedChange={(checked) => setCreateEmails(checked as boolean)}
            />
            <Label htmlFor="createEmails" className="text-sm">
              同时创建邮箱账户（如果数据中包含邮箱信息）
            </Label>
          </div>

          {/* 模板下载 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-900">需要导入模板？</p>
                <p className="text-xs text-blue-700">下载JSON格式的示例数据</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={downloadTemplate}
                className="bg-blue-100 hover:bg-blue-200 border-blue-300"
              >
                <Download className="h-4 w-4 mr-1" />
                下载模板
              </Button>
            </div>
          </div>

          {/* 导入选项卡 */}
          <Tabs defaultValue="json" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="json">JSON导入</TabsTrigger>
              <TabsTrigger value="csv">CSV导入</TabsTrigger>
            </TabsList>
            
            <TabsContent value="json" className="space-y-4">
              <div>
                <Label htmlFor="jsonData">JSON数据</Label>
                <Textarea
                  id="jsonData"
                  placeholder='[{"fullName":"John Smith","firstName":"John","lastName":"Smith",...}]'
                  value={jsonData}
                  onChange={(e) => setJsonData(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  请输入JSON格式的数组数据
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="csv" className="space-y-4">
              <div>
                <Label htmlFor="csvFile">CSV文件</Label>
                <Input
                  id="csvFile"
                  type="file"
                  accept=".csv"
                  onChange={(e) => setCsvFile(e.target.files?.[0] || null)}
                />
                <p className="text-xs text-gray-500 mt-1">
                  请选择CSV格式的文件，第一行应为字段名
                </p>
              </div>
            </TabsContent>
          </Tabs>

          {/* 注意事项 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">导入注意事项：</p>
                <ul className="text-xs space-y-1">
                  <li>• 必填字段：fullName, firstName, lastName, gender, birthday, ssn</li>
                  <li>• 地址字段：street, city, state, stateFullName, zipCode</li>
                  <li>• 邮箱字段：email, password（可选）</li>
                  <li>• 重复数据将被跳过</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isImporting}
          >
            取消
          </Button>
          <Button
            onClick={() => {
              const activeTab = document.querySelector('[data-state="active"]')?.getAttribute('data-value')
              if (activeTab === 'json') {
                handleJsonImport()
              } else {
                handleCsvImport()
              }
            }}
            disabled={isImporting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isImporting ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                导入中...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                开始导入
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
