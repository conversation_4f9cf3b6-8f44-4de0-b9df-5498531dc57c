import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // 检查环境变量
    const envCheck = {
      supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      urlValue: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 20) + '...',
      keyValue: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20) + '...'
    }

    // 测试数据库连接
    let connectionTest = { success: false, error: null, responseTime: 0 }
    try {
      const connStart = Date.now()
      const { data, error } = await supabase.from('saved_person_data').select('count', { count: 'exact', head: true })
      connectionTest = {
        success: !error,
        error: error?.message || null,
        responseTime: Date.now() - connStart
      }
    } catch (err) {
      connectionTest = {
        success: false,
        error: err instanceof Error ? err.message : '连接测试失败',
        responseTime: Date.now() - startTime
      }
    }

    // 检查表结构
    let tableCheck = { exists: false, error: null, columns: [] }
    try {
      const { data, error } = await supabase
        .from('saved_person_data')
        .select('*')
        .limit(1)
      
      if (!error && data !== null) {
        tableCheck.exists = true
        if (data.length > 0) {
          tableCheck.columns = Object.keys(data[0])
        }
      } else {
        tableCheck.error = error?.message || '表不存在或无权限访问'
      }
    } catch (err) {
      tableCheck.error = err instanceof Error ? err.message : '表检查失败'
    }

    // 统计数据数量
    let dataStats = { count: 0, error: null, sampleData: null }
    try {
      const { count, error: countError } = await supabase
        .from('saved_person_data')
        .select('*', { count: 'exact', head: true })
      
      if (!countError) {
        dataStats.count = count || 0
        
        // 获取样本数据
        if (count && count > 0) {
          const { data: sampleData, error: sampleError } = await supabase
            .from('saved_person_data')
            .select('id, full_name, email, created_at')
            .limit(3)
          
          if (!sampleError) {
            dataStats.sampleData = sampleData
          }
        }
      } else {
        dataStats.error = countError.message
      }
    } catch (err) {
      dataStats.error = err instanceof Error ? err.message : '数据统计失败'
    }

    // 测试API接口
    let apiTest = { success: false, error: null, responseTime: 0 }
    try {
      const apiStart = Date.now()
      const response = await fetch(`${request.nextUrl.origin}/api/saved-data?page=1&limit=1`)
      const result = await response.json()
      apiTest = {
        success: response.ok && result.success,
        error: result.error || (!response.ok ? `HTTP ${response.status}` : null),
        responseTime: Date.now() - apiStart
      }
    } catch (err) {
      apiTest = {
        success: false,
        error: err instanceof Error ? err.message : 'API测试失败',
        responseTime: Date.now() - startTime
      }
    }

    const totalTime = Date.now() - startTime

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      totalTime,
      checks: {
        environment: envCheck,
        connection: connectionTest,
        table: tableCheck,
        data: dataStats,
        api: apiTest
      },
      recommendations: generateRecommendations({
        envCheck,
        connectionTest,
        tableCheck,
        dataStats,
        apiTest
      })
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '诊断过程发生未知错误',
      timestamp: new Date().toISOString(),
      totalTime: Date.now() - startTime
    }, { status: 500 })
  }
}

function generateRecommendations(checks: any) {
  const recommendations = []

  if (!checks.envCheck.supabaseUrl || !checks.envCheck.supabaseKey) {
    recommendations.push({
      type: 'error',
      title: '环境变量缺失',
      description: '请检查 .env.local 文件中的 Supabase 配置',
      action: '确保 NEXT_PUBLIC_SUPABASE_URL 和 NEXT_PUBLIC_SUPABASE_ANON_KEY 已正确设置'
    })
  }

  if (!checks.connectionTest.success) {
    recommendations.push({
      type: 'error',
      title: '数据库连接失败',
      description: checks.connectionTest.error,
      action: '检查 Supabase 项目状态和网络连接'
    })
  }

  if (!checks.tableCheck.exists) {
    recommendations.push({
      type: 'error',
      title: '数据表不存在',
      description: 'saved_person_data 表未找到',
      action: '需要创建数据库表结构，请运行数据库迁移脚本'
    })
  }

  if (checks.dataStats.count === 0) {
    recommendations.push({
      type: 'warning',
      title: '数据库为空',
      description: '数据库中没有保存的数据',
      action: '尝试在首页生成数据并点击"保存数据"按钮'
    })
  }

  if (!checks.apiTest.success) {
    recommendations.push({
      type: 'error',
      title: 'API接口异常',
      description: checks.apiTest.error,
      action: '检查 /api/saved-data 接口实现'
    })
  }

  if (recommendations.length === 0) {
    recommendations.push({
      type: 'success',
      title: '系统状态正常',
      description: '所有检查项都通过了',
      action: '如果仍有问题，请检查浏览器控制台错误信息'
    })
  }

  return recommendations
}
