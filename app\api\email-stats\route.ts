import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

/**
 * 获取邮件管理系统的统计信息
 */
export async function GET() {
  try {
    // 获取邮箱总数
    const { count: totalEmails, error: emailCountError } = await supabase
      .from('emails')
      .select('*', { count: 'exact', head: true })

    if (emailCountError) {
      console.error('获取邮箱总数失败:', emailCountError)
    }

    // 获取活跃邮箱数
    const { count: activeEmails, error: activeEmailError } = await supabase
      .from('emails')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    if (activeEmailError) {
      console.error('获取活跃邮箱数失败:', activeEmailError)
    }

    // 获取消息总数
    const { count: totalMessages, error: messageCountError } = await supabase
      .from('messages')
      .select('*', { count: 'exact', head: true })

    if (messageCountError) {
      console.error('获取消息总数失败:', messageCountError)
    }

    // 获取包含验证码的消息数
    const { count: verificationMessages, error: verificationError } = await supabase
      .from('messages')
      .select('*', { count: 'exact', head: true })
      .eq('has_verification_code', true)

    if (verificationError) {
      console.error('获取验证码消息数失败:', verificationError)
    }

    // 获取未读消息数
    const { count: unreadMessages, error: unreadError } = await supabase
      .from('messages')
      .select('*', { count: 'exact', head: true })
      .eq('is_read', false)

    if (unreadError) {
      console.error('获取未读消息数失败:', unreadError)
    }

    // 构建统计数据
    const stats = {
      emails: {
        total: totalEmails || 0,
        active: activeEmails || 0,
        inactive: (totalEmails || 0) - (activeEmails || 0)
      },
      messages: {
        total: totalMessages || 0,
        withVerificationCode: verificationMessages || 0,
        unread: unreadMessages || 0,
        read: (totalMessages || 0) - (unreadMessages || 0)
      },
      performance: {
        messagesPerEmail: totalEmails > 0 ? (totalMessages || 0) / totalEmails : 0,
        verificationRate: totalMessages > 0 ? ((verificationMessages || 0) / totalMessages * 100) : 0,
        readRate: totalMessages > 0 ? (((totalMessages || 0) - (unreadMessages || 0)) / totalMessages * 100) : 0
      },
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('获取邮件统计失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取邮件统计失败'
    }, { status: 500 })
  }
}
