#!/bin/bash

# 美国地址生成器 - Linux自动部署脚本
# 支持 Ubuntu/Debian/CentOS/RHEL/Alpine Linux

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=========================================="
    echo "  美国地址生成器 - Linux部署工具"
    echo "  Next.js 15 + React 19 + Supabase"
    echo "=========================================="
    echo -e "${NC}"
}

# 检测Linux发行版
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查是否为root用户或有sudo权限
    if [[ $EUID -eq 0 ]]; then
        SUDO=""
    elif sudo -n true 2>/dev/null; then
        SUDO="sudo"
    else
        log_error "需要root权限或sudo权限来安装依赖"
        exit 1
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$MEMORY_GB" -lt 2 ]; then
        log_warning "系统内存少于2GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$DISK_SPACE" -lt 5 ]; then
        log_warning "可用磁盘空间少于5GB，可能影响部署"
    fi
    
    log_success "系统要求检查完成"
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker已安装: $(docker --version)"
        return
    fi
    
    # 根据不同发行版安装Docker
    case "$OS" in
        *"Ubuntu"*|*"Debian"*)
            $SUDO apt-get update
            $SUDO apt-get install -y ca-certificates curl gnupg lsb-release
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | $SUDO gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | $SUDO tee /etc/apt/sources.list.d/docker.list > /dev/null
            $SUDO apt-get update
            $SUDO apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        *"CentOS"*|*"Red Hat"*|*"Rocky"*|*"AlmaLinux"*)
            $SUDO yum install -y yum-utils
            $SUDO yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
            $SUDO yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        *"Alpine"*)
            $SUDO apk add docker docker-compose
            ;;
        *)
            log_error "不支持的操作系统: $OS"
            log_info "请手动安装Docker: https://docs.docker.com/engine/install/"
            exit 1
            ;;
    esac
    
    # 启动Docker服务
    $SUDO systemctl start docker
    $SUDO systemctl enable docker
    
    # 将当前用户添加到docker组
    $SUDO usermod -aG docker $USER
    
    log_success "Docker安装完成"
}

# 检查项目文件
check_project_files() {
    log_step "检查项目文件..."
    
    local required_files=(
        "package.json"
        "Dockerfile.linux"
        "docker-compose.linux.yml"
        ".env.local.example"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少必需文件: $file"
            exit 1
        fi
    done
    
    log_success "项目文件检查完成"
}

# 配置环境变量
setup_environment() {
    log_step "配置环境变量..."
    
    if [ ! -f ".env.production" ]; then
        if [ -f ".env.local.example" ]; then
            cp .env.local.example .env.production
            log_info "已创建.env.production文件"
        else
            log_error ".env.local.example文件不存在"
            exit 1
        fi
    fi
    
    # 检查关键环境变量
    local required_vars=(
        "NEXT_PUBLIC_SUPABASE_URL"
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
        "ADMIN_USERNAME"
        "ADMIN_PASSWORD"
        "JWT_SECRET"
    )
    
    local missing_vars=()
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" .env.production || grep -q "^${var}=your_" .env.production; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_warning "以下环境变量需要配置:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo
        read -p "是否现在编辑.env.production文件? (y/n): " edit_env
        if [[ $edit_env =~ ^[Yy]$ ]]; then
            ${EDITOR:-nano} .env.production
        else
            log_warning "请手动编辑.env.production文件后重新运行部署脚本"
            exit 1
        fi
    fi
    
    log_success "环境变量配置完成"
}

# 构建和部署
deploy_application() {
    log_step "构建和部署应用..."

    # 停止现有容器
    log_info "停止现有容器..."
    docker compose -f docker-compose.linux.yml down 2>/dev/null || true

    # 清理旧镜像
    log_info "清理旧镜像..."
    docker image prune -f

    # 尝试多种构建方式
    local build_success=false

    # 方式1: 使用pnpm版本的Dockerfile
    if [ -f "Dockerfile.linux" ] && [ -f "pnpm-lock.yaml" ]; then
        log_info "尝试使用pnpm构建..."
        if docker compose -f docker-compose.linux.yml build --no-cache 2>/dev/null; then
            log_success "pnpm构建成功!"
            build_success=true
        else
            log_warning "pnpm构建失败，尝试npm版本..."
        fi
    fi

    # 方式2: 使用npm版本的Dockerfile
    if [ "$build_success" = false ] && [ -f "Dockerfile.linux-npm" ]; then
        log_info "使用npm版本构建..."
        # 临时修改docker-compose文件使用npm版本的Dockerfile
        sed -i.bak 's/dockerfile: Dockerfile.linux/dockerfile: Dockerfile.linux-npm/' docker-compose.linux.yml

        if docker compose -f docker-compose.linux.yml build --no-cache; then
            log_success "npm构建成功!"
            build_success=true
        else
            log_error "npm构建也失败了"
            # 恢复原始配置
            mv docker-compose.linux.yml.bak docker-compose.linux.yml
        fi
    fi

    # 方式3: 使用标准Dockerfile
    if [ "$build_success" = false ] && [ -f "Dockerfile" ]; then
        log_info "使用标准Dockerfile构建..."
        sed -i.bak 's/dockerfile: Dockerfile.linux/dockerfile: Dockerfile/' docker-compose.linux.yml

        if docker compose -f docker-compose.linux.yml build --no-cache; then
            log_success "标准Dockerfile构建成功!"
            build_success=true
        else
            log_error "所有构建方式都失败了"
            # 恢复原始配置
            mv docker-compose.linux.yml.bak docker-compose.linux.yml
            exit 1
        fi
    fi

    if [ "$build_success" = false ]; then
        log_error "无法构建Docker镜像"
        exit 1
    fi

    # 启动服务
    log_info "启动服务..."
    docker compose -f docker-compose.linux.yml up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 20

    # 检查服务状态
    if docker compose -f docker-compose.linux.yml ps | grep -q "Up"; then
        log_success "应用部署成功!"
    else
        log_error "应用部署失败"
        docker compose -f docker-compose.linux.yml logs
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo
    echo -e "${GREEN}=========================================="
    echo "🎉 部署完成!"
    echo "==========================================${NC}"
    echo
    echo -e "${CYAN}📱 应用访问信息:${NC}"
    echo "  主页: http://localhost:3000"
    echo "  健康检查: http://localhost:3000/api/health"
    echo
    echo -e "${CYAN}🔧 管理命令:${NC}"
    echo "  查看日志: docker compose -f docker-compose.linux.yml logs -f"
    echo "  停止服务: docker compose -f docker-compose.linux.yml down"
    echo "  重启服务: docker compose -f docker-compose.linux.yml restart"
    echo "  查看状态: docker compose -f docker-compose.linux.yml ps"
    echo
    echo -e "${CYAN}📊 服务状态:${NC}"
    docker compose -f docker-compose.linux.yml ps
    echo
    echo -e "${YELLOW}💡 提示:${NC}"
    echo "  - 如果是首次运行，请确保已正确配置.env.production文件"
    echo "  - 生产环境建议配置Nginx反向代理和SSL证书"
    echo "  - 定期备份数据库和日志文件"
    echo
}

# 主函数
main() {
    show_banner
    
    # 检查参数
    case "${1:-}" in
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --help, -h     显示帮助信息"
            echo "  --no-docker    跳过Docker安装"
            echo "  --dev          开发模式部署"
            exit 0
            ;;
        --no-docker)
            SKIP_DOCKER=true
            ;;
        --dev)
            DEV_MODE=true
            ;;
    esac
    
    detect_os
    check_requirements
    check_project_files
    
    if [ "${SKIP_DOCKER:-}" != "true" ]; then
        install_docker
    fi
    
    setup_environment
    deploy_application
    show_deployment_info
    
    log_success "Linux部署完成!"
}

# 运行主函数
main "$@"
