/**
 * 完整邮件服务
 * 基于project2的EmailVerificationHandler，支持多种邮件模式
 */

interface EmailConfig {
  tempMail?: string
  domain: string
  imapServer?: string
  imapPort?: number
  imapUser?: string
  imapPass?: string
  imapDir?: string
  protocol?: 'IMAP' | 'POP3'
}

interface EmailMessage {
  id: string
  from: string
  to: string
  subject: string
  text: string
  html?: string
  date: string
  attachments?: any[]
}

interface VerificationResult {
  code: string | null
  message: string
  success: boolean
  attempts: number
}

export class EmailService {
  private config: EmailConfig
  private useImap: boolean

  constructor() {
    this.config = {
      tempMail: process.env.TEMP_MAIL || 'null',
      domain: process.env.DOMAIN || 'mcpserver.sbs',
      imapServer: process.env.IMAP_SERVER,
      imapPort: parseInt(process.env.IMAP_PORT || '993'),
      imapUser: process.env.IMAP_USER,
      imapPass: process.env.IMAP_PASS,
      imapDir: process.env.IMAP_DIR || 'INBOX',
      protocol: (process.env.IMAP_PROTOCOL as 'IMAP' | 'POP3') || 'IMAP'
    }

    // 总是使用tempmail创建邮箱，但验证码通过IMAP获取
    this.useImap = false // 邮箱创建始终使用tempmail
  }

  /**
   * 创建邮箱账户
   * 总是创建临时邮箱，邮件会转发到IMAP邮箱
   */
  async createEmailAccount(): Promise<{
    email: string
    password: string
    type: 'tempmail-with-imap'
    config: any
  }> {
    // 创建临时邮箱（邮件会转发到IMAP邮箱）
    const username = this.generateRandomUsername()
    const email = `${username}@${this.config.domain}`
    const password = this.generateRandomPassword()

    return {
      email,
      password,
      type: 'tempmail-with-imap',
      config: {
        tempDomain: this.config.domain,
        imapServer: this.config.imapServer,
        imapPort: this.config.imapPort,
        imapUser: this.config.imapUser,
        protocol: this.config.protocol,
        description: '临时邮箱，邮件转发到IMAP邮箱获取验证码'
      }
    }
  }

  /**
   * 获取验证码
   * 临时邮箱收到邮件后会转发到IMAP邮箱，从IMAP邮箱获取验证码
   */
  async getVerificationCode(
    email: string,
    maxRetries: number = 5,
    retryInterval: number = 30000
  ): Promise<VerificationResult> {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        console.log(`尝试从IMAP邮箱获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`)
        console.log(`临时邮箱: ${email}`)
        console.log(`IMAP邮箱: ${this.config.imapUser}`)

        // 总是通过IMAP获取验证码（因为邮件会转发到IMAP邮箱）
        const code = await this.getCodeByImap(email)

        if (code) {
          return {
            code,
            message: `验证码获取成功 (从IMAP邮箱: ${this.config.imapUser})`,
            success: true,
            attempts: attempt + 1
          }
        }

        if (attempt < maxRetries - 1) {
          console.log(`未获取到验证码，${retryInterval / 1000} 秒后重试...`)
          await new Promise(resolve => setTimeout(resolve, retryInterval))
        }

      } catch (error) {
        console.error(`获取验证码失败: ${error}`)
        if (attempt < maxRetries - 1) {
          console.error(`发生错误，${retryInterval / 1000} 秒后重试...`)
          await new Promise(resolve => setTimeout(resolve, retryInterval))
        }
      }
    }

    return {
      code: null,
      message: `经过 ${maxRetries} 次尝试后仍未从IMAP邮箱获取到验证码`,
      success: false,
      attempts: maxRetries
    }
  }

  /**
   * 通过IMAP获取验证码
   * 从转发到IMAP邮箱的邮件中提取验证码
   */
  private async getCodeByImap(tempEmail: string): Promise<string | null> {
    // 这里需要实现IMAP连接逻辑
    // 由于浏览器环境限制，实际的IMAP连接需要在服务端实现
    // 服务端会：
    // 1. 连接到IMAP服务器 (imap.qq.com:993)
    // 2. 搜索包含临时邮箱地址的邮件
    // 3. 提取邮件中的6位数字验证码
    // 4. 删除已处理的邮件
    throw new Error('IMAP验证码获取需要在服务端实现')
  }

  /**
   * 通过POP3获取验证码
   */
  private async getCodeByPop3(email: string): Promise<string | null> {
    // 这里需要实现POP3连接逻辑
    // 由于浏览器环境限制，实际的POP3连接需要在服务端实现
    throw new Error('POP3模式需要在服务端实现')
  }

  /**
   * 通过tempmail.plus获取验证码
   */
  private async getCodeByTempmail(email: string): Promise<string | null> {
    try {
      const [username, domain] = email.split('@')
      const extension = `@${domain}`
      
      // 获取邮件列表
      const listUrl = `${process.env.NEXT_PUBLIC_TEMPMAIL_PLUS_API_BASE}/mails?email=${username}${extension}&limit=20`
      const listResponse = await fetch(listUrl)
      const listData = await listResponse.json()
      
      if (!listData.result || !listData.first_id) {
        return null
      }

      // 获取最新邮件内容
      const mailUrl = `${process.env.NEXT_PUBLIC_TEMPMAIL_PLUS_API_BASE}/mails/${listData.first_id}?email=${username}${extension}`
      const mailResponse = await fetch(mailUrl)
      const mailData = await mailResponse.json()
      
      if (!mailData.result) {
        return null
      }

      // 提取验证码
      const mailText = mailData.text || ''
      const codeMatch = mailText.match(/(?<![a-zA-Z@.])\b\d{6}\b/)
      
      if (codeMatch) {
        // 删除邮件
        await this.deleteTempmail(email, listData.first_id)
        return codeMatch[0]
      }

      return null
    } catch (error) {
      console.error('Tempmail获取验证码失败:', error)
      return null
    }
  }

  /**
   * 删除tempmail邮件
   */
  private async deleteTempmail(email: string, mailId: string): Promise<boolean> {
    try {
      const [username, domain] = email.split('@')
      const extension = `@${domain}`
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_TEMPMAIL_PLUS_API_BASE}/mails/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          email: `${username}${extension}`,
          first_id: mailId
        })
      })

      const data = await response.json()
      return data.result === true
    } catch (error) {
      console.error('删除tempmail邮件失败:', error)
      return false
    }
  }

  /**
   * 获取邮箱收件箱
   * 可以查看临时邮箱的直接收件箱（用于测试）
   */
  async getInbox(email: string): Promise<EmailMessage[]> {
    // 查看临时邮箱的直接收件箱（用于测试邮件转发是否正常）
    try {
      const [username, domain] = email.split('@')
      const extension = `@${domain}`

      const url = `${process.env.NEXT_PUBLIC_TEMPMAIL_PLUS_API_BASE}/mails?email=${username}${extension}&limit=20`
      const response = await fetch(url)
      const data = await response.json()

      if (!data.result) {
        return []
      }

      return data.result.map((mail: any) => ({
        id: mail.id,
        from: mail.from,
        to: mail.to,
        subject: mail.subject,
        text: mail.text,
        html: mail.html,
        date: mail.date,
        attachments: mail.attachments || []
      }))
    } catch (error) {
      console.error('获取临时邮箱收件箱失败:', error)
      return []
    }
  }

  /**
   * 生成随机用户名
   */
  private generateRandomUsername(): string {
    const names = [
      'john', 'jane', 'mike', 'sarah', 'david', 'lisa', 'tom', 'mary',
      'james', 'anna', 'robert', 'emma', 'william', 'olivia', 'richard', 'sophia'
    ]
    
    const name = names[Math.floor(Math.random() * names.length)]
    const timestamp = Date.now().toString().slice(-6)
    
    return `${name}${timestamp}`
  }

  /**
   * 生成随机密码
   */
  private generateRandomPassword(length: number = 12): string {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
    let password = ''
    
    for (let i = 0; i < length; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    
    return password
  }

  /**
   * 获取配置信息
   */
  getConfig() {
    return {
      mode: 'Tempmail + IMAP',
      description: '临时邮箱 + IMAP转发模式',
      tempDomain: this.config.domain,
      imapServer: this.config.imapServer,
      imapPort: this.config.imapPort,
      imapUser: this.config.imapUser ? this.config.imapUser.replace(/(.{3}).*(@.*)/, '$1***$2') : undefined,
      protocol: this.config.protocol,
      workflow: [
        '1. 创建临时邮箱 (如: <EMAIL>)',
        '2. 邮件自动转发到IMAP邮箱',
        '3. 通过IMAP获取验证码'
      ]
    }
  }
}

// 导出默认实例
export const emailService = new EmailService()

// 导出类型
export type { EmailConfig, EmailMessage, VerificationResult }
