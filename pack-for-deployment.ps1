# 美国地址生成器 - Docker部署包打包工具 (PowerShell版本)
# 使用方法: .\pack-for-deployment.ps1

param(
    [string]$OutputName = "us-fake-gen-ui-deploy",
    [switch]$SkipCompression = $false,
    [switch]$Verbose = $false
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "🚀 美国地址生成器 - Docker部署包打包工具" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# 生成时间戳
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$finalName = "${OutputName}_${timestamp}"

Write-Host "📦 开始打包项目..." -ForegroundColor Green
Write-Host "📅 时间戳: $timestamp" -ForegroundColor Yellow
Write-Host "📁 包名: $finalName" -ForegroundColor Yellow
Write-Host ""

# 检查当前目录是否为项目根目录
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误: 请在项目根目录运行此脚本" -ForegroundColor Red
    Write-Host "   当前目录应包含 package.json 文件" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 清理旧的打包目录
if (Test-Path $finalName) {
    Write-Host "🧹 清理旧的打包目录..." -ForegroundColor Yellow
    Remove-Item -Path $finalName -Recurse -Force
}

# 创建打包目录
New-Item -ItemType Directory -Path $finalName -Force | Out-Null

Write-Host "📁 创建目录结构..." -ForegroundColor Green

# 定义要复制的目录
$directories = @("app", "components", "lib", "hooks", "contexts", "database", "scripts", "public", "styles")

foreach ($dir in $directories) {
    if (Test-Path $dir) {
        Write-Host "  ✅ 复制目录: $dir" -ForegroundColor Green
        Copy-Item -Path $dir -Destination $finalName -Recurse -Force
        if ($Verbose) {
            $fileCount = (Get-ChildItem -Path "$finalName\$dir" -Recurse -File).Count
            Write-Host "     包含 $fileCount 个文件" -ForegroundColor Gray
        }
    } else {
        Write-Host "  ⚠️  目录不存在: $dir" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📄 复制配置文件..." -ForegroundColor Green

# 定义要复制的文件
$files = @(
    "Dockerfile",
    "docker-compose.yml", 
    "docker-compose.dev.yml",
    "nginx.conf",
    ".dockerignore",
    ".env.local.example",
    ".env.production",
    "package.json",
    "pnpm-lock.yaml",
    "next.config.mjs",
    "tsconfig.json",
    "tailwind.config.ts",
    "postcss.config.mjs",
    "components.json",
    "middleware.ts"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "  ✅ 复制文件: $file" -ForegroundColor Green
        Copy-Item -Path $file -Destination $finalName -Force
    } else {
        Write-Host "  ⚠️  文件不存在: $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📚 复制文档文件..." -ForegroundColor Green

# 定义要复制的文档文件
$docs = @("README.md", "DOCKER_DEPLOYMENT.md", "DOCKER_QUICKSTART.md", "SETUP.md")

foreach ($doc in $docs) {
    if (Test-Path $doc) {
        Write-Host "  ✅ 复制文档: $doc" -ForegroundColor Green
        Copy-Item -Path $doc -Destination $finalName -Force
    } else {
        Write-Host "  ⚠️  文档不存在: $doc" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📝 创建部署说明文件..." -ForegroundColor Green

# 创建部署说明文件
$deploymentReadme = @"
# 美国地址生成器 Docker 部署包

**打包时间**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")  
**打包工具**: PowerShell 自动化脚本

## 🚀 快速部署步骤

### 1. 环境准备
- 确保已安装 [Docker Desktop](https://www.docker.com/products/docker-desktop)
- 确保 Docker 服务正在运行

### 2. 配置环境变量
``````bash
# 复制环境变量模板
copy .env.local.example .env.local

# 编辑 .env.local 文件，配置以下必需变量：
# - NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
# - NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
# - ADMIN_USERNAME=your_admin_username
# - ADMIN_PASSWORD=your_secure_password
# - JWT_SECRET=your_jwt_secret_32_chars_min
``````

### 3. 部署应用
``````bash
# 方式一：使用部署脚本（推荐）
scripts\deploy.bat dev

# 方式二：直接使用 Docker Compose
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
``````

### 4. 访问应用
- **应用主页**: http://localhost:3000
- **健康检查**: http://localhost:3000/api/health
- **登录页面**: http://localhost:3000/login

## 📁 文件说明

| 文件/目录 | 说明 |
|-----------|------|
| `Dockerfile` | Docker镜像构建文件 |
| `docker-compose.yml` | 生产环境配置 |
| `docker-compose.dev.yml` | 开发环境配置 |
| `nginx.conf` | Nginx反向代理配置 |
| `.env.local.example` | 环境变量模板 |
| `scripts/deploy.bat` | Windows部署脚本 |
| `app/` | Next.js应用源码 |
| `components/` | React组件 |
| `lib/` | 工具库和服务 |
| `database/` | 数据库结构文件 |

## 🛠️ 常用命令

``````bash
# 查看服务状态
docker-compose ps

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f

# 清理资源
docker-compose down && docker image prune -f
``````

## ⚠️ 注意事项

1. **安全配置**
   - 请勿将 `.env.local` 文件提交到版本控制
   - 生产环境请使用强密码
   - 定期更新 JWT_SECRET

2. **网络配置**
   - 确保防火墙允许 3000 端口访问
   - 生产环境建议使用 HTTPS

3. **资源要求**
   - 至少 2GB 可用内存
   - 至少 5GB 可用磁盘空间

## 📚 更多文档

- `DOCKER_DEPLOYMENT.md` - 详细部署指南
- `DOCKER_QUICKSTART.md` - 5分钟快速开始
- `README.md` - 项目说明文档

## 🆘 故障排除

如果遇到问题，请查看：
1. Docker Desktop 是否正常运行
2. 端口 3000 是否被占用
3. 环境变量是否正确配置
4. 查看容器日志: `docker-compose logs`

---
*此部署包由自动化脚本生成，包含完整的 Docker 部署配置*
"@

$deploymentReadme | Out-File -FilePath "$finalName\DEPLOYMENT_README.md" -Encoding UTF8
Write-Host "  ✅ 创建部署说明: DEPLOYMENT_README.md" -ForegroundColor Green

Write-Host ""
Write-Host "🔍 检查打包内容..." -ForegroundColor Green

# 统计文件和目录
$fileCount = (Get-ChildItem -Path $finalName -Recurse -File).Count
$dirCount = (Get-ChildItem -Path $finalName -Recurse -Directory).Count
$totalSize = (Get-ChildItem -Path $finalName -Recurse -File | Measure-Object -Property Length -Sum).Sum

Write-Host "  📊 统计信息:" -ForegroundColor Cyan
Write-Host "     - 文件数量: $fileCount" -ForegroundColor White
Write-Host "     - 目录数量: $dirCount" -ForegroundColor White
Write-Host "     - 总大小: $([math]::Round($totalSize/1MB, 2)) MB" -ForegroundColor White

# 创建压缩包
if (-not $SkipCompression) {
    Write-Host ""
    Write-Host "📦 创建压缩包..." -ForegroundColor Green
    
    try {
        $zipPath = "$finalName.zip"
        Compress-Archive -Path "$finalName\*" -DestinationPath $zipPath -Force
        $zipSize = (Get-Item $zipPath).Length
        Write-Host "  ✅ 压缩包创建成功: $zipPath" -ForegroundColor Green
        Write-Host "     压缩后大小: $([math]::Round($zipSize/1MB, 2)) MB" -ForegroundColor White
        Write-Host "     压缩率: $([math]::Round((1-$zipSize/$totalSize)*100, 1))%" -ForegroundColor White
    } catch {
        Write-Host "  ❌ 压缩包创建失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "     请手动压缩 $finalName 文件夹" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "🎉 打包完成！" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📦 部署包位置:" -ForegroundColor Cyan
if (-not $SkipCompression -and (Test-Path "$finalName.zip")) {
    Write-Host "   压缩包: $finalName.zip" -ForegroundColor White
}
Write-Host "   文件夹: $finalName\" -ForegroundColor White

Write-Host ""
Write-Host "📋 下一步操作:" -ForegroundColor Cyan
Write-Host "   1. 将部署包传输到目标服务器" -ForegroundColor White
Write-Host "   2. 解压并按照 DEPLOYMENT_README.md 说明部署" -ForegroundColor White
Write-Host "   3. 访问 http://localhost:3000 验证部署" -ForegroundColor White

Write-Host ""
Write-Host "📚 相关文档:" -ForegroundColor Cyan
Write-Host "   - DOCKER_DEPLOYMENT.md - 详细部署指南" -ForegroundColor White
Write-Host "   - DOCKER_QUICKSTART.md - 快速开始指南" -ForegroundColor White
Write-Host "   - DEPLOYMENT_README.md - 部署包说明" -ForegroundColor White

Write-Host ""
Read-Host "按任意键退出"
