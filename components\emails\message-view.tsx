"use client"

import { useState, useEffect, useRef } from "react"
import { Loader2, Mail, Calendar, User, Copy, Check, FileText, BarChart3 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"

interface Message {
  id: string
  emailId: string
  fromAddress: string
  toAddress: string
  subject: string
  textContent?: string
  htmlContent?: string | null
  receivedAt: number
  isRead: boolean
  hasVerificationCode: boolean
  verificationCode?: string
}

interface MessageViewProps {
  message: Message | null
  loading?: boolean
}

export function MessageView({ message, loading = false }: MessageViewProps) {
  const [copiedField, setCopiedField] = useState<string | null>(null)
  const { toast } = useToast()

  // 复制到剪贴板
  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(fieldName)
      toast({
        title: "复制成功",
        description: `${fieldName}已复制到剪贴板`,
      })
      setTimeout(() => setCopiedField(null), 2000)
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="w-5 h-5 animate-spin text-primary/60" />
      </div>
    )
  }

  if (!message) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center">
          <Mail className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium">请选择一封邮件</p>
          <p className="text-sm text-gray-400">查看邮件详细内容</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 邮件头部信息 */}
      <div className="p-4 space-y-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-start justify-between">
          <h3 className="text-lg font-bold text-gray-900 flex-1 pr-4">
            {message.subject || '无主题'}
          </h3>
          {!message.isRead && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              未读
            </Badge>
          )}
        </div>
        
        <div className="grid grid-cols-1 gap-3 text-sm">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-gray-500" />
            <span className="text-gray-500 font-medium">发件人:</span>
            <span className="text-gray-700 flex-1">{message.fromAddress}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(message.fromAddress, "发件人")}
              className="h-6 w-6 p-0"
            >
              {copiedField === "发件人" ? 
                <Check className="h-3 w-3 text-green-600" /> : 
                <Copy className="h-3 w-3 text-gray-500" />
              }
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4 text-gray-500" />
            <span className="text-gray-500 font-medium">收件人:</span>
            <span className="text-gray-700 flex-1">{message.toAddress}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(message.toAddress, "收件人")}
              className="h-6 w-6 p-0"
            >
              {copiedField === "收件人" ? 
                <Check className="h-3 w-3 text-green-600" /> : 
                <Copy className="h-3 w-3 text-gray-500" />
              }
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-500" />
            <span className="text-gray-500 font-medium">时间:</span>
            <span className="text-gray-700">
              {new Date(message.receivedAt).toLocaleString('zh-CN')}
            </span>
          </div>
        </div>
      </div>

      {/* 验证码检测 */}
      {message.hasVerificationCode && message.verificationCode && (
        <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm font-semibold text-green-800">验证码检测</span>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-green-700 text-sm mb-1">✅ 发现6位验证码</div>
              <div className="text-2xl font-bold text-green-600 font-mono tracking-wider">
                {message.verificationCode}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(message.verificationCode!, "验证码")}
              className="h-8 w-8 p-0 hover:bg-green-100"
            >
              {copiedField === "验证码" ?
                <Check className="h-4 w-4 text-green-600" /> :
                <Copy className="h-4 w-4 text-green-600" />
              }
            </Button>
          </div>
        </div>
      )}

      {/* 邮件内容 */}
      <div className="flex-1 overflow-auto">
        <div className="p-4">
          <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
            <div className="text-gray-800 whitespace-pre-wrap leading-relaxed text-sm">
              {message.textContent || '无内容'}
            </div>
          </div>

          {/* 内容统计 */}
          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="bg-blue-50 rounded-lg p-3 text-center">
              <div className="text-lg font-bold text-blue-600">
                {message.textContent?.length || 0}
              </div>
              <div className="text-xs text-blue-500">字符数</div>
            </div>
            <div className="bg-green-50 rounded-lg p-3 text-center">
              <div className="text-lg font-bold text-green-600">
                {message.textContent?.split('\n').length || 0}
              </div>
              <div className="text-xs text-green-500">行数</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3 text-center">
              <div className="text-lg font-bold text-purple-600">
                {message.textContent?.split(' ').length || 0}
              </div>
              <div className="text-xs text-purple-500">单词数</div>
            </div>
            <div className="bg-orange-50 rounded-lg p-3 text-center">
              <div className="text-lg font-bold text-orange-600">
                {message.textContent && /\d/.test(message.textContent) ? '是' : '否'}
              </div>
              <div className="text-xs text-orange-500">包含数字</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
