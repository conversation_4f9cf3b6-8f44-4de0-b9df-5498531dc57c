# 数据库表结构说明

## 项目概述
美国地址生成器项目包含3个主要数据表，用于存储生成的个人数据和邮件管理功能。

## 表结构详情

### 1. saved_person_data (个人数据表)
**用途**: 存储生成的美国个人信息数据

**主要字段**:
- `id` - 主键 (UUID)
- `created_at` - 创建时间
- `full_name` - 完整姓名 (必填)
- `first_name`, `last_name` - 姓名分解
- `gender` - 性别
- `birthday` - 生日
- `email` - 邮箱地址
- `phone` - 电话号码
- `street`, `city`, `state`, `zip_code` - 地址信息
- `occupation`, `company`, `salary` - 工作信息
- `ssn`, `card_number`, `cvv` - 金融信息
- `school_name`, `university_name` - 教育信息
- `generation_seed` - 生成种子
- `generation_params` - 生成参数 (JSON)

### 2. emails (邮箱管理表)
**用途**: 邮件管理系统的邮箱存储

**主要字段**:
- `id` - 主键 (UUID)
- `created_at` - 创建时间
- `address` - 邮箱地址 (唯一)
- `password` - 邮箱密码
- `type` - 邮箱类型 (默认: tempmail)
- `config` - 配置信息 (JSON)
- `is_active` - 是否激活
- `created_at_timestamp` - 时间戳 (毫秒)
- `unread_count` - 未读邮件数
- `has_new_messages` - 是否有新消息

### 3. messages (邮件消息表)
**用途**: 存储邮箱接收的邮件消息

**主要字段**:
- `id` - 主键 (UUID)
- `email_id` - 关联邮箱ID (外键)
- `from_address` - 发件人
- `to_address` - 收件人
- `subject` - 邮件主题
- `text_content` - 文本内容
- `html_content` - HTML内容
- `received_at` - 接收时间戳 (毫秒)
- `is_read` - 是否已读
- `has_verification_code` - 是否包含验证码
- `verification_code` - 验证码内容

## 索引说明

### 性能优化索引:
- `idx_saved_person_data_created_at` - 按创建时间查询
- `idx_saved_person_data_email` - 按邮箱查询
- `idx_emails_address` - 按邮箱地址查询
- `idx_messages_email_id` - 按邮箱ID查询消息
- `idx_messages_received_at` - 按接收时间查询

## 关系说明

```
saved_person_data (个人数据)
    ↓ (导入功能)
emails (邮箱管理)
    ↓ (一对多)
messages (邮件消息)
```

## 安全策略
- 所有表启用行级安全策略 (RLS)
- 开发环境允许所有操作
- 生产环境需要根据需求限制权限

## 快速部署

### 方法1: 使用完整脚本
```sql
-- 执行 database/complete-schema.sql
```

### 方法2: 使用快速脚本
```sql
-- 执行 database/quick-setup.sql
```

### 方法3: 手动创建
1. 复制 `database/quick-setup.sql` 内容
2. 登录 Supabase 控制台
3. 进入 SQL 编辑器
4. 粘贴并执行

## 验证安装
执行以下查询验证表是否创建成功:

```sql
-- 检查表是否存在
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('saved_person_data', 'emails', 'messages');

-- 检查索引
SELECT indexname 
FROM pg_indexes 
WHERE tablename IN ('saved_person_data', 'emails', 'messages');
```

## 故障排除

### 常见问题:
1. **表不存在** - 执行 quick-setup.sql
2. **权限错误** - 检查 RLS 策略
3. **外键约束** - 确保 emails 表先于 messages 表创建

### 重置数据库:
```sql
-- 谨慎使用：删除所有表
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS emails CASCADE;
DROP TABLE IF EXISTS saved_person_data CASCADE;
```
