-- 最简化版本 - 只创建必要的表和字段
-- 复制以下内容到数据库执行

-- 1. 个人数据表（简化版）
CREATE TABLE saved_person_data (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  full_name TEXT NOT NULL,
  first_name TEXT,
  last_name TEXT,
  gender TEXT,
  birthday TEXT,
  street TEXT,
  city TEXT,
  state TEXT,
  state_full_name TEXT,
  zip_code TEXT,
  phone TEXT,
  email TEXT,
  full_address TEXT,
  occupation TEXT,
  company TEXT,
  ssn TEXT,
  card_type TEXT,
  card_number TEXT,
  cvv INTEGER,
  expiry TEXT,
  username TEXT,
  password TEXT,
  height TEXT,
  weight TEXT,
  blood_type TEXT,
  education TEXT,
  school_name TEXT,
  university_name TEXT,
  generation_seed INTEGER,
  generation_params JSONB
);

-- 2. 邮箱表
CREATE TABLE emails (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  address TEXT NOT NULL UNIQUE,
  password TEXT,
  type TEXT DEFAULT 'tempmail',
  config JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at_timestamp BIGINT DEFAULT EXTRACT(epoch FROM NOW()) * 1000,
  unread_count INTEGER DEFAULT 0,
  has_new_messages BOOLEAN DEFAULT false
);

-- 3. 消息表
CREATE TABLE messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  email_id UUID REFERENCES emails(id) ON DELETE CASCADE,
  from_address TEXT NOT NULL,
  to_address TEXT NOT NULL,
  subject TEXT,
  text_content TEXT,
  html_content TEXT,
  received_at BIGINT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  has_verification_code BOOLEAN DEFAULT false,
  verification_code TEXT
);

-- 4. 基本索引
CREATE INDEX idx_saved_person_data_created_at ON saved_person_data(created_at);
CREATE INDEX idx_saved_person_data_email ON saved_person_data(email);
CREATE INDEX idx_emails_address ON emails(address);
CREATE INDEX idx_messages_email_id ON messages(email_id);
