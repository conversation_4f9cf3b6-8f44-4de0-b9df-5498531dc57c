import { NextRequest, NextResponse } from 'next/server'
import { extractTokenFromRequest, verifyJWT } from '@/lib/jwt'
import { blacklistToken, isTokenBlacklisted } from '@/lib/token-blacklist'
import { stopEmailSyncOnLogout } from '@/lib/email-sync-manager'
import { removeActiveSession, removeActiveSessionByToken } from '@/lib/session-manager'

/**
 * POST /api/auth/logout
 * 用户登出接口
 */
export async function POST(request: NextRequest) {
  try {
    // 提取token
    const token = extractTokenFromRequest(request)
    
    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: '未找到认证token' 
        },
        { status: 401 }
      )
    }
    
    // 验证token
    const payload = await verifyJWT(token)
    
    if (!payload) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'token无效或已过期' 
        },
        { status: 401 }
      )
    }
    
    // 检查token是否已经在黑名单中
    if (isTokenBlacklisted(token)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'token已失效' 
        },
        { status: 401 }
      )
    }
    
    // 将token添加到黑名单
    blacklistToken(token, payload.exp)

    // 移除活跃会话（通过token查找，更可靠）
    const sessionRemoved = removeActiveSessionByToken(token)
    if (!sessionRemoved) {
      // 如果通过token没找到，尝试通过sessionId移除
      removeActiveSession(payload.sessionId)
    }

    // 停止邮件同步
    try {
      const syncResult = await stopEmailSyncOnLogout()
      console.log('邮件同步停止结果:', syncResult)
    } catch (syncError) {
      console.error('停止邮件同步失败:', syncError)
      // 不影响登出流程，只记录错误
    }

    // 创建响应
    const response = NextResponse.json({
      success: true,
      message: '登出成功',
      user: {
        username: payload.username,
        sessionId: payload.sessionId
      }
    })
    
    // 清除cookie
    response.headers.set('Set-Cookie', [
      'auth-token=; HttpOnly; SameSite=Strict; Path=/; Max-Age=0',
    ].join('; '))
    
    return response
    
  } catch (error) {
    console.error('登出API错误:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误' 
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/auth/logout
 * 返回登出接口信息
 */
export async function GET() {
  return NextResponse.json({
    message: '请使用 POST 方法进行登出',
    description: '需要在请求头中包含 Authorization: Bearer <token>'
  })
}
