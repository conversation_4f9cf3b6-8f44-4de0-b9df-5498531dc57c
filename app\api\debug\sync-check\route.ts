import { NextRequest, NextResponse } from 'next/server'
import { hasActiveSessions, getActiveSessionCount } from '@/lib/session-manager'
import { syncSchedule } from '@/app/api/email-sync/schedule/route'

/**
 * GET /api/debug/sync-check
 * 调试接口：检查同步状态和用户会话
 */
export async function GET(request: NextRequest) {
  try {
    const hasActive = hasActiveSessions()
    const sessionCount = getActiveSessionCount()

    return NextResponse.json({
      success: true,
      data: {
        hasActiveSessions: hasActive,
        activeSessionCount: sessionCount,
        syncSchedule: {
          isRunning: syncSchedule.isRunning,
          interval: syncSchedule.interval,
          lastRun: syncSchedule.lastRun,
          nextRun: syncSchedule.nextRun
        },
        shouldStopSync: !hasActive && syncSchedule.isRunning,
        message: hasActive 
          ? `有 ${sessionCount} 个活跃会话，同步应该继续运行`
          : `没有活跃会话，同步应该停止`
      }
    })
  } catch (error) {
    console.error('检查同步状态失败:', error)
    return NextResponse.json({
      success: false,
      error: '检查同步状态失败'
    }, { status: 500 })
  }
}
