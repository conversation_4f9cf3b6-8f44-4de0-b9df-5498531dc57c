import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

/**
 * 获取数据中心的邮件列表
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search') || ''
    const excludeExisting = searchParams.get('excludeExisting') === 'true'

    const offset = (page - 1) * limit

    // 获取数据中心的邮件列表
    let query = supabase
      .from('saved_person_data')
      .select('id, full_name, email, created_at, city, state', { count: 'exact' })
      .not('email', 'is', null)
      .neq('email', '')

    // 搜索功能
    if (search) {
      query = query.or(`email.ilike.%${search}%,full_name.ilike.%${search}%`)
    }

    // 如果需要排除已存在的邮件
    if (excludeExisting) {
      // 先获取邮件管理系统中已存在的邮件地址
      const { data: existingEmails } = await supabase
        .from('emails')
        .select('address')
      
      if (existingEmails && existingEmails.length > 0) {
        const existingAddresses = existingEmails.map(e => e.address)
        query = query.not('email', 'in', `(${existingAddresses.map(addr => `"${addr}"`).join(',')})`)
      }
    }

    // 分页和排序
    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('获取邮件列表失败:', error)
      return NextResponse.json({
        success: false,
        error: '获取邮件列表失败: ' + error.message
      }, { status: 500 })
    }

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      success: true,
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '服务器错误'
    }, { status: 500 })
  }
}

/**
 * 批量导入邮件到邮件管理系统
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { emailIds, importType = 'tempmail' } = body

    if (!emailIds || !Array.isArray(emailIds) || emailIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: '请选择要导入的邮件'
      }, { status: 400 })
    }

    // 获取要导入的邮件数据
    const { data: emailsToImport, error: fetchError } = await supabase
      .from('saved_person_data')
      .select('id, full_name, email, password')
      .in('id', emailIds)
      .not('email', 'is', null)
      .neq('email', '')

    if (fetchError) {
      return NextResponse.json({
        success: false,
        error: '获取邮件数据失败: ' + fetchError.message
      }, { status: 500 })
    }

    if (!emailsToImport || emailsToImport.length === 0) {
      return NextResponse.json({
        success: false,
        error: '没有找到有效的邮件数据'
      }, { status: 400 })
    }

    // 检查重复邮件
    const emailAddresses = emailsToImport.map(e => e.email)
    const { data: existingEmails } = await supabase
      .from('emails')
      .select('address')
      .in('address', emailAddresses)

    const existingAddresses = new Set(existingEmails?.map(e => e.address) || [])
    
    // 准备导入的邮件数据
    const emailsToInsert = emailsToImport
      .filter(email => !existingAddresses.has(email.email))
      .map(email => ({
        address: email.email,
        password: email.password || 'imported',
        type: importType,
        config: {
          source: 'data_center',
          originalId: email.id,
          fullName: email.full_name,
          importedAt: new Date().toISOString()
        },
        created_at_timestamp: Date.now(),
        is_active: true
      }))

    let successCount = 0
    let skipCount = existingAddresses.size
    let failCount = 0

    // 批量插入邮件
    if (emailsToInsert.length > 0) {
      const { data: insertedEmails, error: insertError } = await supabase
        .from('emails')
        .insert(emailsToInsert)
        .select()

      if (insertError) {
        console.error('批量插入邮件失败:', insertError)
        failCount = emailsToInsert.length
      } else {
        successCount = insertedEmails?.length || 0
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        total: emailIds.length,
        success: successCount,
        skipped: skipCount,
        failed: failCount,
        details: {
          imported: successCount,
          alreadyExists: skipCount,
          errors: failCount
        }
      },
      message: `导入完成：成功 ${successCount} 个，跳过 ${skipCount} 个，失败 ${failCount} 个`
    })

  } catch (error) {
    console.error('导入邮件失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '导入失败'
    }, { status: 500 })
  }
}
