@echo off
echo.
echo ==========================================
echo Linux Deployment Package Builder
echo ==========================================
echo.

REM Set variables
set PACKAGE_NAME=us-fake-gen-ui-linux-deploy
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set FINAL_NAME=%PACKAGE_NAME%_%TIMESTAMP%

echo [INFO] Starting Linux deployment package creation...
echo [INFO] Package name: %FINAL_NAME%
echo [INFO] Time: %date% %time%
echo.

REM Check project root directory
if not exist "package.json" (
    echo [ERROR] Please run this script from project root directory
    echo [ERROR] Current directory should contain package.json file
    pause
    exit /b 1
)

REM Clean old package directory
if exist %FINAL_NAME% (
    echo [INFO] Cleaning old package directory...
    rmdir /s /q %FINAL_NAME%
)

REM Create new directory
mkdir %FINAL_NAME%
echo [SUCCESS] Package directory created: %FINAL_NAME%

echo.
echo [STEP] Copying core directories...

REM Copy core directories
set DIRS=app components lib hooks contexts database scripts public styles
for %%d in (%DIRS%) do (
    if exist %%d (
        echo [OK] Copying directory: %%d
        xcopy %%d %FINAL_NAME%\%%d /e /i /h /y /q
    ) else (
        echo [WARN] Directory not found: %%d
    )
)

echo.
echo [STEP] Copying configuration files...

REM Copy configuration files
set CONFIG_FILES=package.json next.config.mjs tsconfig.json tailwind.config.ts postcss.config.mjs components.json middleware.ts .env.local.example .env.production
for %%f in (%CONFIG_FILES%) do (
    if exist %%f (
        echo [OK] Copying config file: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo [WARN] Config file not found: %%f
    )
)

echo.
echo [STEP] Copying Docker files...

REM Copy Docker files
set DOCKER_FILES=Dockerfile Dockerfile.linux docker-compose.yml docker-compose.linux.yml docker-compose.dev.yml .dockerignore
for %%f in (%DOCKER_FILES%) do (
    if exist %%f (
        echo [OK] Copying Docker file: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo [WARN] Docker file not found: %%f
    )
)

REM Copy nginx configuration
if exist nginx (
    echo [OK] Copying nginx config directory
    xcopy nginx %FINAL_NAME%\nginx /e /i /h /y /q
) else (
    echo [WARN] nginx directory not found
)

if exist nginx.conf (
    echo [OK] Copying nginx.conf
    copy nginx.conf %FINAL_NAME%\ >nul
)

echo.
echo [STEP] Copying deployment scripts...

REM Copy deployment scripts
set SCRIPT_FILES=deploy-linux.sh pack-for-linux.sh deploy-docker.bat check-docker-env.bat
for %%f in (%SCRIPT_FILES%) do (
    if exist %%f (
        echo [OK] Copying script: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo [WARN] Script file not found: %%f
    )
)

echo.
echo [STEP] Copying documentation files...

REM Copy documentation files
set DOC_FILES=README.md SETUP.md DOCKER_DEPLOYMENT.md DOCKER_QUICKSTART.md PACKAGING_CHECKLIST.md MANUAL_PACKAGING_GUIDE.md
for %%f in (%DOC_FILES%) do (
    if exist %%f (
        echo [OK] Copying document: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo [WARN] Document file not found: %%f
    )
)

echo.
echo [STEP] Creating Linux deployment guide...

REM Create Linux deployment guide
(
echo # Linux Deployment Guide
echo.
echo ## Quick Deployment
echo.
echo ### Automatic Deployment ^(Recommended^)
echo ```bash
echo # 1. Extract deployment package
echo tar -xzf %FINAL_NAME%.tar.gz
echo cd %FINAL_NAME%
echo.
echo # 2. Run automatic deployment script
echo chmod +x deploy-linux.sh
echo ./deploy-linux.sh
echo ```
echo.
echo ### Manual Deployment
echo ```bash
echo # 1. Install Docker
echo curl -fsSL https://get.docker.com ^| sh
echo sudo systemctl start docker
echo sudo systemctl enable docker
echo.
echo # 2. Configure environment variables
echo cp .env.local.example .env.production
echo # Edit .env.production file
echo.
echo # 3. Build and start
echo docker compose -f docker-compose.linux.yml up --build -d
echo.
echo # 4. Access application
echo # http://localhost:3000
echo ```
echo.
echo ## System Requirements
echo.
echo - **OS**: Ubuntu 18.04+, Debian 10+, CentOS 7+, RHEL 7+
echo - **Memory**: Minimum 2GB, Recommended 4GB+
echo - **Disk**: Minimum 5GB available space
echo - **Network**: Internet access required for dependencies
echo.
echo ## Required Environment Variables
echo.
echo ```env
echo NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
echo NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
echo ADMIN_USERNAME=your_admin_username
echo ADMIN_PASSWORD=your_secure_password
echo JWT_SECRET=your_jwt_secret_32_chars_min
echo ```
echo.
echo ## Management Commands
echo.
echo ```bash
echo # Check service status
echo docker compose -f docker-compose.linux.yml ps
echo.
echo # View logs
echo docker compose -f docker-compose.linux.yml logs -f
echo.
echo # Restart service
echo docker compose -f docker-compose.linux.yml restart
echo.
echo # Stop service
echo docker compose -f docker-compose.linux.yml down
echo ```
echo.
echo ---
echo *Package time: %date% %time%*
echo *This deployment package contains complete Linux deployment configuration*
) > %FINAL_NAME%\LINUX_DEPLOYMENT.md

echo [SUCCESS] Linux deployment guide created

echo.
echo [STEP] Creating quick start script...

REM Create quick start script
(
echo #!/bin/bash
echo.
echo # US Fake Gen UI - Quick Start Script
echo.
echo echo "US Fake Gen UI - Quick Start"
echo echo "================================"
echo.
echo # Check Docker
echo if ! command -v docker ^&^> /dev/null; then
echo     echo "Docker not installed, installing..."
echo     curl -fsSL https://get.docker.com ^| sh
echo     sudo systemctl start docker
echo     sudo systemctl enable docker
echo     sudo usermod -aG docker $USER
echo     echo "Docker installation complete, please re-login and run this script again"
echo     exit 0
echo fi
echo.
echo # Check environment variables file
echo if [ ! -f ".env.production" ]; then
echo     if [ -f ".env.local.example" ]; then
echo         cp .env.local.example .env.production
echo         echo "Created .env.production file, please edit configuration and run again"
echo         echo "Required configuration items:"
echo         echo "   - NEXT_PUBLIC_SUPABASE_URL"
echo         echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY"
echo         echo "   - ADMIN_USERNAME"
echo         echo "   - ADMIN_PASSWORD"
echo         echo "   - JWT_SECRET"
echo         exit 1
echo     fi
echo fi
echo.
echo # Start services
echo echo "Starting services..."
echo docker compose -f docker-compose.linux.yml up -d
echo.
echo echo "Startup complete!"
echo echo "Access URL: http://localhost:3000"
echo echo "Check status: docker compose -f docker-compose.linux.yml ps"
echo echo "View logs: docker compose -f docker-compose.linux.yml logs -f"
) > %FINAL_NAME%\quick-start.sh

echo [SUCCESS] Quick start script created

echo.
echo [STEP] Collecting package statistics...

REM Count files and directories
for /f %%i in ('dir /s /b %FINAL_NAME% ^| find /c /v ""') do set FILE_COUNT=%%i
for /f %%i in ('dir /s /ad %FINAL_NAME% ^| find /c /v ""') do set DIR_COUNT=%%i

echo [STATS] Package statistics:
echo    Files: %FILE_COUNT%
echo    Directories: %DIR_COUNT%

echo.
echo [STEP] Creating compressed package...

REM Try to create compressed package
powershell -command "Compress-Archive -Path '%FINAL_NAME%\*' -DestinationPath '%FINAL_NAME%.zip' -Force" 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Compressed package created: %FINAL_NAME%.zip
) else (
    echo [WARN] Cannot create ZIP package, please manually compress %FINAL_NAME% folder
)

echo.
echo ==========================================
echo Linux Deployment Package Complete!
echo ==========================================
echo.
echo Package Information:
echo    Folder: %FINAL_NAME%\
if exist %FINAL_NAME%.zip (
    echo    Compressed: %FINAL_NAME%.zip
)
echo.
echo Linux Server Usage:
echo    1. Transfer deployment package to Linux server
if exist %FINAL_NAME%.zip (
    echo    2. Extract: unzip %FINAL_NAME%.zip
) else (
    echo    2. Extract folder to Linux server
)
echo    3. Enter directory: cd %FINAL_NAME%
echo    4. Set permissions: chmod +x *.sh
echo    5. Run deployment: ./deploy-linux.sh
echo    6. Or quick start: ./quick-start.sh
echo.
echo Related Documentation:
echo    - LINUX_DEPLOYMENT.md - Linux deployment guide
echo    - README.md - Project description
echo    - SETUP.md - Setup guide
echo.
echo Tips:
echo    - Ensure Docker is installed on Linux server
echo    - Configure environment variables in .env.production file
echo    - Recommend SSL certificate and domain for production
echo.

pause
