"use client"

import { useState } from "react"
import { Mail, Calendar, RefreshCw, Trash2, Shield, Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"

interface Message {
  id: string
  emailId: string
  fromAddress: string
  toAddress: string
  subject: string
  textContent?: string
  htmlContent?: string | null
  receivedAt: number
  isRead: boolean
  hasVerificationCode: boolean
  verificationCode?: string
}

interface MessageListProps {
  messages: Message[]
  loading: boolean
  onMessageSelect: (message: Message) => void | Promise<void>
  selectedMessageId?: string | null
  onRefresh?: () => void
  refreshing?: boolean
  onDeleteMessage?: (messageId: string) => void
}

export function MessageList({ 
  messages, 
  loading, 
  onMessageSelect, 
  selectedMessageId,
  onRefresh,
  refreshing = false,
  onDeleteMessage
}: MessageListProps) {
  const [messageToDelete, setMessageToDelete] = useState<Message | null>(null)
  const { toast } = useToast()

  const handleDelete = async (message: Message) => {
    try {
      if (onDeleteMessage) {
        await onDeleteMessage(message.id)
        toast({
          title: "删除成功",
          description: "邮件已删除"
        })
      }
    } catch (error) {
      toast({
        title: "删除失败",
        description: "删除邮件时发生错误",
        variant: "destructive"
      })
    } finally {
      setMessageToDelete(null)
    }
  }

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60)
      return `${diffInMinutes}分钟前`
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}小时前`
    } else if (diffInHours < 48) {
      return '昨天'
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      })
    }
  }

  return (
    <>
      <div className="h-full flex flex-col">
        {/* 头部工具栏 */}
        <div className="p-3 flex justify-between items-center border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              disabled={refreshing}
              className={cn("h-8 w-8 p-0", refreshing && "animate-spin")}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            <span className="text-sm text-gray-600">
              {loading ? "加载中..." : `${messages.length} 封邮件`}
            </span>
          </div>
          
          {messages.some(m => m.hasVerificationCode) && (
            <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
              <Shield className="w-3 h-3 mr-1" />
              含验证码
            </Badge>
          )}
        </div>

        {/* 邮件列表 */}
        <div className="flex-1 overflow-auto">
          {loading ? (
            <div className="p-6 text-center text-gray-500">
              <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
              加载邮件中...
            </div>
          ) : messages.length > 0 ? (
            <div className="divide-y divide-gray-100">
              {messages.map((message) => (
                <div
                  key={message.id}
                  onClick={() => onMessageSelect(message)}
                  className={cn(
                    "p-4 hover:bg-gray-50 cursor-pointer group transition-colors",
                    selectedMessageId === message.id && "bg-blue-50 border-l-4 border-blue-500"
                  )}
                >
                  <div className="flex items-start gap-3">
                    {/* 邮件图标 */}
                    <div className="flex-shrink-0 mt-1">
                      <Mail className={cn(
                        "w-4 h-4",
                        message.isRead ? "text-gray-400" : "text-blue-500"
                      )} />
                    </div>
                    
                    {/* 邮件内容 */}
                    <div className="min-w-0 flex-1">
                      {/* 主题行 */}
                      <div className="flex items-center gap-2 mb-1">
                        <span className={cn(
                          "text-sm truncate flex-1",
                          message.isRead ? "text-gray-700" : "font-semibold text-gray-900"
                        )}>
                          {message.subject || '无主题'}
                        </span>
                        
                        {/* 状态标识 */}
                        <div className="flex items-center gap-1 flex-shrink-0">
                          {message.hasVerificationCode && (
                            <Badge variant="default" className="text-xs bg-green-100 text-green-800 px-1.5 py-0.5">
                              验证码
                            </Badge>
                          )}
                          {!message.isRead && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                      
                      {/* 发件人 */}
                      <div className="text-xs text-gray-600 truncate mb-1">
                        来自: {message.fromAddress}
                      </div>
                      
                      {/* 时间和操作 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Clock className="w-3 h-3" />
                          {formatDate(message.receivedAt)}
                        </div>
                        
                        {/* 删除按钮 */}
                        {onDeleteMessage && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 hover:bg-red-50"
                            onClick={(e) => {
                              e.stopPropagation()
                              setMessageToDelete(message)
                            }}
                          >
                            <Trash2 className="h-3 w-3 text-red-500" />
                          </Button>
                        )}
                      </div>
                      
                      {/* 内容预览 */}
                      {message.textContent && (
                        <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                          {message.textContent.substring(0, 100)}
                          {message.textContent.length > 100 && '...'}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              <Mail className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p className="text-sm">暂无邮件</p>
              <p className="text-xs text-gray-400">邮件将自动显示在这里</p>
            </div>
          )}
        </div>
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={!!messageToDelete} onOpenChange={() => setMessageToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              确认删除邮件
            </AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除邮件 "{messageToDelete?.subject || '无主题'}" 吗？
              <br />
              <span className="text-xs text-gray-500">
                发件人: {messageToDelete?.fromAddress}
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={() => messageToDelete && handleDelete(messageToDelete)}
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
