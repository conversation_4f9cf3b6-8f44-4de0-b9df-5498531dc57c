/**
 * 身份验证中间件工具
 * 用于API路由的身份验证检查
 */

import { NextRequest, NextResponse } from 'next/server'
import { extractTokenFromRequest, verifyJWT, JWTPayload } from './jwt'
import { isTokenBlacklisted } from './token-blacklist'

export interface AuthenticatedRequest extends NextRequest {
  user?: JWTPayload
}

export interface AuthMiddlewareOptions {
  required?: boolean // 是否必须登录
  adminOnly?: boolean // 是否只允许管理员
  permissions?: string[] // 需要的权限列表
}

/**
 * 身份验证中间件结果
 */
export interface AuthResult {
  success: boolean
  user?: JWTPayload
  error?: string
  statusCode?: number
}

/**
 * 检查用户权限
 */
function checkPermissions(user: JWTPayload, options: AuthMiddlewareOptions): boolean {
  // 检查是否只允许管理员
  if (options.adminOnly && user.username !== 'admin') {
    return false
  }
  
  // 检查特定权限（可以根据需要扩展）
  if (options.permissions && options.permissions.length > 0) {
    // 简单的权限检查逻辑
    for (const permission of options.permissions) {
      switch (permission) {
        case 'admin':
          if (user.username !== 'admin') return false
          break
        case 'email-sync':
          // 所有登录用户都可以使用邮件同步
          break
        default:
          // 未知权限，拒绝访问
          return false
      }
    }
  }
  
  return true
}

/**
 * 身份验证中间件函数
 */
export async function authenticateRequest(
  request: NextRequest, 
  options: AuthMiddlewareOptions = {}
): Promise<AuthResult> {
  try {
    // 提取token
    const token = extractTokenFromRequest(request)
    
    if (!token) {
      if (options.required !== false) {
        return {
          success: false,
          error: '需要登录访问',
          statusCode: 401
        }
      } else {
        return { success: true }
      }
    }
    
    // 验证token
    const payload = await verifyJWT(token)
    
    if (!payload) {
      return {
        success: false,
        error: 'token无效或已过期',
        statusCode: 401
      }
    }
    
    // 检查token是否在黑名单中
    if (isTokenBlacklisted(token)) {
      return {
        success: false,
        error: 'token已失效',
        statusCode: 401
      }
    }
    
    // 检查权限
    if (!checkPermissions(payload, options)) {
      return {
        success: false,
        error: '权限不足',
        statusCode: 403
      }
    }
    
    return {
      success: true,
      user: payload
    }
    
  } catch (error) {
    console.error('身份验证中间件错误:', error)
    return {
      success: false,
      error: '身份验证失败',
      statusCode: 500
    }
  }
}

/**
 * 创建需要身份验证的API处理器
 */
export function withAuth(
  handler: (request: AuthenticatedRequest, user: JWTPayload) => Promise<NextResponse>,
  options: AuthMiddlewareOptions = { required: true }
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const authResult = await authenticateRequest(request, options)
    
    if (!authResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: authResult.error 
        },
        { status: authResult.statusCode || 401 }
      )
    }
    
    // 将用户信息添加到请求对象
    const authenticatedRequest = request as AuthenticatedRequest
    if (authResult.user) {
      authenticatedRequest.user = authResult.user
    }
    
    return handler(authenticatedRequest, authResult.user!)
  }
}

/**
 * 创建可选身份验证的API处理器
 */
export function withOptionalAuth(
  handler: (request: AuthenticatedRequest, user?: JWTPayload) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const authResult = await authenticateRequest(request, { required: false })
    
    // 将用户信息添加到请求对象（如果存在）
    const authenticatedRequest = request as AuthenticatedRequest
    if (authResult.user) {
      authenticatedRequest.user = authResult.user
    }
    
    return handler(authenticatedRequest, authResult.user)
  }
}

/**
 * 创建只允许管理员的API处理器
 */
export function withAdminAuth(
  handler: (request: AuthenticatedRequest, user: JWTPayload) => Promise<NextResponse>
) {
  return withAuth(handler, { required: true, adminOnly: true })
}

/**
 * 快速身份验证检查（用于简单的API路由）
 */
export async function requireAuth(request: NextRequest): Promise<NextResponse | null> {
  const authResult = await authenticateRequest(request, { required: true })
  
  if (!authResult.success) {
    return NextResponse.json(
      { 
        success: false, 
        error: authResult.error 
      },
      { status: authResult.statusCode || 401 }
    )
  }
  
  return null // 认证成功，返回null表示继续处理
}

/**
 * 快速管理员权限检查
 */
export async function requireAdmin(request: NextRequest): Promise<NextResponse | null> {
  const authResult = await authenticateRequest(request, { required: true, adminOnly: true })
  
  if (!authResult.success) {
    return NextResponse.json(
      { 
        success: false, 
        error: authResult.error 
      },
      { status: authResult.statusCode || 401 }
    )
  }
  
  return null // 认证成功，返回null表示继续处理
}
