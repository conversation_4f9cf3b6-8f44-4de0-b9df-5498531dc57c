-- 创建邮件管理系统的邮箱表
CREATE TABLE IF NOT EXISTS emails (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 邮箱基本信息
  address TEXT NOT NULL UNIQUE,
  password TEXT,
  type TEXT DEFAULT 'tempmail',
  config JSONB,
  
  -- 状态信息
  is_active BOOLEAN DEFAULT true,
  created_at_timestamp BIGINT DEFAULT EXTRACT(epoch FROM NOW()) * 1000,
  
  -- 统计信息
  unread_count INTEGER DEFAULT 0,
  has_new_messages BOOLEAN DEFAULT false
);

-- 创建邮件消息表
CREATE TABLE IF NOT EXISTS messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 关联邮箱
  email_id UUID REFERENCES emails(id) ON DELETE CASCADE,
  
  -- 邮件信息
  from_address TEXT NOT NULL,
  to_address TEXT NOT NULL,
  subject TEXT,
  text_content TEXT,
  html_content TEXT,
  
  -- 状态信息
  received_at BIGINT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  has_verification_code BOOLEAN DEFAULT false,
  verification_code TEXT
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_emails_address ON emails(address);
CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_email_id ON messages(email_id);
CREATE INDEX IF NOT EXISTS idx_messages_received_at ON messages(received_at);
