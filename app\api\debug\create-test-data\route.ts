import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // 不再创建硬编码的测试数据，改为生成真实数据
    return NextResponse.json({
      success: false,
      error: '测试数据API已禁用，请使用真实数据生成功能'
    }, { status: 400 })

    // 以下代码已禁用
    /*
    const testData = [
      {
        full_name: '张三',
        first_name: '三',
        last_name: '张',
        gender: '男',
        birthday: '1990-05-15',
        title: 'Mr.',
        hair_color: '黑色',
        country: 'United States',
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        state_full_name: 'New York',
        zip_code: '10001',
        phone: '(*************',
        email: 'zhang<PERSON>@example.com',
        full_address: '123 Main St, New York, NY 10001',
        occupation: '软件工程师',
        company: 'Tech Corp',
        company_size: '100-500',
        industry: '科技',
        status: '在职',
        salary: '$75,000',
        ssn: '***********',
        card_type: 'Visa',
        card_number: '****************',
        cvv: 123,
        expiry: '12/25',
        username: 'zhangsan123',
        password: 'password123',
        security_question: '您的第一只宠物叫什么名字？',
        security_answer: '小白',
        height: '175cm',
        weight: '70kg',
        blood_type: 'A+',
        os: 'Windows 11',
        guid: '12345678-1234-1234-1234-123456789012',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        education: '本科',
        website: 'https://zhangsan.com',
        generation_seed: 12345,
        generation_params: JSON.stringify({ test: true })
      },
      {
        full_name: '李四',
        first_name: '四',
        last_name: '李',
        gender: '女',
        birthday: '1985-08-22',
        title: 'Ms.',
        hair_color: '棕色',
        country: 'United States',
        street: '456 Oak Ave',
        city: 'Los Angeles',
        state: 'CA',
        state_full_name: 'California',
        zip_code: '90210',
        phone: '(*************',
        email: '<EMAIL>',
        full_address: '456 Oak Ave, Los Angeles, CA 90210',
        occupation: '产品经理',
        company: 'Design Studio',
        company_size: '50-100',
        industry: '设计',
        status: '在职',
        salary: '$85,000',
        ssn: '***********',
        card_type: 'MasterCard',
        card_number: '****************',
        cvv: 456,
        expiry: '06/26',
        username: 'lisi456',
        password: 'securepass456',
        security_question: '您出生的城市是哪里？',
        security_answer: '北京',
        height: '165cm',
        weight: '55kg',
        blood_type: 'B+',
        os: 'macOS',
        guid: '*************-4321-4321-************',
        user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        education: '硕士',
        website: 'https://lisi.design',
        generation_seed: 67890,
        generation_params: JSON.stringify({ test: true })
      },
      {
        full_name: '王五',
        first_name: '五',
        last_name: '王',
        gender: '男',
        birthday: '1992-12-03',
        title: 'Mr.',
        hair_color: '黑色',
        country: 'United States',
        street: '789 Pine Rd',
        city: 'Chicago',
        state: 'IL',
        state_full_name: 'Illinois',
        zip_code: '60601',
        phone: '(*************',
        email: '<EMAIL>',
        full_address: '789 Pine Rd, Chicago, IL 60601',
        occupation: '数据分析师',
        company: 'Analytics Inc',
        company_size: '200-500',
        industry: '金融',
        status: '在职',
        salary: '$65,000',
        ssn: '***********',
        card_type: 'American Express',
        card_number: '***************',
        cvv: 789,
        expiry: '09/27',
        username: 'wangwu789',
        password: 'mypassword789',
        security_question: '您最喜欢的颜色是什么？',
        security_answer: '蓝色',
        height: '180cm',
        weight: '75kg',
        blood_type: 'O+',
        os: 'Ubuntu',
        guid: '11111111-**************-************',
        user_agent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        education: '本科',
        website: 'https://wangwu.analytics',
        generation_seed: 11111,
        generation_params: JSON.stringify({ test: true })
      }
    ]

    // 批量插入测试数据
    const { data, error } = await supabase
      .from('saved_person_data')
      .insert(testData)
      .select()

    if (error) {
      console.error('Supabase error:', error)
      return NextResponse.json(
        { success: false, error: '创建测试数据失败: ' + error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: data,
      message: `成功创建 ${data.length} 条测试数据`,
      count: data.length
    })
    */

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : '服务器错误' },
      { status: 500 }
    )
  }
}
