import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import fs from 'fs'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    // 读取数据库schema文件
    const schemaPath = path.join(process.cwd(), 'database', 'schema.sql')
    
    if (!fs.existsSync(schemaPath)) {
      return NextResponse.json({
        success: false,
        error: '数据库schema文件不存在'
      }, { status: 404 })
    }

    const schemaSQL = fs.readFileSync(schemaPath, 'utf-8')
    
    // 分割SQL语句（简单的分割，基于分号）
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    const results = []
    let successCount = 0
    let errorCount = 0

    // 执行每个SQL语句
    for (const statement of statements) {
      try {
        // 跳过注释行
        if (statement.startsWith('--') || statement.startsWith('/*')) {
          continue
        }

        // 使用Supabase的rpc功能执行SQL
        const { data, error } = await supabase.rpc('exec_sql', { 
          sql_query: statement 
        })

        if (error) {
          // 如果是"已存在"错误，我们认为是成功的
          if (error.message.includes('already exists') || 
              error.message.includes('duplicate key') ||
              error.message.includes('relation') && error.message.includes('already exists')) {
            results.push({
              statement: statement.substring(0, 100) + '...',
              success: true,
              message: '已存在（跳过）'
            })
            successCount++
          } else {
            results.push({
              statement: statement.substring(0, 100) + '...',
              success: false,
              error: error.message
            })
            errorCount++
          }
        } else {
          results.push({
            statement: statement.substring(0, 100) + '...',
            success: true,
            message: '执行成功'
          })
          successCount++
        }
      } catch (err) {
        results.push({
          statement: statement.substring(0, 100) + '...',
          success: false,
          error: err instanceof Error ? err.message : '未知错误'
        })
        errorCount++
      }
    }

    // 验证表是否创建成功
    let tableExists = false
    try {
      const { data, error } = await supabase
        .from('saved_person_data')
        .select('count', { count: 'exact', head: true })
      
      tableExists = !error
    } catch (err) {
      // 忽略验证错误
    }

    return NextResponse.json({
      success: errorCount === 0 || tableExists,
      message: `数据库初始化完成。成功: ${successCount}, 失败: ${errorCount}`,
      tableExists,
      results,
      summary: {
        totalStatements: statements.length,
        successCount,
        errorCount
      }
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '数据库初始化失败',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// 简化版本：直接创建表（如果Supabase不支持rpc）
export async function PUT(request: NextRequest) {
  try {
    // 直接尝试创建表的简化版本
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS saved_person_data (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        full_name TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        gender TEXT,
        birthday TEXT,
        title TEXT,
        hair_color TEXT,
        country TEXT,
        street TEXT,
        city TEXT,
        state TEXT,
        state_full_name TEXT,
        zip_code TEXT,
        phone TEXT,
        email TEXT,
        full_address TEXT,
        occupation TEXT,
        company TEXT,
        company_size TEXT,
        industry TEXT,
        status TEXT,
        salary TEXT,
        ssn TEXT,
        card_type TEXT,
        card_number TEXT,
        cvv INTEGER,
        expiry TEXT,
        username TEXT,
        password TEXT,
        security_question TEXT,
        security_answer TEXT,
        height TEXT,
        weight TEXT,
        blood_type TEXT,
        os TEXT,
        guid TEXT,
        user_agent TEXT,
        education TEXT,
        website TEXT,
        school_name TEXT,
        school_id TEXT,
        school_zip TEXT,
        school_website TEXT,
        school_address TEXT,
        school_city TEXT,
        school_state TEXT,
        school_phone TEXT,
        school_grades TEXT,
        university_name TEXT,
        university_id TEXT,
        university_zip TEXT,
        university_website TEXT,
        university_address TEXT,
        university_city TEXT,
        university_state TEXT,
        university_phone TEXT,
        university_type TEXT,
        generation_seed INTEGER,
        generation_params JSONB
      );
    `

    // 注意：Supabase客户端库通常不支持直接执行DDL语句
    // 这里我们返回SQL语句，让用户手动在Supabase控制台执行
    return NextResponse.json({
      success: true,
      message: '请在Supabase控制台的SQL编辑器中执行以下SQL语句',
      sql: createTableSQL,
      instructions: [
        '1. 登录到 Supabase 控制台',
        '2. 进入 SQL 编辑器',
        '3. 复制并执行上面的SQL语句',
        '4. 或者直接执行 database/schema.sql 文件中的内容'
      ]
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '生成SQL失败'
    }, { status: 500 })
  }
}
