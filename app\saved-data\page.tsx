"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Trash2,
  Eye,
  ChevronLeft,
  ChevronRight,
  Home,
  RefreshCw,
  Calendar,
  User,
  Mail,
  MapPin,
  FileSpreadsheet,
  Database,
  BarChart3,
  Settings,
  Download,
  Filter,
  SortAsc,
  SortDesc
} from "lucide-react"
import { toast } from "@/hooks/use-toast"
import Link from "next/link"
import { SavedPersonData } from "@/lib/supabase"
import DataDetailsDialog from "@/components/data-details-dialog"
import PersonEmailsDialog from "@/components/person-emails-dialog"
import ErrorDisplay from "@/components/error-display"

interface Stats {
  total: number
  withEmail: number
  withoutEmail: number
  canExport: boolean
  genderDistribution: { male: number; female: number; other: number }
  recentCount: number
}

export default function SavedDataPage() {
  const [data, setData] = useState<SavedPersonData[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState("")
  const [sortBy, setSortBy] = useState("created_at")
  const [sortOrder, setSortOrder] = useState("desc")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [total, setTotal] = useState(0)
  const [selectedItem, setSelectedItem] = useState<SavedPersonData | null>(null)
  const [showDetails, setShowDetails] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [stats, setStats] = useState<Stats | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [emailsDialogOpen, setEmailsDialogOpen] = useState(false)
  const [selectedPersonForEmails, setSelectedPersonForEmails] = useState<SavedPersonData | null>(null)

  const fetchData = async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "12", // 增加每页显示数量
        search,
        sortBy,
        sortOrder
      })

      const response = await fetch(`/api/saved-data?${params}`)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.success) {
        setData(result.data)
        setTotal(result.pagination.total)
        setTotalPages(result.pagination.totalPages)
        setRetryCount(0) // 重置重试计数
      } else {
        throw new Error(result.error || '获取数据失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "获取数据时发生错误"
      setError(errorMessage)

      // 只在非网络错误时显示 toast
      if (!errorMessage.includes('fetch') && !errorMessage.includes('network')) {
        toast({
          title: "获取数据失败",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const [statsResponse, exportResponse] = await Promise.all([
        fetch('/api/stats'),
        fetch('/api/export/excel')
      ])

      const statsResult = await statsResponse.json()
      const exportResult = await exportResponse.json()

      if (statsResult.success && exportResult.success) {
        const statsData = statsResult.data
        const exportData = exportResult.data

        setStats({
          total: exportData.total || 0,
          withEmail: exportData.withEmail || 0,
          withoutEmail: exportData.withoutEmail || 0,
          canExport: exportData.canExport || false,
          genderDistribution: statsData.savedData?.genderDistribution || { male: 0, female: 0, other: 0 },
          recentCount: statsData.savedData?.recent || 0
        })
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  const deleteItem = async (id: string) => {
    if (!confirm('确定要删除这条数据吗？')) return

    try {
      const response = await fetch(`/api/saved-data?id=${id}`, {
        method: 'DELETE'
      })
      const result = await response.json()

      if (result.success) {
        toast({
          title: "删除成功",
          description: "数据已删除",
        })
        fetchData() // 重新获取数据
      } else {
        throw new Error(result.error || '删除失败')
      }
    } catch (error) {
      toast({
        title: "删除失败",
        description: error instanceof Error ? error.message : "删除数据时发生错误",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    fetchData()
    fetchStats()
  }, [currentPage, search, sortBy, sortOrder])

  useEffect(() => {
    fetchStats()
  }, [])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const handleSearch = (value: string) => {
    setSearch(value)
    setCurrentPage(1) // 重置到第一页
  }

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    fetchData()
  }

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
    setCurrentPage(1)
  }

  const exportToExcel = async () => {
    setExporting(true)
    try {
      const response = await fetch('/api/export/excel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          limit: 1000,
          offset: 0
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '导出失败')
      }

      // 获取文件名
      const contentDisposition = response.headers.get('content-disposition')
      const filename = contentDisposition
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
        : `数据导出_${new Date().toISOString().split('T')[0]}.xlsx`

      // 下载文件
      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: "导出成功",
        description: "Excel文件已下载",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "导出Excel时发生错误",
        variant: "destructive",
      })
    } finally {
      setExporting(false)
    }
  }

  // 检查个人邮件记录
  const checkPersonEmails = (person: SavedPersonData) => {
    setSelectedPersonForEmails(person)
    setEmailsDialogOpen(true)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">数据管理中心</h1>
            <p className="text-lg text-gray-600">统一管理和查看已保存的生成数据</p>
          </div>
          <div className="flex gap-2">
            <Link href="/debug">
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                系统诊断
              </Button>
            </Link>
            <Link href="/">
              <Button variant="outline">
                <Home className="mr-2 h-4 w-4" />
                返回首页
              </Button>
            </Link>
          </div>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Database className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">总数据量</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Mail className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">包含邮箱</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.withEmail}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <User className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">最近7天</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.recentCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">性别分布</p>
                    <p className="text-sm text-gray-900">
                      男:{stats.genderDistribution.male} 女:{stats.genderDistribution.female}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 搜索和操作栏 */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索姓名、邮箱、城市或州..."
                    value={search}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="排序字段" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at">创建时间</SelectItem>
                    <SelectItem value="full_name">姓名</SelectItem>
                    <SelectItem value="email">邮箱</SelectItem>
                    <SelectItem value="city">城市</SelectItem>
                    <SelectItem value="state">州</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                >
                  {sortOrder === 'asc' ? (
                    <SortAsc className="h-4 w-4" />
                  ) : (
                    <SortDesc className="h-4 w-4" />
                  )}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    fetchData()
                    fetchStats()
                  }}
                  disabled={loading}
                >
                  <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  刷新
                </Button>

                <Button
                  onClick={exportToExcel}
                  disabled={exporting || total === 0}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white"
                  size="sm"
                >
                  {exporting ? (
                    <>
                      <Download className="mr-2 h-4 w-4 animate-spin" />
                      导出中...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      导出Excel
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 统计信息和视图切换 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="text-sm">
              共 {total} 条记录，第 {currentPage} / {totalPages} 页
            </Badge>
            {search && (
              <Badge variant="secondary" className="text-sm">
                搜索结果: {data.length} 条
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              网格视图
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              列表视图
            </Button>
          </div>
        </div>

        {/* 错误显示 */}
        {error && !loading && (
          <div className="mb-6">
            <ErrorDisplay
              error={error}
              context="获取已保存数据"
              onRetry={handleRetry}
              showDetails={true}
              suggestions={[
                '检查网络连接是否正常',
                '确认数据库服务是否运行',
                '尝试刷新页面',
                '查看系统诊断页面'
              ]}
            />
          </div>
        )}

        {/* 数据显示区域 */}
        {loading ? (
          <div className="text-center py-12">
            <RefreshCw className="mx-auto h-8 w-8 animate-spin text-gray-400 mb-4" />
            <p className="text-gray-500">加载中...</p>
          </div>
        ) : data.length === 0 ? (
          <div className="text-center py-12">
            <Database className="mx-auto h-16 w-16 text-gray-300 mb-4" />
            <p className="text-gray-500 text-lg mb-2">
              {search ? '没有找到匹配的数据' : '暂无数据'}
            </p>
            {!search && (
              <p className="text-gray-400 text-sm mb-4">请先在主页生成一些数据</p>
            )}
            <Link href="/">
              <Button>
                <Home className="mr-2 h-4 w-4" />
                去生成数据
              </Button>
            </Link>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {data.map((item) => (
              <Card key={item.id} className="hover:shadow-lg transition-all duration-200 hover:scale-105">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-semibold text-gray-800 truncate">
                      {item.full_name}
                    </CardTitle>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedItem(item)
                          setShowDetails(true)
                        }}
                        className="h-8 w-8 p-0"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {item.email && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => checkPersonEmails(item)}
                          className="h-8 w-8 p-0 text-blue-600 hover:text-blue-800"
                          title="查看邮件记录"
                        >
                          <Database className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteItem(item.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <User className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{item.gender} • {item.birthday}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{item.email}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{item.city}, {item.state}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{formatDate(item.created_at)}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                数据列表
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.map((item) => (
                  <div
                    key={item.id}
                    className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                      {/* 基本信息 */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-blue-600" />
                          <span className="font-semibold text-gray-900">{item.full_name}</span>
                          <Badge variant={item.gender === 'Male' ? 'default' : 'secondary'} className="text-xs">
                            {item.gender}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600">{item.birthday}</div>
                      </div>

                      {/* 联系信息 */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-green-600" />
                          <span className="text-sm text-gray-900 font-mono">{item.email}</span>
                        </div>
                        <div className="text-sm text-gray-600">{item.phone}</div>
                      </div>

                      {/* 地址信息 */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-orange-600" />
                          <span className="text-sm text-gray-900">{item.city}, {item.state}</span>
                        </div>
                        <div className="text-sm text-gray-600">{item.zip_code}</div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center justify-end gap-2">
                        <div className="text-xs text-gray-500">
                          {formatDate(item.created_at)}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedItem(item)
                            setShowDetails(true)
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {item.email && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => checkPersonEmails(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="查看邮件记录"
                          >
                            <Database className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteItem(item.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-4 mt-8">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              上一页
            </Button>
            <span className="text-sm text-gray-600">
              第 {currentPage} / {totalPages} 页
            </span>
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              下一页
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* 详情对话框 */}
        <DataDetailsDialog
          open={showDetails}
          onOpenChange={setShowDetails}
          data={selectedItem}
        />

        {/* 邮件记录对话框 */}
        <PersonEmailsDialog
          open={emailsDialogOpen}
          onOpenChange={setEmailsDialogOpen}
          email={selectedPersonForEmails?.email || null}
          personName={selectedPersonForEmails?.full_name || ''}
        />
      </div>
    </div>
  )
}
