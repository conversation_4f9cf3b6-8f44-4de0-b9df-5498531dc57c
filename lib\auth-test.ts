/**
 * 身份验证测试工具
 * 用于测试身份验证系统的各种功能和场景
 */

export interface TestResult {
  name: string
  success: boolean
  message: string
  duration: number
  details?: any
}

export interface TestSuite {
  name: string
  tests: TestResult[]
  totalTests: number
  passedTests: number
  failedTests: number
  duration: number
}

/**
 * 身份验证测试类
 */
export class AuthTester {
  private baseUrl: string
  private testResults: TestResult[] = []

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3002')
  }

  /**
   * 执行单个测试
   */
  private async runTest(name: string, testFn: () => Promise<any>): Promise<TestResult> {
    const startTime = Date.now()
    
    try {
      const result = await testFn()
      const duration = Date.now() - startTime
      
      return {
        name,
        success: true,
        message: '测试通过',
        duration,
        details: result
      }
    } catch (error) {
      const duration = Date.now() - startTime
      
      return {
        name,
        success: false,
        message: error instanceof Error ? error.message : '测试失败',
        duration,
        details: error
      }
    }
  }

  /**
   * 测试登录API - 正确凭据
   */
  async testValidLogin(): Promise<TestResult> {
    return this.runTest('有效登录测试', async () => {
      const response = await fetch(`${this.baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: 'admin',
          password: 'admin123456'
        })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(`登录失败: ${data.error || response.statusText}`)
      }

      if (!data.success || !data.token) {
        throw new Error('登录响应格式错误')
      }

      return { token: data.token, user: data.user }
    })
  }

  /**
   * 测试登录API - 错误凭据
   */
  async testInvalidLogin(): Promise<TestResult> {
    return this.runTest('无效登录测试', async () => {
      const response = await fetch(`${this.baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: 'admin',
          password: 'wrongpassword'
        })
      })

      const data = await response.json()
      
      if (response.ok) {
        throw new Error('错误凭据应该登录失败')
      }

      if (response.status !== 401) {
        throw new Error(`期望状态码401，实际${response.status}`)
      }

      return { status: response.status, error: data.error }
    })
  }

  /**
   * 测试身份验证状态检查
   */
  async testAuthStatus(token?: string): Promise<TestResult> {
    return this.runTest('身份验证状态测试', async () => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`${this.baseUrl}/api/auth/status`, {
        method: 'GET',
        headers
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(`状态检查失败: ${data.error || response.statusText}`)
      }

      return { 
        isAuthenticated: data.isAuthenticated, 
        user: data.user,
        session: data.session 
      }
    })
  }

  /**
   * 测试受保护的API访问
   */
  async testProtectedApiAccess(token?: string): Promise<TestResult> {
    return this.runTest('受保护API访问测试', async () => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`${this.baseUrl}/api/saved-data`, {
        method: 'GET',
        headers
      })

      const data = await response.json()
      
      if (token) {
        // 有token时应该成功
        if (!response.ok) {
          throw new Error(`有token时API访问失败: ${data.error || response.statusText}`)
        }
        return { status: 'authorized', data }
      } else {
        // 无token时应该失败
        if (response.ok) {
          throw new Error('无token时API访问应该失败')
        }
        if (response.status !== 401) {
          throw new Error(`期望状态码401，实际${response.status}`)
        }
        return { status: 'unauthorized', error: data.error }
      }
    })
  }

  /**
   * 测试登出功能
   */
  async testLogout(token: string): Promise<TestResult> {
    return this.runTest('登出功能测试', async () => {
      const response = await fetch(`${this.baseUrl}/api/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(`登出失败: ${data.error || response.statusText}`)
      }

      if (!data.success) {
        throw new Error('登出响应格式错误')
      }

      return { message: data.message, user: data.user }
    })
  }

  /**
   * 测试邮件同步状态
   */
  async testEmailSyncStatus(token?: string): Promise<TestResult> {
    return this.runTest('邮件同步状态测试', async () => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`${this.baseUrl}/api/email-sync/schedule`, {
        method: 'GET',
        headers
      })

      const data = await response.json()
      
      if (token) {
        // 有token时应该成功
        if (!response.ok) {
          throw new Error(`有token时邮件同步状态获取失败: ${data.error || response.statusText}`)
        }
        return { status: 'authorized', syncStatus: data.data }
      } else {
        // 无token时应该失败
        if (response.ok) {
          throw new Error('无token时邮件同步状态获取应该失败')
        }
        if (response.status !== 401) {
          throw new Error(`期望状态码401，实际${response.status}`)
        }
        return { status: 'unauthorized', error: data.error }
      }
    })
  }

  /**
   * 运行完整的测试套件
   */
  async runFullTestSuite(): Promise<TestSuite> {
    const startTime = Date.now()
    const tests: TestResult[] = []

    console.log('🧪 开始身份验证系统测试...')

    // 1. 测试无效登录
    console.log('1. 测试无效登录...')
    tests.push(await this.testInvalidLogin())

    // 2. 测试未授权API访问
    console.log('2. 测试未授权API访问...')
    tests.push(await this.testProtectedApiAccess())
    tests.push(await this.testEmailSyncStatus())

    // 3. 测试有效登录
    console.log('3. 测试有效登录...')
    const loginResult = await this.testValidLogin()
    tests.push(loginResult)

    let token: string | undefined
    if (loginResult.success && loginResult.details?.token) {
      token = loginResult.details.token
    }

    if (token) {
      // 4. 测试授权状态
      console.log('4. 测试授权状态...')
      tests.push(await this.testAuthStatus(token))

      // 5. 测试授权API访问
      console.log('5. 测试授权API访问...')
      tests.push(await this.testProtectedApiAccess(token))
      tests.push(await this.testEmailSyncStatus(token))

      // 6. 测试登出
      console.log('6. 测试登出...')
      tests.push(await this.testLogout(token))

      // 7. 测试登出后的状态
      console.log('7. 测试登出后状态...')
      tests.push(await this.testProtectedApiAccess())
    }

    const duration = Date.now() - startTime
    const passedTests = tests.filter(t => t.success).length
    const failedTests = tests.filter(t => !t.success).length

    return {
      name: '身份验证系统测试套件',
      tests,
      totalTests: tests.length,
      passedTests,
      failedTests,
      duration
    }
  }

  /**
   * 格式化测试结果
   */
  formatTestResults(suite: TestSuite): string {
    const lines: string[] = []
    
    lines.push(`\n📊 ${suite.name}`)
    lines.push(`⏱️  总耗时: ${suite.duration}ms`)
    lines.push(`📈 测试结果: ${suite.passedTests}/${suite.totalTests} 通过`)
    
    if (suite.failedTests > 0) {
      lines.push(`❌ 失败: ${suite.failedTests}`)
    }
    
    lines.push('\n📋 详细结果:')
    
    suite.tests.forEach((test, index) => {
      const status = test.success ? '✅' : '❌'
      lines.push(`${index + 1}. ${status} ${test.name} (${test.duration}ms)`)
      
      if (!test.success) {
        lines.push(`   错误: ${test.message}`)
      }
    })
    
    return lines.join('\n')
  }
}
