"use client"

import { useAuthContext } from '@/contexts/auth-context'
import { LoginCredentials, AuthResponse } from '@/lib/auth'

/**
 * 身份验证Hook
 * 提供便捷的身份验证功能访问
 */
export function useAuth() {
  const {
    isAuthenticated,
    user,
    loading,
    login,
    logout,
    validateSession,
    refreshAuth,
  } = useAuthContext()

  /**
   * 检查用户是否有特定权限
   * 目前是简单的管理员权限检查，可以扩展
   */
  const hasPermission = (permission: string): boolean => {
    if (!isAuthenticated || !user) return false
    
    // 简单的权限检查逻辑
    // 可以根据需要扩展更复杂的权限系统
    switch (permission) {
      case 'admin':
        return user.username === 'admin'
      case 'email-sync':
        return isAuthenticated // 所有登录用户都可以使用邮件同步
      default:
        return isAuthenticated
    }
  }

  /**
   * 安全的登录函数，包含错误处理
   */
  const safeLogin = async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      return await login(credentials)
    } catch (error) {
      console.error('登录过程中发生错误:', error)
      return {
        success: false,
        error: '登录失败，请重试'
      }
    }
  }

  /**
   * 安全的登出函数，包含错误处理
   */
  const safeLogout = async (): Promise<void> => {
    try {
      await logout()
    } catch (error) {
      console.error('登出过程中发生错误:', error)
      // 即使出错也要执行登出，确保本地状态清除
    }
  }

  /**
   * 检查是否需要重新登录
   */
  const requiresReauth = (): boolean => {
    return !isAuthenticated && !loading
  }

  /**
   * 获取用户显示名称
   */
  const getUserDisplayName = (): string => {
    if (!user) return '未登录'
    return user.username || '用户'
  }

  /**
   * 获取登录时长（毫秒）
   */
  const getLoginDuration = (): number => {
    if (!user || !user.loginTime) return 0
    return Date.now() - user.loginTime
  }

  /**
   * 格式化登录时长
   */
  const getFormattedLoginDuration = (): string => {
    const duration = getLoginDuration()
    const hours = Math.floor(duration / (1000 * 60 * 60))
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  }

  /**
   * 检查会话是否即将过期（1小时内）
   */
  const isSessionExpiringSoon = (): boolean => {
    if (!user || !user.loginTime) return false
    
    const duration = getLoginDuration()
    const sessionDuration = 24 * 60 * 60 * 1000 // 24小时
    const remainingTime = sessionDuration - duration
    
    return remainingTime < (60 * 60 * 1000) // 小于1小时
  }

  return {
    // 基础状态
    isAuthenticated,
    user,
    loading,
    
    // 基础操作
    login: safeLogin,
    logout: safeLogout,
    validateSession,
    refreshAuth,
    
    // 权限检查
    hasPermission,
    
    // 状态检查
    requiresReauth,
    isSessionExpiringSoon,
    
    // 用户信息
    getUserDisplayName,
    getLoginDuration,
    getFormattedLoginDuration,
  }
}

/**
 * 身份验证状态Hook（只读）
 * 用于只需要读取认证状态的组件
 */
export function useAuthState() {
  const { isAuthenticated, user, loading } = useAuthContext()
  
  return {
    isAuthenticated,
    user,
    loading,
  }
}

/**
 * 权限检查Hook
 * 用于需要权限控制的组件
 */
export function usePermissions() {
  const { hasPermission } = useAuth()
  
  return {
    hasPermission,
    isAdmin: hasPermission('admin'),
    canUseEmailSync: hasPermission('email-sync'),
  }
}
