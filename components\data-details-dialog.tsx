"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { SavedPersonData } from "@/lib/supabase"
import { Calendar, User, Mail, MapPin, Briefcase, CreditCard, GraduationCap, School, Database } from "lucide-react"
import PersonEmailsDialog from "./person-emails-dialog"

interface DataDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: SavedPersonData | null
}

export default function DataDetailsDialog({ open, onOpenChange, data }: DataDetailsDialogProps) {
  const [emailsDialogOpen, setEmailsDialogOpen] = useState(false)

  if (!data) return null

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const renderSection = (title: string, icon: React.ReactNode, fields: Array<{ label: string; value: string | number | null | undefined }>) => {
    const validFields = fields.filter(field => field.value)
    if (validFields.length === 0) return null

    return (
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
            {icon}
            <span className="ml-2">{title}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {validFields.map((field, index) => (
            <div key={index} className="flex justify-between items-center py-1">
              <span className="text-sm text-gray-600">{field.label}:</span>
              <span className="text-sm font-medium text-gray-900">{field.value}</span>
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-900 flex items-center justify-between">
            <span>{data.full_name} 的详细信息</span>
            {data.email && (
              <Button
                onClick={() => setEmailsDialogOpen(true)}
                variant="outline"
                size="sm"
                className="border-blue-300 text-blue-600 hover:bg-blue-50"
              >
                <Database className="mr-2 h-4 w-4" />
                查看邮件记录
              </Button>
            )}
          </DialogTitle>
          <div className="flex items-center text-sm text-gray-500 mt-2">
            <Calendar className="mr-2 h-4 w-4" />
            创建时间: {formatDate(data.created_at)}
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {/* 基本信息 */}
          {renderSection("基本信息", <User className="h-5 w-5" />, [
            { label: "全名", value: data.full_name },
            { label: "名", value: data.first_name },
            { label: "姓", value: data.last_name },
            { label: "性别", value: data.gender },
            { label: "生日", value: data.birthday },
            { label: "称谓", value: data.title },
            { label: "发色", value: data.hair_color },
            { label: "国家", value: data.country },
          ])}

          {/* 联系信息 */}
          {renderSection("联系信息", <Mail className="h-5 w-5" />, [
            { label: "邮箱", value: data.email },
            { label: "电话", value: data.phone },
            { label: "街道", value: data.street },
            { label: "城市", value: data.city },
            { label: "州", value: data.state },
            { label: "州全名", value: data.state_full_name },
            { label: "邮编", value: data.zip_code },
            { label: "完整地址", value: data.full_address },
          ])}

          {/* 工作信息 */}
          {renderSection("工作信息", <Briefcase className="h-5 w-5" />, [
            { label: "职业", value: data.occupation },
            { label: "公司", value: data.company },
            { label: "公司规模", value: data.company_size },
            { label: "行业", value: data.industry },
            { label: "工作状态", value: data.status },
            { label: "薪资", value: data.salary },
          ])}

          {/* 金融信息 */}
          {renderSection("金融信息", <CreditCard className="h-5 w-5" />, [
            { label: "社会安全号", value: data.ssn },
            { label: "信用卡类型", value: data.card_type },
            { label: "信用卡号", value: data.card_number },
            { label: "CVV", value: data.cvv },
            { label: "到期日期", value: data.expiry },
          ])}

          {/* 高中信息 */}
          {data.school_name && renderSection("高中信息", <School className="h-5 w-5" />, [
            { label: "学校名称", value: data.school_name },
            { label: "学校ID", value: data.school_id },
            { label: "学校邮编", value: data.school_zip },
            { label: "学校网站", value: data.school_website },
            { label: "学校地址", value: data.school_address },
            { label: "学校城市", value: data.school_city },
            { label: "学校州", value: data.school_state },
            { label: "学校电话", value: data.school_phone },
            { label: "年级范围", value: data.school_grades },
          ])}

          {/* 大学信息 */}
          {data.university_name && renderSection("大学信息", <GraduationCap className="h-5 w-5" />, [
            { label: "大学名称", value: data.university_name },
            { label: "大学ID", value: data.university_id },
            { label: "大学邮编", value: data.university_zip },
            { label: "大学网站", value: data.university_website },
            { label: "大学地址", value: data.university_address },
            { label: "大学城市", value: data.university_city },
            { label: "大学州", value: data.university_state },
            { label: "大学电话", value: data.university_phone },
            { label: "大学类型", value: data.university_type },
          ])}

          {/* 身体信息 */}
          {renderSection("身体信息", <User className="h-5 w-5" />, [
            { label: "身高", value: data.height },
            { label: "体重", value: data.weight },
            { label: "血型", value: data.blood_type },
          ])}

          {/* 账户信息 */}
          {renderSection("账户信息", <User className="h-5 w-5" />, [
            { label: "用户名", value: data.username },
            { label: "密码", value: data.password },
            { label: "安全问题", value: data.security_question },
            { label: "安全答案", value: data.security_answer },
          ])}

          {/* 技术信息 */}
          {renderSection("技术信息", <User className="h-5 w-5" />, [
            { label: "操作系统", value: data.os },
            { label: "GUID", value: data.guid },
            { label: "用户代理", value: data.user_agent },
          ])}

          {/* 其他信息 */}
          {renderSection("其他信息", <User className="h-5 w-5" />, [
            { label: "教育程度", value: data.education },
            { label: "个人网站", value: data.website },
          ])}
        </div>

        {/* 生成参数 */}
        <Card className="mt-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold text-gray-800">生成参数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">随机种子:</span>
                <Badge variant="outline">{data.generation_seed}</Badge>
              </div>
              {data.generation_params && (
                <div className="mt-2">
                  <span className="text-sm text-gray-600">生成配置:</span>
                  <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                    {JSON.stringify(JSON.parse(data.generation_params), null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </DialogContent>

      {/* 邮件记录对话框 */}
      <PersonEmailsDialog
        open={emailsDialogOpen}
        onOpenChange={setEmailsDialogOpen}
        email={data.email}
        personName={data.full_name}
      />
    </Dialog>
  )
}
