import type { Metadata } from 'next'
import './globals.css'
import { AuthProvider } from '@/contexts/auth-context'
import { Navigation } from '@/components/navigation'
import { LayoutWrapper } from '@/components/layout-wrapper'

export const metadata: Metadata = {
  title: '美国地址生成器',
  description: '专业的美国地址生成工具，支持邮件管理和数据导出',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN">
      <body>
        <AuthProvider>
          <LayoutWrapper>
            {children}
          </LayoutWrapper>
        </AuthProvider>
      </body>
    </html>
  )
}
