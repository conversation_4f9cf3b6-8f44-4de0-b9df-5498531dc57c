"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { 
  Archive, 
  Download, 
  Upload, 
  RefreshCw, 
  AlertTriangle,
  FileText,
  Calendar,
  HardDrive
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"

interface BackupRestoreProps {
  onRestoreComplete: () => void
  disabled?: boolean
}

export function BackupRestore({ onRestoreComplete, disabled = false }: BackupRestoreProps) {
  const [open, setOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [restoreFile, setRestoreFile] = useState<File | null>(null)
  const [restoreMode, setRestoreMode] = useState<"replace" | "merge">("merge")
  const { toast } = useToast()

  // 创建备份
  const createBackup = async () => {
    setIsProcessing(true)
    try {
      const response = await fetch('/api/backup', {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('备份创建失败')
      }

      // 下载备份文件
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      
      const contentDisposition = response.headers.get('Content-Disposition')
      const filename = contentDisposition 
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '') 
        : 'backup.json'
      
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      toast({
        title: "备份创建成功",
        description: `备份文件已下载：${filename}`,
      })
    } catch (error) {
      toast({
        title: "备份创建失败",
        description: error instanceof Error ? error.message : "创建备份时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // 恢复数据
  const restoreData = async () => {
    if (!restoreFile) {
      toast({
        title: "恢复失败",
        description: "请选择备份文件",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    try {
      const text = await restoreFile.text()
      const backupData = JSON.parse(text)
      
      // 验证备份文件格式
      if (!backupData.data || !backupData.data.emails || !backupData.data.messages || !backupData.data.generatedData) {
        throw new Error('备份文件格式无效')
      }

      const response = await fetch('/api/restore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          backupData,
          mode: restoreMode
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "数据恢复成功",
          description: result.message,
        })
        setOpen(false)
        setRestoreFile(null)
        onRestoreComplete()
      } else {
        toast({
          title: "数据恢复失败",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "数据恢复失败",
        description: error instanceof Error ? error.message : "恢复数据时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          className="bg-purple-50 hover:bg-purple-100 border-purple-200"
        >
          <Archive className="h-4 w-4 mr-2" />
          备份恢复
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Archive className="h-5 w-5 text-purple-600" />
            数据备份与恢复
          </DialogTitle>
          <DialogDescription>
            创建数据备份或从备份文件恢复数据。
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="backup" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="backup">创建备份</TabsTrigger>
            <TabsTrigger value="restore">恢复数据</TabsTrigger>
          </TabsList>
          
          <TabsContent value="backup" className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <HardDrive className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 mb-2">创建完整备份</h4>
                  <p className="text-sm text-blue-700 mb-3">
                    将创建包含所有邮箱、消息和生成数据的完整备份文件。
                  </p>
                  <ul className="text-xs text-blue-600 space-y-1">
                    <li>• 包含所有邮箱账户和配置</li>
                    <li>• 包含所有接收的邮件消息</li>
                    <li>• 包含所有生成的个人数据</li>
                    <li>• 自动下载JSON格式备份文件</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <Button
              onClick={createBackup}
              disabled={isProcessing}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  创建备份中...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  创建并下载备份
                </>
              )}
            </Button>
          </TabsContent>
          
          <TabsContent value="restore" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="restoreFile">选择备份文件</Label>
                <Input
                  id="restoreFile"
                  type="file"
                  accept=".json"
                  onChange={(e) => setRestoreFile(e.target.files?.[0] || null)}
                />
                <p className="text-xs text-gray-500 mt-1">
                  请选择之前创建的JSON格式备份文件
                </p>
              </div>
              
              <div>
                <Label htmlFor="restoreMode">恢复模式</Label>
                <Select value={restoreMode} onValueChange={(value: "replace" | "merge") => setRestoreMode(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="merge">合并模式（推荐）</SelectItem>
                    <SelectItem value="replace">替换模式</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  {restoreMode === "merge" 
                    ? "将备份数据与现有数据合并，避免重复" 
                    : "完全替换现有数据（谨慎使用）"
                  }
                </p>
              </div>
            </div>
            
            {restoreMode === "replace" && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
                  <div className="text-sm text-red-800">
                    <p className="font-medium mb-1">警告：替换模式</p>
                    <p className="text-xs">
                      此模式将完全删除现有数据并替换为备份数据。此操作不可撤销！
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            <Button
              onClick={restoreData}
              disabled={isProcessing || !restoreFile}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  恢复数据中...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  开始恢复数据
                </>
              )}
            </Button>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
