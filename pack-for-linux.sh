#!/bin/bash

# 美国地址生成器 - Linux部署包打包工具
# 创建完整的Linux部署包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置
PACKAGE_NAME="us-fake-gen-ui-linux-deploy"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
FINAL_NAME="${PACKAGE_NAME}_${TIMESTAMP}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=========================================="
    echo "  美国地址生成器 - Linux打包工具"
    echo "  创建完整的Linux部署包"
    echo "=========================================="
    echo -e "${NC}"
    echo
    echo "📦 包名: $FINAL_NAME"
    echo "📅 时间: $(date)"
    echo
}

# 检查项目根目录
check_project_root() {
    log_step "检查项目根目录..."
    
    if [ ! -f "package.json" ]; then
        log_error "请在项目根目录运行此脚本"
        log_error "当前目录应包含 package.json 文件"
        exit 1
    fi
    
    log_success "项目根目录检查通过"
}

# 创建打包目录
create_package_directory() {
    log_step "创建打包目录..."
    
    # 清理旧的打包目录
    if [ -d "$FINAL_NAME" ]; then
        log_info "清理旧的打包目录..."
        rm -rf "$FINAL_NAME"
    fi
    
    # 创建新目录
    mkdir -p "$FINAL_NAME"
    
    log_success "打包目录创建完成: $FINAL_NAME"
}

# 复制核心目录
copy_directories() {
    log_step "复制核心目录..."
    
    local directories=(
        "app"
        "components"
        "lib"
        "hooks"
        "contexts"
        "database"
        "scripts"
        "public"
        "styles"
    )
    
    for dir in "${directories[@]}"; do
        if [ -d "$dir" ]; then
            log_info "复制目录: $dir"
            cp -r "$dir" "$FINAL_NAME/"
        else
            log_warning "目录不存在: $dir"
        fi
    done
    
    log_success "核心目录复制完成"
}

# 复制配置文件
copy_config_files() {
    log_step "复制配置文件..."
    
    local config_files=(
        "package.json"
        "next.config.mjs"
        "tsconfig.json"
        "tailwind.config.ts"
        "postcss.config.mjs"
        "components.json"
        "middleware.ts"
        ".env.local.example"
        ".env.production"
    )
    
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "复制配置文件: $file"
            cp "$file" "$FINAL_NAME/"
        else
            log_warning "配置文件不存在: $file"
        fi
    done
    
    log_success "配置文件复制完成"
}

# 复制Docker文件
copy_docker_files() {
    log_step "复制Docker文件..."
    
    local docker_files=(
        "Dockerfile"
        "Dockerfile.linux"
        "docker-compose.yml"
        "docker-compose.linux.yml"
        "docker-compose.dev.yml"
        ".dockerignore"
        "nginx.conf"
    )
    
    for file in "${docker_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "复制Docker文件: $file"
            cp "$file" "$FINAL_NAME/"
        else
            log_warning "Docker文件不存在: $file"
        fi
    done
    
    log_success "Docker文件复制完成"
}

# 复制部署脚本
copy_deployment_scripts() {
    log_step "复制部署脚本..."
    
    local script_files=(
        "deploy-linux.sh"
        "pack-for-linux.sh"
        "deploy-docker.bat"
        "check-docker-env.bat"
    )
    
    for file in "${script_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "复制脚本: $file"
            cp "$file" "$FINAL_NAME/"
            # 确保脚本有执行权限
            chmod +x "$FINAL_NAME/$file" 2>/dev/null || true
        else
            log_warning "脚本文件不存在: $file"
        fi
    done
    
    log_success "部署脚本复制完成"
}

# 复制文档文件
copy_documentation() {
    log_step "复制文档文件..."
    
    local doc_files=(
        "README.md"
        "SETUP.md"
        "DOCKER_DEPLOYMENT.md"
        "DOCKER_QUICKSTART.md"
        "PACKAGING_CHECKLIST.md"
        "MANUAL_PACKAGING_GUIDE.md"
    )
    
    for file in "${doc_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "复制文档: $file"
            cp "$file" "$FINAL_NAME/"
        else
            log_warning "文档文件不存在: $file"
        fi
    done
    
    log_success "文档文件复制完成"
}

# 创建Linux部署说明
create_linux_deployment_guide() {
    log_step "创建Linux部署说明..."
    
    cat > "$FINAL_NAME/LINUX_DEPLOYMENT.md" << 'EOF'
# Linux部署指南

## 🚀 快速部署

### 自动部署（推荐）
```bash
# 1. 解压部署包
tar -xzf us-fake-gen-ui-linux-deploy_*.tar.gz
cd us-fake-gen-ui-linux-deploy_*

# 2. 运行自动部署脚本
chmod +x deploy-linux.sh
./deploy-linux.sh
```

### 手动部署
```bash
# 1. 安装Docker
curl -fsSL https://get.docker.com | sh
sudo systemctl start docker
sudo systemctl enable docker

# 2. 配置环境变量
cp .env.local.example .env.production
# 编辑 .env.production 文件

# 3. 构建和启动
docker compose -f docker-compose.linux.yml up --build -d

# 4. 访问应用
# http://localhost:3000
```

## 📋 系统要求

- **操作系统**: Ubuntu 18.04+, Debian 10+, CentOS 7+, RHEL 7+
- **内存**: 最少2GB，推荐4GB+
- **磁盘**: 最少5GB可用空间
- **网络**: 需要访问互联网下载依赖

## 🔧 配置说明

### 必需环境变量
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
ADMIN_USERNAME=your_admin_username
ADMIN_PASSWORD=your_secure_password
JWT_SECRET=your_jwt_secret_32_chars_min
```

### 可选配置
```env
# 邮件系统
IMAP_SERVER=imap.qq.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your_email_password

# 应用配置
APP_NAME=美国地址生成器
APP_VERSION=1.0.0
```

## 🛠️ 管理命令

```bash
# 查看服务状态
docker compose -f docker-compose.linux.yml ps

# 查看日志
docker compose -f docker-compose.linux.yml logs -f

# 重启服务
docker compose -f docker-compose.linux.yml restart

# 停止服务
docker compose -f docker-compose.linux.yml down

# 更新应用
docker compose -f docker-compose.linux.yml down
docker compose -f docker-compose.linux.yml build --no-cache
docker compose -f docker-compose.linux.yml up -d
```

## 🔒 安全建议

1. **防火墙配置**
   ```bash
   # Ubuntu/Debian
   sudo ufw allow 3000/tcp
   sudo ufw enable
   
   # CentOS/RHEL
   sudo firewall-cmd --permanent --add-port=3000/tcp
   sudo firewall-cmd --reload
   ```

2. **SSL证书**
   - 生产环境建议使用HTTPS
   - 可以使用Let's Encrypt免费证书

3. **定期备份**
   - 备份.env.production文件
   - 备份数据库数据
   - 备份日志文件

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tulpn | grep :3000
   sudo lsof -i :3000
   ```

2. **Docker权限问题**
   ```bash
   sudo usermod -aG docker $USER
   # 重新登录或运行: newgrp docker
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   # 检查Docker资源使用
   docker stats
   ```

4. **查看详细错误**
   ```bash
   docker compose -f docker-compose.linux.yml logs us-fake-gen-app
   ```

## 📞 技术支持

如果遇到问题，请检查：
1. 系统要求是否满足
2. 环境变量是否正确配置
3. 网络连接是否正常
4. Docker服务是否正常运行

---
*此部署包由自动化脚本生成，包含完整的Linux部署配置*
EOF
    
    log_success "Linux部署说明创建完成"
}

# 创建快速启动脚本
create_quick_start_script() {
    log_step "创建快速启动脚本..."
    
    cat > "$FINAL_NAME/quick-start.sh" << 'EOF'
#!/bin/bash

# 美国地址生成器 - 快速启动脚本

echo "🚀 美国地址生成器 - 快速启动"
echo "================================"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，正在安装..."
    curl -fsSL https://get.docker.com | sh
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -aG docker $USER
    echo "✅ Docker安装完成，请重新登录后再次运行此脚本"
    exit 0
fi

# 检查环境变量文件
if [ ! -f ".env.production" ]; then
    if [ -f ".env.local.example" ]; then
        cp .env.local.example .env.production
        echo "⚠️  已创建.env.production文件，请编辑配置后重新运行"
        echo "📝 必需配置项："
        echo "   - NEXT_PUBLIC_SUPABASE_URL"
        echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY"
        echo "   - ADMIN_USERNAME"
        echo "   - ADMIN_PASSWORD"
        echo "   - JWT_SECRET"
        exit 1
    fi
fi

# 启动服务
echo "🔧 启动服务..."
docker compose -f docker-compose.linux.yml up -d

echo "✅ 启动完成！"
echo "🌐 访问地址: http://localhost:3000"
echo "📊 查看状态: docker compose -f docker-compose.linux.yml ps"
echo "📋 查看日志: docker compose -f docker-compose.linux.yml logs -f"
EOF
    
    chmod +x "$FINAL_NAME/quick-start.sh"
    
    log_success "快速启动脚本创建完成"
}

# 统计打包信息
show_package_stats() {
    log_step "统计打包信息..."
    
    local file_count=$(find "$FINAL_NAME" -type f | wc -l)
    local dir_count=$(find "$FINAL_NAME" -type d | wc -l)
    local total_size=$(du -sh "$FINAL_NAME" | cut -f1)
    
    echo
    echo -e "${CYAN}📊 打包统计信息:${NC}"
    echo "   文件数量: $file_count"
    echo "   目录数量: $dir_count"
    echo "   总大小: $total_size"
    echo
}

# 创建压缩包
create_archive() {
    log_step "创建压缩包..."
    
    local archive_name="${FINAL_NAME}.tar.gz"
    
    log_info "压缩中..."
    tar -czf "$archive_name" "$FINAL_NAME"
    
    local archive_size=$(du -sh "$archive_name" | cut -f1)
    
    log_success "压缩包创建完成: $archive_name ($archive_size)"
}

# 显示完成信息
show_completion_info() {
    echo
    echo -e "${GREEN}=========================================="
    echo "🎉 Linux部署包打包完成！"
    echo "==========================================${NC}"
    echo
    echo -e "${CYAN}📦 部署包信息:${NC}"
    echo "   文件夹: $FINAL_NAME/"
    echo "   压缩包: ${FINAL_NAME}.tar.gz"
    echo
    echo -e "${CYAN}🚀 使用方法:${NC}"
    echo "   1. 将压缩包传输到Linux服务器"
    echo "   2. 解压: tar -xzf ${FINAL_NAME}.tar.gz"
    echo "   3. 进入目录: cd $FINAL_NAME"
    echo "   4. 运行部署: ./deploy-linux.sh"
    echo "   5. 或快速启动: ./quick-start.sh"
    echo
    echo -e "${CYAN}📚 相关文档:${NC}"
    echo "   - LINUX_DEPLOYMENT.md - Linux部署指南"
    echo "   - README.md - 项目说明"
    echo "   - SETUP.md - 设置指南"
    echo
}

# 主函数
main() {
    show_banner
    check_project_root
    create_package_directory
    copy_directories
    copy_config_files
    copy_docker_files
    copy_deployment_scripts
    copy_documentation
    create_linux_deployment_guide
    create_quick_start_script
    show_package_stats
    create_archive
    show_completion_info
    
    log_success "Linux打包完成！"
}

# 运行主函数
main "$@"
