import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

/**
 * 获取邮箱列表
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search') || ''

    const offset = (page - 1) * limit

    // 获取邮箱列表
    let query = supabase
      .from('emails')
      .select('*', { count: 'exact' })

    // 搜索功能
    if (search) {
      query = query.ilike('address', `%${search}%`)
    }

    // 分页和排序
    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('获取邮箱列表失败:', error)
      return NextResponse.json({
        success: false,
        error: '获取邮箱列表失败: ' + error.message
      }, { status: 500 })
    }

    // 转换数据格式以匹配前端期望的格式
    const emails = (data || []).map(email => ({
      id: email.id,
      address: email.address,
      password: email.password,
      type: email.type,
      config: email.config,
      createdAt: email.created_at_timestamp || Date.now(),
      isActive: email.is_active,
      unreadCount: email.unread_count || 0,
      hasNewMessages: email.has_new_messages || false
    }))

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      success: true,
      data: {
        emails,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '服务器错误'
    }, { status: 500 })
  }
}

/**
 * 创建新邮箱
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { address, password, type = 'tempmail', config = {} } = body

    if (!address) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址不能为空'
      }, { status: 400 })
    }

    // 检查邮箱是否已存在
    const { data: existingEmail } = await supabase
      .from('emails')
      .select('id')
      .eq('address', address)
      .single()

    if (existingEmail) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址已存在'
      }, { status: 400 })
    }

    // 创建新邮箱
    const { data, error } = await supabase
      .from('emails')
      .insert({
        address,
        password: password || 'tempmail',
        type,
        config: typeof config === 'string' ? JSON.parse(config) : config,
        is_active: true,
        created_at_timestamp: Date.now()
      })
      .select()
      .single()

    if (error) {
      console.error('创建邮箱失败:', error)
      return NextResponse.json({
        success: false,
        error: '创建邮箱失败: ' + error.message
      }, { status: 500 })
    }

    // 转换数据格式
    const email = {
      id: data.id,
      address: data.address,
      password: data.password,
      type: data.type,
      config: data.config,
      createdAt: data.created_at_timestamp,
      isActive: data.is_active,
      unreadCount: 0,
      hasNewMessages: false
    }

    return NextResponse.json({
      success: true,
      data: email,
      message: '邮箱创建成功'
    })

  } catch (error) {
    console.error('创建邮箱失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '创建邮箱失败'
    }, { status: 500 })
  }
}
