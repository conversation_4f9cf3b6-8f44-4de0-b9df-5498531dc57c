import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

/**
 * 获取系统统计信息
 */
export async function GET() {
  try {
    // 获取已保存数据统计
    const { count: totalCount, error: countError } = await supabase
      .from('saved_person_data')
      .select('*', { count: 'exact', head: true })

    if (countError) {
      throw new Error('获取数据统计失败: ' + countError.message)
    }

    // 获取最近7天的数据
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const { count: recentCount, error: recentError } = await supabase
      .from('saved_person_data')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', sevenDaysAgo.toISOString())

    if (recentError) {
      console.warn('获取最近数据统计失败:', recentError.message)
    }

    // 获取性别分布
    const { data: genderData, error: genderError } = await supabase
      .from('saved_person_data')
      .select('gender')

    let genderStats = { male: 0, female: 0, other: 0 }
    if (!genderError && genderData) {
      genderStats = genderData.reduce((acc, item) => {
        const gender = item.gender?.toLowerCase()
        if (gender === '男' || gender === 'male' || gender === 'm') {
          acc.male++
        } else if (gender === '女' || gender === 'female' || gender === 'f') {
          acc.female++
        } else {
          acc.other++
        }
        return acc
      }, { male: 0, female: 0, other: 0 })
    }

    const stats = {
      savedData: {
        total: totalCount || 0,
        recent: recentCount || 0,
        genderDistribution: genderStats
      },
      database: {
        type: 'Supabase (PostgreSQL)',
        status: 'connected',
        lastUpdated: new Date().toISOString()
      }
    }

    return NextResponse.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('获取统计信息失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取统计信息时发生未知错误'
    }, { status: 500 })
  }
}
