/**
 * 服务端邮箱生成器
 * 用于API路由中生成邮箱地址
 */

interface GeneratedEmail {
  email: string
  password: string
  firstName: string
  lastName: string
  timestamp: number
}

export class ServerEmailGenerator {
  private domain: string
  private namesList: string[]

  constructor() {
    // 使用自定义域名（来自project2配置）
    this.domain = process.env.NEXT_PUBLIC_TEMP_MAIL_DOMAIN || this.getRandomDomain()
    this.namesList = this.getDefaultNames()
  }

  /**
   * 生成随机邮箱地址
   */
  generateEmail(length: number = 4): GeneratedEmail {
    const firstName = this.generateRandomName()
    const lastName = this.generateRandomName()
    
    // 使用时间戳的后几位作为唯一标识
    const timestamp = Date.now()
    const timestampSuffix = timestamp.toString().slice(-length)
    
    // 生成邮箱地址
    const email = `${firstName.toLowerCase()}${timestampSuffix}@${this.domain}`
    
    return {
      email,
      password: this.generateRandomPassword(),
      firstName,
      lastName,
      timestamp
    }
  }

  /**
   * 批量生成邮箱地址
   */
  generateBatchEmails(count: number): GeneratedEmail[] {
    const emails: GeneratedEmail[] = []
    
    for (let i = 0; i < count; i++) {
      // 为每个邮箱生成不同的时间戳，避免重复
      const baseTimestamp = Date.now()
      const uniqueTimestamp = baseTimestamp + i
      
      const firstName = this.generateRandomName()
      const lastName = this.generateRandomName()
      const timestampSuffix = uniqueTimestamp.toString().slice(-4)
      const email = `${firstName.toLowerCase()}${timestampSuffix}@${this.domain}`
      
      emails.push({
        email,
        password: this.generateRandomPassword(),
        firstName,
        lastName,
        timestamp: uniqueTimestamp
      })
    }
    
    return emails
  }

  /**
   * 生成随机名字
   */
  private generateRandomName(): string {
    return this.namesList[Math.floor(Math.random() * this.namesList.length)]
  }

  /**
   * 生成随机密码
   */
  private generateRandomPassword(length: number = 12): string {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
    let password = ''
    
    for (let i = 0; i < length; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    
    return password
  }

  /**
   * 获取随机域名
   */
  private getRandomDomain(): string {
    const domains = [
      'tempmail.plus',
      '10minutemail.com',
      'guerrillamail.com',
      'mailinator.com',
      'temp-mail.org'
    ]
    
    return domains[Math.floor(Math.random() * domains.length)]
  }

  /**
   * 获取默认名字列表
   */
  private getDefaultNames(): string[] {
    return [
      // 男性名字
      'John', 'James', 'Robert', 'Michael', 'William', 'David', 'Richard', 'Joseph',
      'Thomas', 'Christopher', 'Charles', 'Daniel', 'Matthew', 'Anthony', 'Mark', 'Donald',
      'Steven', 'Paul', 'Andrew', 'Joshua', 'Kenneth', 'Kevin', 'Brian', 'George',
      'Timothy', 'Ronald', 'Jason', 'Edward', 'Jeffrey', 'Ryan', 'Jacob', 'Gary',
      
      // 女性名字
      'Mary', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth', 'Barbara', 'Susan', 'Jessica',
      'Sarah', 'Karen', 'Nancy', 'Lisa', 'Betty', 'Helen', 'Sandra', 'Donna',
      'Carol', 'Ruth', 'Sharon', 'Michelle', 'Laura', 'Sarah', 'Kimberly', 'Deborah',
      'Dorothy', 'Lisa', 'Nancy', 'Karen', 'Betty', 'Helen', 'Sandra', 'Donna',
      
      // 现代流行名字
      'Emma', 'Olivia', 'Ava', 'Isabella', 'Sophia', 'Charlotte', 'Mia', 'Amelia',
      'Harper', 'Evelyn', 'Abigail', 'Emily', 'Elizabeth', 'Mila', 'Ella', 'Avery',
      'Liam', 'Noah', 'Oliver', 'Elijah', 'William', 'James', 'Benjamin', 'Lucas',
      'Henry', 'Alexander', 'Mason', 'Michael', 'Ethan', 'Daniel', 'Jacob', 'Logan',
      
      // 姓氏
      'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
      'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas',
      'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White',
      'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young'
    ]
  }
}

// 导出类型
export type { GeneratedEmail }
