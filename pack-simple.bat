@echo off
echo.
echo ==========================================
echo Docker Deployment Package Builder
echo ==========================================
echo.

REM Check if package.json exists
if not exist "package.json" (
    echo [ERROR] Please run this script from project root directory
    echo [ERROR] Current directory should contain package.json file
    pause
    exit /b 1
)

REM Set package name
set PACKAGE_DIR=us-fake-gen-ui-deploy

echo [INFO] Creating package directory: %PACKAGE_DIR%

REM Clean old directory
if exist %PACKAGE_DIR% (
    echo [INFO] Removing old package directory...
    rmdir /s /q %PACKAGE_DIR%
)

REM Create new directory
mkdir %PACKAGE_DIR%

echo [INFO] Copying directories...

REM Copy directories one by one
if exist app (
    echo [OK] Copying app/
    xcopy app %PACKAGE_DIR%\app /e /i /h /y /q
) else (
    echo [WARN] app/ directory not found
)

if exist components (
    echo [OK] Copying components/
    xcopy components %PACKAGE_DIR%\components /e /i /h /y /q
) else (
    echo [WARN] components/ directory not found
)

if exist lib (
    echo [OK] Copying lib/
    xcopy lib %PACKAGE_DIR%\lib /e /i /h /y /q
) else (
    echo [WARN] lib/ directory not found
)

if exist hooks (
    echo [OK] Copying hooks/
    xcopy hooks %PACKAGE_DIR%\hooks /e /i /h /y /q
) else (
    echo [WARN] hooks/ directory not found
)

if exist contexts (
    echo [OK] Copying contexts/
    xcopy contexts %PACKAGE_DIR%\contexts /e /i /h /y /q
) else (
    echo [WARN] contexts/ directory not found
)

if exist database (
    echo [OK] Copying database/
    xcopy database %PACKAGE_DIR%\database /e /i /h /y /q
) else (
    echo [WARN] database/ directory not found
)

if exist scripts (
    echo [OK] Copying scripts/
    xcopy scripts %PACKAGE_DIR%\scripts /e /i /h /y /q
) else (
    echo [WARN] scripts/ directory not found
)

if exist public (
    echo [OK] Copying public/
    xcopy public %PACKAGE_DIR%\public /e /i /h /y /q
) else (
    echo [WARN] public/ directory not found
)

if exist styles (
    echo [OK] Copying styles/
    xcopy styles %PACKAGE_DIR%\styles /e /i /h /y /q
) else (
    echo [WARN] styles/ directory not found
)

echo.
echo [INFO] Copying configuration files...

REM Copy files one by one
if exist Dockerfile (
    echo [OK] Copying Dockerfile
    copy Dockerfile %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] Dockerfile not found
)

if exist docker-compose.yml (
    echo [OK] Copying docker-compose.yml
    copy docker-compose.yml %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] docker-compose.yml not found
)

if exist docker-compose.dev.yml (
    echo [OK] Copying docker-compose.dev.yml
    copy docker-compose.dev.yml %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] docker-compose.dev.yml not found
)

if exist nginx.conf (
    echo [OK] Copying nginx.conf
    copy nginx.conf %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] nginx.conf not found
)

if exist .dockerignore (
    echo [OK] Copying .dockerignore
    copy .dockerignore %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] .dockerignore not found
)

if exist .env.local.example (
    echo [OK] Copying .env.local.example
    copy .env.local.example %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] .env.local.example not found
)

if exist .env.production (
    echo [OK] Copying .env.production
    copy .env.production %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] .env.production not found
)

if exist package.json (
    echo [OK] Copying package.json
    copy package.json %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] package.json not found
)

if exist pnpm-lock.yaml (
    echo [OK] Copying pnpm-lock.yaml
    copy pnpm-lock.yaml %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] pnpm-lock.yaml not found
)

if exist next.config.mjs (
    echo [OK] Copying next.config.mjs
    copy next.config.mjs %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] next.config.mjs not found
)

if exist tsconfig.json (
    echo [OK] Copying tsconfig.json
    copy tsconfig.json %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] tsconfig.json not found
)

if exist tailwind.config.ts (
    echo [OK] Copying tailwind.config.ts
    copy tailwind.config.ts %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] tailwind.config.ts not found
)

if exist postcss.config.mjs (
    echo [OK] Copying postcss.config.mjs
    copy postcss.config.mjs %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] postcss.config.mjs not found
)

if exist components.json (
    echo [OK] Copying components.json
    copy components.json %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] components.json not found
)

if exist middleware.ts (
    echo [OK] Copying middleware.ts
    copy middleware.ts %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] middleware.ts not found
)

echo.
echo [INFO] Copying documentation files...

if exist README.md (
    echo [OK] Copying README.md
    copy README.md %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] README.md not found
)

if exist DOCKER_DEPLOYMENT.md (
    echo [OK] Copying DOCKER_DEPLOYMENT.md
    copy DOCKER_DEPLOYMENT.md %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] DOCKER_DEPLOYMENT.md not found
)

if exist DOCKER_QUICKSTART.md (
    echo [OK] Copying DOCKER_QUICKSTART.md
    copy DOCKER_QUICKSTART.md %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] DOCKER_QUICKSTART.md not found
)

if exist SETUP.md (
    echo [OK] Copying SETUP.md
    copy SETUP.md %PACKAGE_DIR%\ >nul
) else (
    echo [WARN] SETUP.md not found
)

echo.
echo [INFO] Creating deployment instructions...

REM Create simple deployment readme
echo # Docker Deployment Package > %PACKAGE_DIR%\DEPLOYMENT_README.md
echo. >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo Package created: %date% %time% >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo. >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo ## Quick Start >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo. >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo 1. Install Docker Desktop >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo 2. Copy .env.local.example to .env.local >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo 3. Edit .env.local with your configuration >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo 4. Run: scripts\deploy.bat dev >> %PACKAGE_DIR%\DEPLOYMENT_README.md
echo 5. Access: http://localhost:3000 >> %PACKAGE_DIR%\DEPLOYMENT_README.md

echo [OK] Created DEPLOYMENT_README.md

echo.
echo [INFO] Attempting to create ZIP file...

REM Try to create ZIP using PowerShell
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%\*' -DestinationPath '%PACKAGE_DIR%.zip' -Force" 2>nul
if %errorlevel% equ 0 (
    echo [OK] ZIP file created: %PACKAGE_DIR%.zip
) else (
    echo [WARN] Could not create ZIP file automatically
    echo [INFO] Please manually compress the %PACKAGE_DIR% folder
)

echo.
echo ==========================================
echo Package Creation Complete!
echo ==========================================
echo.
echo Package location: %PACKAGE_DIR%\
if exist %PACKAGE_DIR%.zip (
    echo ZIP file: %PACKAGE_DIR%.zip
)
echo.
echo Next steps:
echo 1. Transfer package to target server
echo 2. Extract and configure .env.local
echo 3. Run deployment script
echo 4. Access http://localhost:3000
echo.

pause
