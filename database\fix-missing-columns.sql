-- 修复 saved_person_data 表缺失字段的问题
-- 请在 Supabase SQL 编辑器中执行此脚本

-- 方案1: 添加缺失的字段（如果表已有数据，推荐使用此方案）
ALTER TABLE saved_person_data 
ADD COLUMN IF NOT EXISTS title TEXT,
ADD COLUMN IF NOT EXISTS hair_color TEXT,
ADD COLUMN IF NOT EXISTS country TEXT,
ADD COLUMN IF NOT EXISTS company_size TEXT,
ADD COLUMN IF NOT EXISTS industry TEXT,
ADD COLUMN IF NOT EXISTS status TEXT,
ADD COLUMN IF NOT EXISTS salary TEXT,
ADD COLUMN IF NOT EXISTS card_type TEXT,
ADD COLUMN IF NOT EXISTS card_number TEXT,
ADD COLUMN IF NOT EXISTS cvv INTEGER,
ADD COLUMN IF NOT EXISTS expiry TEXT,
ADD COLUMN IF NOT EXISTS username TEXT,
ADD COLUMN IF NOT EXISTS password TEXT,
ADD COLUMN IF NOT EXISTS security_question TEXT,
ADD COLUMN IF NOT EXISTS security_answer TEXT,
ADD COLUMN IF NOT EXISTS os TEXT,
ADD COLUMN IF NOT EXISTS guid TEXT,
ADD COLUMN IF NOT EXISTS user_agent TEXT,
ADD COLUMN IF NOT EXISTS website TEXT,
ADD COLUMN IF NOT EXISTS school_name TEXT,
ADD COLUMN IF NOT EXISTS school_id TEXT,
ADD COLUMN IF NOT EXISTS school_zip TEXT,
ADD COLUMN IF NOT EXISTS school_website TEXT,
ADD COLUMN IF NOT EXISTS school_address TEXT,
ADD COLUMN IF NOT EXISTS school_city TEXT,
ADD COLUMN IF NOT EXISTS school_state TEXT,
ADD COLUMN IF NOT EXISTS school_phone TEXT,
ADD COLUMN IF NOT EXISTS school_grades TEXT,
ADD COLUMN IF NOT EXISTS university_name TEXT,
ADD COLUMN IF NOT EXISTS university_id TEXT,
ADD COLUMN IF NOT EXISTS university_zip TEXT,
ADD COLUMN IF NOT EXISTS university_website TEXT,
ADD COLUMN IF NOT EXISTS university_address TEXT,
ADD COLUMN IF NOT EXISTS university_city TEXT,
ADD COLUMN IF NOT EXISTS university_state TEXT,
ADD COLUMN IF NOT EXISTS university_phone TEXT,
ADD COLUMN IF NOT EXISTS university_type TEXT;

-- 验证字段是否添加成功
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'saved_person_data' 
ORDER BY ordinal_position;
