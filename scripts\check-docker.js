#!/usr/bin/env node

/**
 * Docker 环境检查脚本
 * 检查 Docker 和 Docker Compose 是否正确安装
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkCommand(command, name) {
  try {
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log('green', `✅ ${name} 已安装: ${result.trim()}`);
    return true;
  } catch (error) {
    log('red', `❌ ${name} 未安装或不可用`);
    return false;
  }
}

function checkFile(filePath, name) {
  if (fs.existsSync(filePath)) {
    log('green', `✅ ${name} 存在`);
    return true;
  } else {
    log('red', `❌ ${name} 不存在: ${filePath}`);
    return false;
  }
}

function checkEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    log('yellow', `⚠️  环境变量文件不存在: ${filePath}`);
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'ADMIN_USERNAME',
    'ADMIN_PASSWORD',
    'JWT_SECRET'
  ];

  let allConfigured = true;
  for (const varName of requiredVars) {
    const regex = new RegExp(`^${varName}=(.+)$`, 'm');
    const match = content.match(regex);
    
    if (!match || match[1].includes('your_') || match[1].trim() === '') {
      log('red', `❌ ${varName} 未正确配置`);
      allConfigured = false;
    } else {
      log('green', `✅ ${varName} 已配置`);
    }
  }

  return allConfigured;
}

function main() {
  log('blue', '🔍 检查 Docker 部署环境...\n');

  let allChecksPass = true;

  // 检查 Docker
  log('blue', '1. 检查 Docker:');
  if (!checkCommand('docker --version', 'Docker')) {
    allChecksPass = false;
    log('yellow', '   请安装 Docker Desktop: https://www.docker.com/products/docker-desktop');
  }

  // 检查 Docker Compose
  log('blue', '\n2. 检查 Docker Compose:');
  const hasDockerCompose = checkCommand('docker-compose --version', 'Docker Compose (standalone)');
  const hasDockerComposePlugin = checkCommand('docker compose version', 'Docker Compose (plugin)');
  
  if (!hasDockerCompose && !hasDockerComposePlugin) {
    allChecksPass = false;
    log('yellow', '   请安装 Docker Compose');
  }

  // 检查 Docker 文件
  log('blue', '\n3. 检查 Docker 配置文件:');
  checkFile('Dockerfile', 'Dockerfile');
  checkFile('docker-compose.yml', 'docker-compose.yml');
  checkFile('.dockerignore', '.dockerignore');

  // 检查环境变量文件
  log('blue', '\n4. 检查环境变量配置:');
  const hasDevEnv = checkFile('.env.local', '开发环境变量文件');
  const hasProdEnv = checkFile('.env.production', '生产环境变量文件');

  if (hasDevEnv) {
    log('blue', '\n5. 验证开发环境变量:');
    if (!checkEnvFile('.env.local')) {
      allChecksPass = false;
    }
  }

  // 检查部署脚本
  log('blue', '\n6. 检查部署脚本:');
  checkFile('scripts/deploy.sh', 'Linux/macOS 部署脚本');
  checkFile('scripts/deploy.bat', 'Windows 部署脚本');

  // 总结
  console.log('\n' + '='.repeat(50));
  if (allChecksPass) {
    log('green', '🎉 所有检查通过！可以开始 Docker 部署');
    console.log('\n快速开始:');
    console.log('  开发环境: npm run docker:dev');
    console.log('  生产环境: npm run docker:prod');
    console.log('  查看日志: npm run docker:logs');
  } else {
    log('red', '❌ 部分检查未通过，请解决上述问题后重试');
    console.log('\n帮助文档:');
    console.log('  Docker 安装: https://docs.docker.com/get-docker/');
    console.log('  部署指南: ./DOCKER_DEPLOYMENT.md');
    console.log('  快速开始: ./DOCKER_QUICKSTART.md');
  }

  process.exit(allChecksPass ? 0 : 1);
}

if (require.main === module) {
  main();
}
