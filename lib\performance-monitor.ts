/**
 * 性能监控工具
 * 用于监控和诊断页面加载性能问题
 */

interface PerformanceMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  metadata?: Record<string, any>
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map()
  private static instance: PerformanceMonitor

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * 开始计时
   */
  start(name: string, metadata?: Record<string, any>): void {
    this.metrics.set(name, {
      name,
      startTime: Date.now(),
      metadata
    })
  }

  /**
   * 结束计时
   */
  end(name: string): number | null {
    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`性能指标 "${name}" 未找到`)
      return null
    }

    const endTime = Date.now()
    const duration = endTime - metric.startTime

    metric.endTime = endTime
    metric.duration = duration

    // 如果耗时超过阈值，记录警告
    if (duration > 1000) {
      console.warn(`⚠️ 性能警告: ${name} 耗时 ${duration}ms`)
    } else if (duration > 500) {
      console.info(`ℹ️ 性能提示: ${name} 耗时 ${duration}ms`)
    }

    return duration
  }

  /**
   * 获取性能报告
   */
  getReport(): PerformanceMetric[] {
    return Array.from(this.metrics.values())
      .filter(metric => metric.duration !== undefined)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
  }

  /**
   * 清除所有指标
   */
  clear(): void {
    this.metrics.clear()
  }

  /**
   * 获取慢操作（超过阈值的操作）
   */
  getSlowOperations(threshold: number = 1000): PerformanceMetric[] {
    return this.getReport().filter(metric => (metric.duration || 0) > threshold)
  }

  /**
   * 打印性能报告
   */
  printReport(): void {
    const report = this.getReport()
    if (report.length === 0) {
      console.log('📊 性能报告: 暂无数据')
      return
    }

    console.log('📊 性能报告:')
    console.table(report.map(metric => ({
      操作: metric.name,
      耗时: `${metric.duration}ms`,
      开始时间: new Date(metric.startTime).toLocaleTimeString(),
      结束时间: metric.endTime ? new Date(metric.endTime).toLocaleTimeString() : '未完成'
    })))

    const slowOps = this.getSlowOperations()
    if (slowOps.length > 0) {
      console.warn('🐌 慢操作警告:')
      slowOps.forEach(op => {
        console.warn(`  - ${op.name}: ${op.duration}ms`)
      })
    }
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()

// 便捷函数
export const startTimer = (name: string, metadata?: Record<string, any>) => 
  performanceMonitor.start(name, metadata)

export const endTimer = (name: string) => 
  performanceMonitor.end(name)

export const getPerformanceReport = () => 
  performanceMonitor.getReport()

export const printPerformanceReport = () => 
  performanceMonitor.printReport()

/**
 * 装饰器：自动监控函数执行时间
 */
export function monitor(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const metricName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function (...args: any[]) {
      startTimer(metricName)
      try {
        const result = await originalMethod.apply(this, args)
        return result
      } finally {
        endTimer(metricName)
      }
    }

    return descriptor
  }
}

/**
 * 监控Promise执行时间
 */
export async function monitorPromise<T>(
  name: string, 
  promise: Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  startTimer(name, metadata)
  try {
    const result = await promise
    return result
  } finally {
    endTimer(name)
  }
}

/**
 * 监控函数执行时间
 */
export async function monitorFunction<T>(
  name: string,
  fn: () => Promise<T> | T,
  metadata?: Record<string, any>
): Promise<T> {
  startTimer(name, metadata)
  try {
    const result = await fn()
    return result
  } finally {
    endTimer(name)
  }
}

// 页面加载性能监控
if (typeof window !== 'undefined') {
  // 监控页面加载性能
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    console.log('📈 页面加载性能:')
    console.log(`  DNS查询: ${navigation.domainLookupEnd - navigation.domainLookupStart}ms`)
    console.log(`  TCP连接: ${navigation.connectEnd - navigation.connectStart}ms`)
    console.log(`  请求响应: ${navigation.responseEnd - navigation.requestStart}ms`)
    console.log(`  DOM解析: ${navigation.domContentLoadedEventEnd - navigation.responseEnd}ms`)
    console.log(`  总加载时间: ${navigation.loadEventEnd - navigation.navigationStart}ms`)
  })
}
