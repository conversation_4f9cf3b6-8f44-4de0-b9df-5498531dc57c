"use client"

import { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthState } from '@/hooks/use-auth'
import { Button } from './ui/button'
import { 
  User, 
  LogOut, 
  Settings, 
  ChevronDown,
  Clock,
  Shield
} from 'lucide-react'

/**
 * 用户菜单组件
 * 显示用户信息和相关操作菜单
 */
export function UserMenu() {
  const router = useRouter()
  const { user, sessionInfo } = useAuthState()
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 处理登出
  const handleLogout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        // 登出成功，重定向到登录页面
        router.push('/login')
      } else {
        console.error('登出失败')
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    }
    setIsOpen(false)
  }

  // 格式化剩余时间
  const formatRemainingTime = (minutes: number) => {
    if (minutes < 60) {
      return `${Math.round(minutes)} 分钟`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = Math.round(minutes % 60)
    return `${hours} 小时 ${remainingMinutes} 分钟`
  }

  return (
    <div className="relative" ref={menuRef}>
      {/* 用户菜单触发按钮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100"
      >
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
          <span className="hidden md:block">
            {user?.username || '管理员'}
          </span>
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1">
            {/* 用户信息头部 */}
            <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {user?.username || '管理员'}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    系统管理员
                  </p>
                </div>
              </div>
            </div>

            {/* 会话信息 */}
            {sessionInfo && (
              <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-400">
                    <Clock className="w-3 h-3" />
                    <span>
                      会话剩余时间: {formatRemainingTime(sessionInfo.remainingMinutes)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-400">
                    <Shield className="w-3 h-3" />
                    <span>
                      登录时间: {new Date(sessionInfo.loginTime).toLocaleString('zh-CN')}
                    </span>
                  </div>
                  
                  {/* 会话过期警告 */}
                  {sessionInfo.remainingMinutes < 30 && (
                    <div className="text-xs text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded">
                      ⚠️ 会话即将过期，请及时保存工作
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 菜单项 */}
            <div className="py-1">
              <button
                onClick={() => {
                  setIsOpen(false)
                  // 这里可以添加设置页面的导航
                }}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <Settings className="w-4 h-4 mr-3" />
                系统设置
              </button>
              
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 transition-colors duration-200"
              >
                <LogOut className="w-4 h-4 mr-3" />
                退出登录
              </button>
            </div>

            {/* 版本信息 */}
            <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                美国地址生成器 v1.0.0
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
