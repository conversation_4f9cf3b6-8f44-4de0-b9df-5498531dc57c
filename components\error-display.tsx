"use client"

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  AlertTriangle, 
  RefreshCw, 
  Wifi, 
  Database, 
  Server,
  ExternalLink,
  Copy,
  ChevronDown,
  ChevronUp
} from "lucide-react"
import { useState } from "react"
import Link from "next/link"

interface ErrorDisplayProps {
  error: string | Error
  context?: string
  onRetry?: () => void
  showDetails?: boolean
  suggestions?: string[]
}

export default function ErrorDisplay({ 
  error, 
  context = "操作", 
  onRetry, 
  showDetails = false,
  suggestions = []
}: ErrorDisplayProps) {
  const [showFullError, setShowFullError] = useState(false)
  
  const errorMessage = error instanceof Error ? error.message : error
  const errorStack = error instanceof Error ? error.stack : null

  // 分析错误类型并提供相应的建议
  const analyzeError = (errorMsg: string) => {
    const lowerMsg = errorMsg.toLowerCase()
    
    if (lowerMsg.includes('network') || lowerMsg.includes('fetch')) {
      return {
        type: 'network',
        icon: <Wifi className="h-5 w-5" />,
        title: '网络连接问题',
        suggestions: [
          '检查网络连接是否正常',
          '确认服务器是否正在运行',
          '检查防火墙设置',
          '尝试刷新页面'
        ]
      }
    }
    
    if (lowerMsg.includes('supabase') || lowerMsg.includes('database')) {
      return {
        type: 'database',
        icon: <Database className="h-5 w-5" />,
        title: '数据库连接问题',
        suggestions: [
          '检查 Supabase 配置是否正确',
          '确认环境变量是否设置',
          '检查数据库表是否存在',
          '验证 API 密钥是否有效'
        ]
      }
    }
    
    if (lowerMsg.includes('api') || lowerMsg.includes('500') || lowerMsg.includes('404')) {
      return {
        type: 'api',
        icon: <Server className="h-5 w-5" />,
        title: 'API 接口问题',
        suggestions: [
          '检查 API 路由是否正确',
          '确认服务器是否正常运行',
          '查看服务器日志',
          '检查请求参数是否正确'
        ]
      }
    }
    
    return {
      type: 'general',
      icon: <AlertTriangle className="h-5 w-5" />,
      title: '系统错误',
      suggestions: [
        '尝试刷新页面',
        '检查浏览器控制台',
        '清除浏览器缓存',
        '联系技术支持'
      ]
    }
  }

  const errorAnalysis = analyzeError(errorMessage)
  const allSuggestions = [...errorAnalysis.suggestions, ...suggestions]

  const copyErrorToClipboard = () => {
    const errorInfo = `
错误上下文: ${context}
错误信息: ${errorMessage}
时间: ${new Date().toLocaleString('zh-CN')}
${errorStack ? `\n堆栈信息:\n${errorStack}` : ''}
    `.trim()
    
    navigator.clipboard.writeText(errorInfo).then(() => {
      // 可以添加一个 toast 提示
    })
  }

  return (
    <div className="space-y-4">
      {/* 主要错误提示 */}
      <Alert variant="destructive">
        <div className="flex items-start gap-3">
          {errorAnalysis.icon}
          <div className="flex-1">
            <AlertTitle>{errorAnalysis.title}</AlertTitle>
            <AlertDescription className="mt-1">
              {context}失败: {errorMessage}
            </AlertDescription>
          </div>
        </div>
      </Alert>

      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-2">
        {onRetry && (
          <Button onClick={onRetry} variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            重试
          </Button>
        )}
        <Button onClick={copyErrorToClipboard} variant="outline" size="sm">
          <Copy className="mr-2 h-4 w-4" />
          复制错误信息
        </Button>
        <Link href="/debug">
          <Button variant="outline" size="sm">
            <Database className="mr-2 h-4 w-4" />
            系统诊断
          </Button>
        </Link>
        {showDetails && (
          <Button 
            onClick={() => setShowFullError(!showFullError)} 
            variant="outline" 
            size="sm"
          >
            {showFullError ? (
              <>
                <ChevronUp className="mr-2 h-4 w-4" />
                隐藏详情
              </>
            ) : (
              <>
                <ChevronDown className="mr-2 h-4 w-4" />
                显示详情
              </>
            )}
          </Button>
        )}
      </div>

      {/* 建议解决方案 */}
      {allSuggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">建议解决方案</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {allSuggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{suggestion}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* 详细错误信息 */}
      {showDetails && showFullError && errorStack && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">详细错误信息</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto whitespace-pre-wrap">
              {errorStack}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* 快速链接 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Link href="/">
              <Button variant="outline" size="sm">
                返回首页
              </Button>
            </Link>
            <Link href="/saved-data">
              <Button variant="outline" size="sm">
                查看数据
              </Button>
            </Link>
            <Link href="/debug">
              <Button variant="outline" size="sm">
                系统诊断
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
