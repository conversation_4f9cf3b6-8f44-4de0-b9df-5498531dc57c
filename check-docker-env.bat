@echo off
echo.
echo ==========================================
echo Docker环境检查工具
echo ==========================================
echo.

echo [检查1] Docker安装状态...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装
    echo 💡 请从以下地址下载安装Docker Desktop:
    echo    https://www.docker.com/products/docker-desktop
    goto :end_check
) else (
    echo ✅ Docker已安装
    docker --version
)

echo.
echo [检查2] Docker服务状态...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker服务未运行
    echo 💡 请启动Docker Desktop应用程序
    goto :end_check
) else (
    echo ✅ Docker服务正常运行
)

echo.
echo [检查3] Docker Compose状态...
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    docker compose version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Docker Compose未安装
        echo 💡 请安装Docker Compose或更新Docker Desktop
        goto :end_check
    ) else (
        echo ✅ Docker Compose (plugin)已安装
        docker compose version
    )
) else (
    echo ✅ Docker Compose已安装
    docker-compose --version
)

echo.
echo [检查4] 系统资源...
for /f "tokens=2 delims=:" %%i in ('docker system df ^| findstr "Total"') do (
    echo 💾 Docker磁盘使用: %%i
)

echo.
echo [检查5] 项目文件...
if not exist "package.json" (
    echo ❌ package.json不存在
    echo 💡 请在项目根目录运行此脚本
    goto :end_check
) else (
    echo ✅ package.json存在
)

if not exist "Dockerfile" (
    echo ❌ Dockerfile不存在
    goto :end_check
) else (
    echo ✅ Dockerfile存在
)

if not exist "docker-compose.yml" (
    echo ❌ docker-compose.yml不存在
    goto :end_check
) else (
    echo ✅ docker-compose.yml存在
)

echo.
echo [检查6] 环境变量配置...
if not exist ".env.local" (
    echo ⚠️  .env.local不存在
    if exist ".env.local.example" (
        echo 💡 可以从.env.local.example创建
        set /p create_env="是否现在创建? (y/n): "
        if /i "%create_env%"=="y" (
            copy .env.local.example .env.local
            echo ✅ 已创建.env.local文件
            echo 💡 请编辑此文件配置您的环境变量
        )
    ) else (
        echo ❌ .env.local.example也不存在
    )
) else (
    echo ✅ .env.local存在
    
    REM 检查关键环境变量
    findstr "NEXT_PUBLIC_SUPABASE_URL" .env.local >nul
    if %errorlevel% neq 0 (
        echo ⚠️  缺少NEXT_PUBLIC_SUPABASE_URL配置
    ) else (
        echo ✅ SUPABASE_URL已配置
    )
    
    findstr "ADMIN_USERNAME" .env.local >nul
    if %errorlevel% neq 0 (
        echo ⚠️  缺少ADMIN_USERNAME配置
    ) else (
        echo ✅ ADMIN_USERNAME已配置
    )
)

echo.
echo [检查7] 端口占用...
netstat -an | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口3000已被占用
    echo 💡 请停止占用端口的程序或修改docker-compose.yml中的端口映射
) else (
    echo ✅ 端口3000可用
)

echo.
echo ==========================================
echo 检查完成
echo ==========================================
echo.

REM 检查是否所有条件都满足
if exist "package.json" if exist "Dockerfile" if exist "docker-compose.yml" (
    docker info >nul 2>&1
    if %errorlevel% equ 0 (
        echo 🎉 环境检查通过！可以开始Docker部署
        echo.
        echo 💡 下一步操作:
        echo    1. 确保.env.local文件已正确配置
        echo    2. 运行: deploy-docker.bat
        echo    3. 或手动运行: docker-compose up --build
        goto :end
    )
)

:end_check
echo ❌ 环境检查未通过，请解决上述问题后重试

:end
echo.
pause
