import { NextRequest, NextResponse } from 'next/server'
import {
  validateCredentials,
  generateJWT,
  generateSessionId,
  createUserObject,
  isValidUsername,
  isValidPassword
} from '@/lib/jwt'
import { startEmailSyncOnLogin } from '@/lib/email-sync-manager'
import { addActiveSession } from '@/lib/session-manager'

// 登录尝试记录（简单的内存存储，生产环境应使用数据库）
const loginAttempts = new Map<string, { count: number; lastAttempt: number }>()

/**
 * 检查是否被锁定
 */
function isLockedOut(ip: string): boolean {
  const attempts = loginAttempts.get(ip)
  if (!attempts) return false
  
  const now = Date.now()
  const lockoutDuration = 15 * 60 * 1000 // 15分钟
  
  if (attempts.count >= 5 && (now - attempts.lastAttempt) < lockoutDuration) {
    return true
  }
  
  // 如果锁定时间已过，重置计数
  if ((now - attempts.lastAttempt) >= lockoutDuration) {
    loginAttempts.delete(ip)
  }
  
  return false
}

/**
 * 记录登录尝试
 */
function recordLoginAttempt(ip: string, success: boolean) {
  if (success) {
    // 登录成功，清除记录
    loginAttempts.delete(ip)
    return
  }
  
  const now = Date.now()
  const attempts = loginAttempts.get(ip) || { count: 0, lastAttempt: 0 }
  
  attempts.count += 1
  attempts.lastAttempt = now
  
  loginAttempts.set(ip, attempts)
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

/**
 * POST /api/auth/login
 * 用户登录接口
 */
export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    // 检查是否被锁定
    if (isLockedOut(clientIP)) {
      return NextResponse.json(
        { 
          success: false, 
          error: '登录尝试过多，请15分钟后再试' 
        },
        { status: 429 }
      )
    }
    
    // 解析请求体
    let body
    try {
      body = await request.json()
    } catch (error) {
      return NextResponse.json(
        { 
          success: false, 
          error: '请求格式无效' 
        },
        { status: 400 }
      )
    }
    
    const { username, password, rememberMe } = body
    
    // 验证输入
    if (!username || !password) {
      recordLoginAttempt(clientIP, false)
      return NextResponse.json(
        { 
          success: false, 
          error: '用户名和密码不能为空' 
        },
        { status: 400 }
      )
    }
    
    if (!isValidUsername(username)) {
      recordLoginAttempt(clientIP, false)
      return NextResponse.json(
        { 
          success: false, 
          error: '用户名格式无效' 
        },
        { status: 400 }
      )
    }
    
    if (!isValidPassword(password)) {
      recordLoginAttempt(clientIP, false)
      return NextResponse.json(
        { 
          success: false, 
          error: '密码格式无效' 
        },
        { status: 400 }
      )
    }
    
    // 验证凭据
    if (!validateCredentials(username, password)) {
      recordLoginAttempt(clientIP, false)
      return NextResponse.json(
        { 
          success: false, 
          error: '用户名或密码错误' 
        },
        { status: 401 }
      )
    }
    
    // 登录成功，生成会话
    const sessionId = generateSessionId()
    const user = createUserObject(username, sessionId)
    
    // 生成 JWT token
    const token = await generateJWT({
      username: user.username,
      sessionId: user.sessionId,
      loginTime: user.loginTime
    })
    
    // 记录成功的登录尝试
    recordLoginAttempt(clientIP, true)

    // 添加活跃会话
    addActiveSession(user.sessionId, user.username, token, user.loginTime)

    // 启动邮件同步
    try {
      const syncResult = await startEmailSyncOnLogin(5) // 默认5分钟间隔
      console.log('邮件同步启动结果:', syncResult)
    } catch (syncError) {
      console.error('启动邮件同步失败:', syncError)
      // 不影响登录流程，只记录错误
    }

    // 创建响应
    const response = NextResponse.json({
      success: true,
      user,
      token,
      message: '登录成功'
    })
    
    // 设置 HTTP-only cookie（可选的额外安全措施）
    const cookieOptions = [
      `auth-token=${token}`,
      'HttpOnly',
      'SameSite=Strict',
      'Path=/',
      rememberMe ? 'Max-Age=2592000' : '', // 30天或会话
    ].filter(Boolean).join('; ')
    
    response.headers.set('Set-Cookie', cookieOptions)
    
    return response
    
  } catch (error) {
    console.error('登录API错误:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误' 
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/auth/login
 * 返回登录页面信息（可选）
 */
export async function GET() {
  return NextResponse.json({
    message: '请使用 POST 方法进行登录',
    endpoints: {
      login: 'POST /api/auth/login',
      logout: 'POST /api/auth/logout',
      status: 'GET /api/auth/status'
    }
  })
}
