@echo off
echo.
echo ==========================================
echo 美国地址生成器 - Linux部署包打包工具
echo ==========================================
echo.

REM 设置变量
set PACKAGE_NAME=us-fake-gen-ui-linux-deploy
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set FINAL_NAME=%PACKAGE_NAME%_%TIMESTAMP%

echo [INFO] 开始创建Linux部署包...
echo [INFO] 包名: %FINAL_NAME%
echo [INFO] 时间: %date% %time%
echo.

REM 检查项目根目录
if not exist "package.json" (
    echo [ERROR] 请在项目根目录运行此脚本
    echo [ERROR] 当前目录应包含 package.json 文件
    pause
    exit /b 1
)

REM 清理旧的打包目录
if exist %FINAL_NAME% (
    echo [INFO] 清理旧的打包目录...
    rmdir /s /q %FINAL_NAME%
)

REM 创建新目录
mkdir %FINAL_NAME%
echo [SUCCESS] 打包目录创建完成: %FINAL_NAME%

echo.
echo [STEP] 复制核心目录...

REM 复制核心目录
set DIRS=app components lib hooks contexts database scripts public styles
for %%d in (%DIRS%) do (
    if exist %%d (
        echo [OK] 复制目录: %%d
        xcopy %%d %FINAL_NAME%\%%d /e /i /h /y /q
    ) else (
        echo [WARN] 目录不存在: %%d
    )
)

echo.
echo [STEP] 复制配置文件...

REM 复制配置文件
set CONFIG_FILES=package.json next.config.mjs tsconfig.json tailwind.config.ts postcss.config.mjs components.json middleware.ts .env.local.example .env.production
for %%f in (%CONFIG_FILES%) do (
    if exist %%f (
        echo [OK] 复制配置文件: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo [WARN] 配置文件不存在: %%f
    )
)

echo.
echo [STEP] 复制Docker文件...

REM 复制Docker文件
set DOCKER_FILES=Dockerfile Dockerfile.linux docker-compose.yml docker-compose.linux.yml docker-compose.dev.yml .dockerignore
for %%f in (%DOCKER_FILES%) do (
    if exist %%f (
        echo [OK] 复制Docker文件: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo [WARN] Docker文件不存在: %%f
    )
)

REM 复制nginx配置
if exist nginx (
    echo [OK] 复制nginx配置目录
    xcopy nginx %FINAL_NAME%\nginx /e /i /h /y /q
) else (
    echo [WARN] nginx目录不存在
)

if exist nginx.conf (
    echo [OK] 复制nginx.conf
    copy nginx.conf %FINAL_NAME%\ >nul
)

echo.
echo [STEP] 复制部署脚本...

REM 复制部署脚本
set SCRIPT_FILES=deploy-linux.sh pack-for-linux.sh deploy-docker.bat check-docker-env.bat
for %%f in (%SCRIPT_FILES%) do (
    if exist %%f (
        echo [OK] 复制脚本: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo [WARN] 脚本文件不存在: %%f
    )
)

echo.
echo [STEP] 复制文档文件...

REM 复制文档文件
set DOC_FILES=README.md SETUP.md DOCKER_DEPLOYMENT.md DOCKER_QUICKSTART.md PACKAGING_CHECKLIST.md MANUAL_PACKAGING_GUIDE.md
for %%f in (%DOC_FILES%) do (
    if exist %%f (
        echo [OK] 复制文档: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo [WARN] 文档文件不存在: %%f
    )
)

echo.
echo [STEP] 创建Linux部署说明...

REM 创建Linux部署说明
(
echo # Linux部署指南
echo.
echo ## 🚀 快速部署
echo.
echo ### 自动部署（推荐）
echo ```bash
echo # 1. 解压部署包
echo tar -xzf %FINAL_NAME%.tar.gz
echo cd %FINAL_NAME%
echo.
echo # 2. 运行自动部署脚本
echo chmod +x deploy-linux.sh
echo ./deploy-linux.sh
echo ```
echo.
echo ### 手动部署
echo ```bash
echo # 1. 安装Docker
echo curl -fsSL https://get.docker.com ^| sh
echo sudo systemctl start docker
echo sudo systemctl enable docker
echo.
echo # 2. 配置环境变量
echo cp .env.local.example .env.production
echo # 编辑 .env.production 文件
echo.
echo # 3. 构建和启动
echo docker compose -f docker-compose.linux.yml up --build -d
echo.
echo # 4. 访问应用
echo # http://localhost:3000
echo ```
echo.
echo ## 📋 系统要求
echo.
echo - **操作系统**: Ubuntu 18.04+, Debian 10+, CentOS 7+, RHEL 7+
echo - **内存**: 最少2GB，推荐4GB+
echo - **磁盘**: 最少5GB可用空间
echo - **网络**: 需要访问互联网下载依赖
echo.
echo ## 🔧 必需环境变量
echo.
echo ```env
echo NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
echo NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
echo ADMIN_USERNAME=your_admin_username
echo ADMIN_PASSWORD=your_secure_password
echo JWT_SECRET=your_jwt_secret_32_chars_min
echo ```
echo.
echo ## 🛠️ 管理命令
echo.
echo ```bash
echo # 查看服务状态
echo docker compose -f docker-compose.linux.yml ps
echo.
echo # 查看日志
echo docker compose -f docker-compose.linux.yml logs -f
echo.
echo # 重启服务
echo docker compose -f docker-compose.linux.yml restart
echo.
echo # 停止服务
echo docker compose -f docker-compose.linux.yml down
echo ```
echo.
echo ---
echo *打包时间: %date% %time%*
echo *此部署包包含完整的Linux部署配置*
) > %FINAL_NAME%\LINUX_DEPLOYMENT.md

echo [SUCCESS] Linux部署说明创建完成

echo.
echo [STEP] 创建快速启动脚本...

REM 创建快速启动脚本
(
echo #!/bin/bash
echo.
echo # 美国地址生成器 - 快速启动脚本
echo.
echo echo "🚀 美国地址生成器 - 快速启动"
echo echo "================================"
echo.
echo # 检查Docker
echo if ! command -v docker ^&^> /dev/null; then
echo     echo "❌ Docker未安装，正在安装..."
echo     curl -fsSL https://get.docker.com ^| sh
echo     sudo systemctl start docker
echo     sudo systemctl enable docker
echo     sudo usermod -aG docker $USER
echo     echo "✅ Docker安装完成，请重新登录后再次运行此脚本"
echo     exit 0
echo fi
echo.
echo # 检查环境变量文件
echo if [ ! -f ".env.production" ]; then
echo     if [ -f ".env.local.example" ]; then
echo         cp .env.local.example .env.production
echo         echo "⚠️  已创建.env.production文件，请编辑配置后重新运行"
echo         echo "📝 必需配置项："
echo         echo "   - NEXT_PUBLIC_SUPABASE_URL"
echo         echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY"
echo         echo "   - ADMIN_USERNAME"
echo         echo "   - ADMIN_PASSWORD"
echo         echo "   - JWT_SECRET"
echo         exit 1
echo     fi
echo fi
echo.
echo # 启动服务
echo echo "🔧 启动服务..."
echo docker compose -f docker-compose.linux.yml up -d
echo.
echo echo "✅ 启动完成！"
echo echo "🌐 访问地址: http://localhost:3000"
echo echo "📊 查看状态: docker compose -f docker-compose.linux.yml ps"
echo echo "📋 查看日志: docker compose -f docker-compose.linux.yml logs -f"
) > %FINAL_NAME%\quick-start.sh

echo [SUCCESS] 快速启动脚本创建完成

echo.
echo [STEP] 统计打包信息...

REM 统计文件数量
for /f %%i in ('dir /s /b %FINAL_NAME% ^| find /c /v ""') do set FILE_COUNT=%%i
for /f %%i in ('dir /s /ad %FINAL_NAME% ^| find /c /v ""') do set DIR_COUNT=%%i

echo [STATS] 打包统计信息:
echo    文件数量: %FILE_COUNT%
echo    目录数量: %DIR_COUNT%

echo.
echo [STEP] 创建压缩包...

REM 尝试创建压缩包
powershell -command "Compress-Archive -Path '%FINAL_NAME%\*' -DestinationPath '%FINAL_NAME%.zip' -Force" 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] 压缩包创建成功: %FINAL_NAME%.zip
) else (
    echo [WARN] 无法创建ZIP压缩包，请手动压缩 %FINAL_NAME% 文件夹
)

echo.
echo ==========================================
echo 🎉 Linux部署包打包完成！
echo ==========================================
echo.
echo 📦 部署包信息:
echo    文件夹: %FINAL_NAME%\
if exist %FINAL_NAME%.zip (
    echo    压缩包: %FINAL_NAME%.zip
)
echo.
echo 🚀 Linux服务器使用方法:
echo    1. 将部署包传输到Linux服务器
if exist %FINAL_NAME%.zip (
    echo    2. 解压: unzip %FINAL_NAME%.zip
) else (
    echo    2. 解压文件夹到Linux服务器
)
echo    3. 进入目录: cd %FINAL_NAME%
echo    4. 设置权限: chmod +x *.sh
echo    5. 运行部署: ./deploy-linux.sh
echo    6. 或快速启动: ./quick-start.sh
echo.
echo 📚 相关文档:
echo    - LINUX_DEPLOYMENT.md - Linux部署指南
echo    - README.md - 项目说明
echo    - SETUP.md - 设置指南
echo.
echo 💡 提示:
echo    - 确保Linux服务器已安装Docker
echo    - 配置好.env.production文件中的环境变量
echo    - 生产环境建议配置SSL证书和域名
echo.

pause
