import { NextRequest, NextResponse } from 'next/server'
import { extractTokenFromRequest, verifyJWT } from '@/lib/jwt'
import { isTokenBlacklisted } from '@/lib/token-blacklist'

/**
 * 计算会话剩余时间
 */
function getSessionTimeRemaining(exp: number): number {
  const now = Math.floor(Date.now() / 1000)
  return Math.max(0, exp - now)
}

/**
 * 格式化时间
 */
function formatTimeRemaining(seconds: number): string {
  if (seconds <= 0) return '已过期'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

/**
 * GET /api/auth/status
 * 检查用户认证状态
 */
export async function GET(request: NextRequest) {
  try {
    // 提取token
    const token = extractTokenFromRequest(request)
    
    if (!token) {
      return NextResponse.json({
        success: false,
        authenticated: false,
        error: '未找到认证token'
      })
    }
    
    // 验证token
    const payload = await verifyJWT(token)
    
    if (!payload) {
      return NextResponse.json({
        success: false,
        authenticated: false,
        error: 'token无效或已过期'
      })
    }
    
    // 检查token是否在黑名单中
    if (isTokenBlacklisted(token)) {
      return NextResponse.json({
        success: false,
        authenticated: false,
        error: 'token已失效'
      })
    }
    
    // 计算会话信息
    const timeRemaining = getSessionTimeRemaining(payload.exp)
    const isExpiringSoon = timeRemaining < 3600 // 小于1小时
    
    return NextResponse.json({
      success: true,
      authenticated: true,
      user: {
        username: payload.username,
        sessionId: payload.sessionId,
        loginTime: payload.loginTime
      },
      session: {
        issuedAt: payload.iat,
        expiresAt: payload.exp,
        timeRemaining: timeRemaining,
        timeRemainingFormatted: formatTimeRemaining(timeRemaining),
        isExpiringSoon: isExpiringSoon
      },
      permissions: {
        admin: payload.username === 'admin',
        emailSync: true // 所有登录用户都可以使用邮件同步
      }
    })
    
  } catch (error) {
    console.error('状态检查API错误:', error)
    return NextResponse.json(
      { 
        success: false,
        authenticated: false,
        error: '服务器内部错误' 
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/auth/status
 * 刷新会话状态（可选功能）
 */
export async function POST(request: NextRequest) {
  try {
    // 提取token
    const token = extractTokenFromRequest(request)
    
    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: '未找到认证token' 
        },
        { status: 401 }
      )
    }
    
    // 验证token
    const payload = await verifyJWT(token)
    
    if (!payload) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'token无效或已过期' 
        },
        { status: 401 }
      )
    }
    
    // 检查token是否在黑名单中
    if (isTokenBlacklisted(token)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'token已失效' 
        },
        { status: 401 }
      )
    }
    
    // 返回刷新后的状态
    return NextResponse.json({
      success: true,
      message: '会话状态已刷新',
      user: {
        username: payload.username,
        sessionId: payload.sessionId,
        loginTime: payload.loginTime
      },
      lastRefresh: Date.now()
    })
    
  } catch (error) {
    console.error('状态刷新API错误:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误' 
      },
      { status: 500 }
    )
  }
}
