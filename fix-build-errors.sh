#!/bin/bash

# 修复构建错误脚本
# 自动检测和修复常见的Next.js构建问题

echo "🔧 检测和修复构建错误..."

# 检查语法错误
echo "📝 检查TypeScript语法错误..."

# 使用npx tsc进行类型检查
if command -v npx &> /dev/null; then
    echo "正在进行TypeScript类型检查..."
    npx tsc --noEmit --skipLibCheck
    if [ $? -eq 0 ]; then
        echo "✅ TypeScript类型检查通过"
    else
        echo "❌ TypeScript类型检查失败，请检查上述错误"
    fi
fi

# 检查ESLint错误
echo "📋 检查ESLint错误..."
if [ -f ".eslintrc.json" ] || [ -f ".eslintrc.js" ]; then
    if command -v npx &> /dev/null; then
        npx eslint . --ext .ts,.tsx,.js,.jsx --fix
        echo "✅ ESLint检查和自动修复完成"
    fi
fi

# 清理缓存
echo "🧹 清理构建缓存..."
rm -rf .next
rm -rf node_modules/.cache
if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
    pnpm store prune
else
    npm cache clean --force
fi

echo "✅ 构建错误修复完成"
echo "🚀 现在可以重新尝试构建: npm run build"
