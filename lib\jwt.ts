/**
 * JWT 工具函数
 * 处理 JWT token 的生成、验证和解析
 */

import { getConfig } from './config'

export interface JWTPayload {
  username: string
  sessionId: string
  loginTime: number
  iat: number
  exp: number
}

/**
 * 简单的 Base64 编码
 */
function base64UrlEncode(str: string): string {
  return Buffer.from(str)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '')
}

/**
 * 简单的 Base64 解码
 */
function base64UrlDecode(str: string): string {
  // 补齐 padding
  str += '='.repeat((4 - str.length % 4) % 4)
  return Buffer.from(str.replace(/-/g, '+').replace(/_/g, '/'), 'base64').toString()
}

/**
 * 创建 HMAC SHA256 签名
 */
async function createSignature(data: string, secret: string): Promise<string> {
  const encoder = new TextEncoder()
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  )
  
  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data))
  return base64UrlEncode(String.fromCharCode(...new Uint8Array(signature)))
}

/**
 * 验证 HMAC SHA256 签名
 */
async function verifySignature(data: string, signature: string, secret: string): Promise<boolean> {
  try {
    const expectedSignature = await createSignature(data, secret)
    return expectedSignature === signature
  } catch (error) {
    console.error('签名验证失败:', error)
    return false
  }
}

/**
 * 生成 JWT token
 */
export async function generateJWT(payload: Omit<JWTPayload, 'iat' | 'exp'>): Promise<string> {
  const config = getConfig()
  const now = Math.floor(Date.now() / 1000)
  const exp = now + (config.auth.sessionTimeout / 1000) // 转换为秒
  
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  }
  
  const fullPayload: JWTPayload = {
    ...payload,
    iat: now,
    exp: exp
  }
  
  const encodedHeader = base64UrlEncode(JSON.stringify(header))
  const encodedPayload = base64UrlEncode(JSON.stringify(fullPayload))
  const data = `${encodedHeader}.${encodedPayload}`
  
  const signature = await createSignature(data, config.auth.jwtSecret)
  
  return `${data}.${signature}`
}

/**
 * 验证并解析 JWT token
 */
export async function verifyJWT(token: string): Promise<JWTPayload | null> {
  try {
    const config = getConfig()
    const parts = token.split('.')
    
    if (parts.length !== 3) {
      return null
    }
    
    const [encodedHeader, encodedPayload, signature] = parts
    const data = `${encodedHeader}.${encodedPayload}`
    
    // 验证签名
    const isValidSignature = await verifySignature(data, signature, config.auth.jwtSecret)
    if (!isValidSignature) {
      return null
    }
    
    // 解析 payload
    const payload: JWTPayload = JSON.parse(base64UrlDecode(encodedPayload))
    
    // 检查过期时间
    const now = Math.floor(Date.now() / 1000)
    if (payload.exp < now) {
      return null
    }
    
    return payload
  } catch (error) {
    console.error('JWT 验证失败:', error)
    return null
  }
}

/**
 * 从请求中提取 JWT token
 * 支持从 Authorization 头和 Cookie 中提取
 */
export function extractTokenFromRequest(request: Request): string | null {
  // 首先尝试从 Authorization 头中提取
  const authHeader = request.headers.get('Authorization')
  if (authHeader) {
    const match = authHeader.match(/^Bearer\s+(.+)$/)
    if (match) {
      return match[1]
    }
  }

  // 如果 Authorization 头中没有，尝试从 Cookie 中提取
  const cookieHeader = request.headers.get('Cookie')
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').map(cookie => cookie.trim())
    for (const cookie of cookies) {
      if (cookie.startsWith('auth-token=')) {
        const token = cookie.substring('auth-token='.length)
        if (token && token !== '') {
          return token
        }
      }
    }
  }

  return null
}

/**
 * 生成会话 ID
 */
export function generateSessionId(): string {
  const timestamp = Date.now().toString(36)
  const randomPart = Math.random().toString(36).substring(2)
  return `${timestamp}-${randomPart}`
}

/**
 * 验证用户凭据
 */
export function validateCredentials(username: string, password: string): boolean {
  try {
    const config = getConfig()
    
    // 简单的用户名密码验证
    return username === config.auth.adminUsername && password === config.auth.adminPassword
  } catch (error) {
    console.error('凭据验证失败:', error)
    return false
  }
}

/**
 * 创建用户对象
 */
export function createUserObject(username: string, sessionId: string) {
  return {
    username,
    sessionId,
    loginTime: Date.now()
  }
}

/**
 * 检查是否为有效的用户名
 */
export function isValidUsername(username: string): boolean {
  return typeof username === 'string' && 
         username.length >= 3 && 
         username.length <= 50 &&
         /^[a-zA-Z0-9_-]+$/.test(username)
}

/**
 * 检查是否为有效的密码
 */
export function isValidPassword(password: string): boolean {
  return typeof password === 'string' && 
         password.length >= 6 && 
         password.length <= 100
}
