@echo off
echo.
echo ==========================================
echo Docker Deployment Package Builder
echo ==========================================
echo.

REM Set variables
set PACKAGE_NAME=us-fake-gen-ui-deploy
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set FINAL_NAME=%PACKAGE_NAME%_%TIMESTAMP%

echo [INFO] Starting package creation...
echo [INFO] Timestamp: %TIMESTAMP%
echo [INFO] Package name: %FINAL_NAME%
echo.

REM Check if current directory is project root
if not exist "package.json" (
    echo [ERROR] Please run this script from project root directory
    echo [ERROR] Current directory should contain package.json file
    pause
    exit /b 1
)

REM Create temporary directory
if exist %FINAL_NAME% (
    echo [INFO] Cleaning old package directory...
    rmdir /s /q %FINAL_NAME%
)
mkdir %FINAL_NAME%

echo [INFO] Creating directory structure...

REM Copy directories (if exist)
set DIRS=app components lib hooks contexts database scripts public styles
for %%d in (%DIRS%) do (
    if exist %%d (
        echo   [OK] Copying directory: %%d
        xcopy %%d %FINAL_NAME%\%%d /e /i /h /y /q >nul
    ) else (
        echo   [WARN] Directory not found: %%d
    )
)

echo.
echo [INFO] Copying configuration files...

REM Copy individual files
set FILES=Dockerfile docker-compose.yml docker-compose.dev.yml nginx.conf .dockerignore .env.local.example .env.production package.json pnpm-lock.yaml next.config.mjs tsconfig.json tailwind.config.ts postcss.config.mjs components.json middleware.ts
for %%f in (%FILES%) do (
    if exist %%f (
        echo   [OK] Copying file: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo   [WARN] File not found: %%f
    )
)

echo.
echo [INFO] Copying documentation files...

REM Copy documentation files
set DOCS=README.md DOCKER_DEPLOYMENT.md DOCKER_QUICKSTART.md SETUP.md
for %%f in (%DOCS%) do (
    if exist %%f (
        echo   [OK] Copying document: %%f
        copy %%f %FINAL_NAME%\ >nul
    ) else (
        echo   [WARN] Document not found: %%f
    )
)

echo.
echo [INFO] Creating deployment instructions...

REM Create deployment instructions file
(
echo # Docker Deployment Package
echo.
echo Package created: %date% %time%
echo.
echo ## Quick Deployment Steps
echo.
echo 1. Install Docker Desktop
echo 2. Configure environment variables:
echo    copy .env.local.example .env.local
echo    # Edit .env.local with your database configuration
echo.
echo 3. Deploy application:
echo    # Windows
echo    scripts\deploy.bat dev
echo    # Or use Docker Compose directly
echo    docker-compose -f docker-compose.dev.yml up -d
echo.
echo 4. Access application: http://localhost:3000
echo.
echo ## File Description
echo.
echo - Dockerfile - Docker image build file
echo - docker-compose.yml - Production environment config
echo - docker-compose.dev.yml - Development environment config
echo - nginx.conf - Nginx reverse proxy config
echo - .env.local.example - Environment variables template
echo - scripts/deploy.bat - Windows deployment script
echo - DOCKER_DEPLOYMENT.md - Detailed deployment guide
echo.
echo ## Important Notes
echo.
echo - Do not commit .env.local file to version control
echo - Use strong passwords in production
echo - Ensure firewall allows port 3000 access
) > %FINAL_NAME%\DEPLOYMENT_README.md

echo   [OK] Created deployment instructions: DEPLOYMENT_README.md

echo.
echo [INFO] Checking package contents...

REM Count files and directories
for /f %%i in ('dir /s /b %FINAL_NAME% ^| find /c /v ""') do set FILE_COUNT=%%i
for /f %%i in ('dir /s /ad %FINAL_NAME% ^| find /c /v ""') do set DIR_COUNT=%%i

echo   [STATS] Package statistics:
echo      - Files: %FILE_COUNT%
echo      - Directories: %DIR_COUNT%

echo.
echo [INFO] Creating compressed package...

REM Try to create compressed package
set COMPRESSED=false

REM Try using PowerShell compression
powershell -command "try { Compress-Archive -Path '%FINAL_NAME%\*' -DestinationPath '%FINAL_NAME%.zip' -Force; Write-Host '[OK] PowerShell compression successful' } catch { Write-Host '[ERROR] PowerShell compression failed'; exit 1 }" 2>nul
if %errorlevel% equ 0 (
    set COMPRESSED=true
    echo   [OK] Compressed package created: %FINAL_NAME%.zip
) else (
    echo   [WARN] PowerShell compression failed, please manually compress %FINAL_NAME% folder
)

echo.
echo ==========================================
echo Package Creation Complete!
echo ==========================================
echo.
echo [RESULT] Package location:
if "%COMPRESSED%"=="true" (
    echo    Compressed: %FINAL_NAME%.zip
)
echo    Folder: %FINAL_NAME%\
echo.
echo [NEXT] Next steps:
echo    1. Transfer package to target server
echo    2. Extract and follow DEPLOYMENT_README.md instructions
echo    3. Access http://localhost:3000 to verify deployment
echo.
echo [DOCS] Related documentation:
echo    - DOCKER_DEPLOYMENT.md - Detailed deployment guide
echo    - DOCKER_QUICKSTART.md - Quick start guide
echo    - DEPLOYMENT_README.md - Package instructions
echo.

pause
