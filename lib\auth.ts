/**
 * 身份验证工具库
 * 提供用户登录、登出、会话管理等功能
 */

export interface User {
  username: string
  loginTime: number
  sessionId: string
}

export interface AuthState {
  isAuthenticated: boolean
  user: User | null
  loading: boolean
}

export interface LoginCredentials {
  username: string
  password: string
  rememberMe?: boolean
}

export interface AuthResponse {
  success: boolean
  user?: User
  token?: string
  error?: string
}

class AuthService {
  private readonly TOKEN_KEY = 'auth-token'
  private readonly USER_KEY = 'auth-user'
  private readonly SESSION_DURATION = 24 * 60 * 60 * 1000 // 24小时

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      const result = await response.json()

      if (result.success && result.token && result.user) {
        // 存储会话信息
        this.setSession(result.token, result.user, credentials.rememberMe)
        return result
      } else {
        return {
          success: false,
          error: result.error || '登录失败'
        }
      }
    } catch (error) {
      console.error('登录请求失败:', error)
      return {
        success: false,
        error: '网络错误，请检查网络连接'
      }
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      // 调用服务器端登出API
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json',
        },
      })
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 无论服务器端是否成功，都清除本地会话
      this.clearSession()
    }
  }

  /**
   * 检查用户是否已登录
   */
  isAuthenticated(): boolean {
    const token = this.getToken()
    const user = this.getUser()

    if (!token || !user) {
      return false
    }

    // 检查会话是否过期
    if (this.isSessionExpired(user)) {
      this.clearSession()
      return false
    }

    return true
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): User | null {
    if (!this.isAuthenticated()) {
      return null
    }
    return this.getUser()
  }

  /**
   * 获取认证token
   */
  getToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.TOKEN_KEY) || sessionStorage.getItem(this.TOKEN_KEY)
  }

  /**
   * 验证会话状态
   */
  async validateSession(): Promise<boolean> {
    const token = this.getToken()
    if (!token) return false

    try {
      const response = await fetch('/api/auth/status', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()
      
      if (!result.success) {
        this.clearSession()
        return false
      }

      return true
    } catch (error) {
      console.error('会话验证失败:', error)
      this.clearSession()
      return false
    }
  }

  /**
   * 设置会话信息
   */
  private setSession(token: string, user: User, rememberMe: boolean = false): void {
    if (typeof window === 'undefined') return

    const storage = rememberMe ? localStorage : sessionStorage

    storage.setItem(this.TOKEN_KEY, token)
    storage.setItem(this.USER_KEY, JSON.stringify(user))

    // 如果选择记住我，也在localStorage中存储
    if (rememberMe) {
      localStorage.setItem(this.TOKEN_KEY, token)
      localStorage.setItem(this.USER_KEY, JSON.stringify(user))
    }
  }

  /**
   * 清除会话信息
   */
  private clearSession(): void {
    if (typeof window === 'undefined') return

    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.USER_KEY)
    sessionStorage.removeItem(this.TOKEN_KEY)
    sessionStorage.removeItem(this.USER_KEY)
  }

  /**
   * 获取存储的用户信息
   */
  private getUser(): User | null {
    if (typeof window === 'undefined') return null

    const userStr = localStorage.getItem(this.USER_KEY) || sessionStorage.getItem(this.USER_KEY)
    if (!userStr) return null

    try {
      return JSON.parse(userStr)
    } catch (error) {
      console.error('解析用户信息失败:', error)
      return null
    }
  }

  /**
   * 检查会话是否过期
   */
  private isSessionExpired(user: User): boolean {
    const now = Date.now()
    const loginTime = user.loginTime
    return (now - loginTime) > this.SESSION_DURATION
  }

  /**
   * 生成会话ID
   */
  generateSessionId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// 导出单例实例
export const authService = new AuthService()

// 导出类型和工具函数
export { AuthService }

/**
 * 检查是否在浏览器环境
 */
export const isBrowser = typeof window !== 'undefined'

/**
 * 获取认证头部
 */
export const getAuthHeaders = (): Record<string, string> => {
  const token = authService.getToken()
  return token ? { 'Authorization': `Bearer ${token}` } : {}
}
