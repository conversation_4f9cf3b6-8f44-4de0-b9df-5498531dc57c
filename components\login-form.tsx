"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Eye, EyeOff, Lock, User, AlertCircle } from "lucide-react"
import { useAuth } from '@/hooks/use-auth'
import { LoginCredentials } from '@/lib/auth'

interface LoginFormProps {
  onSuccess?: () => void
}

export function LoginForm({ onSuccess }: LoginFormProps) {
  const router = useRouter()
  const { login, loading } = useAuth()
  
  const [credentials, setCredentials] = useState<LoginCredentials>({
    username: '',
    password: '',
    rememberMe: false,
  })
  
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 表单验证
  const validateForm = (): boolean => {
    if (!credentials.username.trim()) {
      setError('请输入用户名')
      return false
    }
    
    if (credentials.username.length < 3) {
      setError('用户名至少需要3个字符')
      return false
    }
    
    if (!credentials.password) {
      setError('请输入密码')
      return false
    }
    
    if (credentials.password.length < 6) {
      setError('密码至少需要6个字符')
      return false
    }
    
    return true
  }

  // 处理登录
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 清除之前的错误
    setError('')
    
    // 表单验证
    if (!validateForm()) {
      return
    }
    
    setIsSubmitting(true)
    
    try {
      const response = await login(credentials)
      
      if (response.success) {
        // 登录成功
        if (onSuccess) {
          onSuccess()
        } else {
          router.push('/')
        }
      } else {
        setError(response.error || '登录失败，请检查用户名和密码')
      }
    } catch (error) {
      console.error('登录过程中发生错误:', error)
      setError('登录过程中发生错误，请重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  // 处理输入变化
  const handleInputChange = (field: keyof LoginCredentials, value: string | boolean) => {
    setCredentials(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除错误信息
    if (error) {
      setError('')
    }
  }

  // 切换密码显示
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const isLoading = loading || isSubmitting

  return (
    <Card className="w-full shadow-lg">
      <CardHeader className="space-y-1 text-center">
        <div className="flex justify-center mb-4">
          <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
            <Lock className="w-6 h-6 text-white" />
          </div>
        </div>
        <CardTitle className="text-2xl font-bold">欢迎回来</CardTitle>
        <CardDescription>
          请登录您的账户以继续使用系统
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleLogin} className="space-y-4">
          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {/* 用户名输入 */}
          <div className="space-y-2">
            <Label htmlFor="username">用户名</Label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="username"
                type="text"
                placeholder="请输入用户名"
                value={credentials.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className="pl-10"
                disabled={isLoading}
                autoComplete="username"
                autoFocus
              />
            </div>
          </div>
          
          {/* 密码输入 */}
          <div className="space-y-2">
            <Label htmlFor="password">密码</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="请输入密码"
                value={credentials.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="pl-10 pr-10"
                disabled={isLoading}
                autoComplete="current-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={togglePasswordVisibility}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
          </div>
          
          {/* 记住我选项 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="rememberMe"
              checked={credentials.rememberMe}
              onCheckedChange={(checked) => handleInputChange('rememberMe', checked as boolean)}
              disabled={isLoading}
            />
            <Label 
              htmlFor="rememberMe" 
              className="text-sm font-normal cursor-pointer"
            >
              记住我
            </Label>
          </div>
          
          {/* 登录按钮 */}
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                登录中...
              </>
            ) : (
              '登录'
            )}
          </Button>
        </form>
        
        {/* 帮助信息 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            使用管理员账户登录系统
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
