/**
 * 会话管理器
 * 用于跟踪活跃的用户会话
 */

interface ActiveSession {
  sessionId: string
  username: string
  loginTime: number
  lastActivity: number
  token: string
}

// 活跃会话存储（简单的内存存储，生产环境应使用数据库或Redis）
const activeSessions = new Map<string, ActiveSession>()

/**
 * 添加活跃会话
 */
export function addActiveSession(sessionId: string, username: string, token: string, loginTime: number) {
  const session: ActiveSession = {
    sessionId,
    username,
    loginTime,
    lastActivity: Date.now(),
    token
  }
  
  activeSessions.set(sessionId, session)
  console.log(`添加活跃会话: ${username} (${sessionId})，当前活跃会话数: ${activeSessions.size}`)
}

/**
 * 移除活跃会话
 */
export function removeActiveSession(sessionId: string): boolean {
  const session = activeSessions.get(sessionId)
  const removed = activeSessions.delete(sessionId)
  
  if (removed && session) {
    console.log(`移除活跃会话: ${session.username} (${sessionId})，当前活跃会话数: ${activeSessions.size}`)
  }
  
  return removed
}

/**
 * 通过token移除会话
 */
export function removeActiveSessionByToken(token: string): boolean {
  for (const [sessionId, session] of activeSessions.entries()) {
    if (session.token === token) {
      return removeActiveSession(sessionId)
    }
  }
  return false
}

/**
 * 更新会话活动时间
 */
export function updateSessionActivity(sessionId: string): boolean {
  const session = activeSessions.get(sessionId)
  if (session) {
    session.lastActivity = Date.now()
    return true
  }
  return false
}

/**
 * 获取活跃会话数量
 */
export function getActiveSessionCount(): number {
  return activeSessions.size
}

/**
 * 检查是否有活跃会话
 */
export function hasActiveSessions(): boolean {
  return activeSessions.size > 0
}

/**
 * 获取所有活跃会话
 */
export function getActiveSessions(): ActiveSession[] {
  return Array.from(activeSessions.values())
}

/**
 * 根据sessionId获取会话
 */
export function getActiveSession(sessionId: string): ActiveSession | undefined {
  return activeSessions.get(sessionId)
}

/**
 * 清理过期的会话（超过24小时未活动）
 */
export function cleanupInactiveSessions(): number {
  const now = Date.now()
  const maxInactiveTime = 24 * 60 * 60 * 1000 // 24小时
  let cleanedCount = 0
  
  for (const [sessionId, session] of activeSessions.entries()) {
    if (now - session.lastActivity > maxInactiveTime) {
      activeSessions.delete(sessionId)
      cleanedCount++
      console.log(`清理过期会话: ${session.username} (${sessionId})`)
    }
  }
  
  if (cleanedCount > 0) {
    console.log(`清理了 ${cleanedCount} 个过期会话，当前活跃会话数: ${activeSessions.size}`)
  }
  
  return cleanedCount
}

/**
 * 获取会话统计信息
 */
export function getSessionStats() {
  const now = Date.now()
  const sessions = Array.from(activeSessions.values())
  
  return {
    total: sessions.length,
    usernames: [...new Set(sessions.map(s => s.username))],
    oldestSession: sessions.length > 0 ? Math.min(...sessions.map(s => s.loginTime)) : null,
    newestSession: sessions.length > 0 ? Math.max(...sessions.map(s => s.loginTime)) : null,
    averageAge: sessions.length > 0 ? (now - sessions.reduce((sum, s) => sum + s.loginTime, 0) / sessions.length) / 1000 / 60 : 0 // 分钟
  }
}

// 定期清理过期会话（每小时执行一次）
setInterval(cleanupInactiveSessions, 60 * 60 * 1000)
