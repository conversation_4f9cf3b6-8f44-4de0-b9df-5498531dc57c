"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Database, 
  Mail, 
  Download,
  Trash2,
  AlertTriangle,
  Info
} from "lucide-react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [settings, setSettings] = useState({
    // 邮件设置
    defaultEmailDomain: 'mcpserver.sbs',
    autoCreateEmails: true,
    emailRetentionDays: 30,
    
    // 数据设置
    defaultBatchSize: 10,
    maxBatchSize: 99,
    autoSaveData: true,
    
    // 系统设置
    enableBackup: true,
    backupInterval: 24, // 小时
    debugMode: false,
    
    // IMAP设置
    imapServer: '',
    imapPort: 993,
    imapUser: '',
    imapPassword: '',
    imapSSL: true
  })
  
  const [systemInfo, setSystemInfo] = useState({
    version: '1.0.0',
    dataPath: './data',
    totalSize: '0 MB',
    lastBackup: null
  })
  
  const { toast } = useToast()

  // 加载设置
  const loadSettings = async () => {
    setIsLoading(true)
    try {
      // 这里可以从API加载设置
      // const response = await fetch('/api/settings')
      // const result = await response.json()
      // if (result.success) {
      //   setSettings(result.data)
      // }
    } catch (error) {
      console.error('加载设置失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 保存设置
  const saveSettings = async () => {
    setIsLoading(true)
    try {
      // 这里可以保存设置到API
      // const response = await fetch('/api/settings', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(settings)
      // })
      
      toast({
        title: "设置保存成功",
        description: "系统设置已更新",
      })
    } catch (error) {
      toast({
        title: "设置保存失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 清理数据
  const clearData = async (type: 'emails' | 'messages' | 'generated' | 'all') => {
    if (!confirm(`确定要清理${type === 'all' ? '所有' : type}数据吗？此操作不可撤销！`)) {
      return
    }

    try {
      // 这里可以调用清理API
      toast({
        title: "数据清理成功",
        description: `${type}数据已清理`,
      })
    } catch (error) {
      toast({
        title: "数据清理失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    loadSettings()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto p-4">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                系统设置
              </h1>
              <p className="text-gray-600">
                配置系统参数和管理数据
              </p>
            </div>
            <div className="flex gap-2">
              <Link href="/saved-data">
                <Button variant="outline">
                  <Database className="mr-2 h-4 w-4" />
                  数据管理
                </Button>
              </Link>
              <Link href="/">
                <Button variant="outline">
                  返回主页
                </Button>
              </Link>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 邮件设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                邮件设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="defaultEmailDomain">默认邮箱域名</Label>
                <Input
                  id="defaultEmailDomain"
                  value={settings.defaultEmailDomain}
                  onChange={(e) => setSettings({...settings, defaultEmailDomain: e.target.value})}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoCreateEmails"
                  checked={settings.autoCreateEmails}
                  onCheckedChange={(checked) => setSettings({...settings, autoCreateEmails: checked as boolean})}
                />
                <Label htmlFor="autoCreateEmails">自动创建邮箱</Label>
              </div>
              
              <div>
                <Label htmlFor="emailRetentionDays">邮件保留天数</Label>
                <Input
                  id="emailRetentionDays"
                  type="number"
                  value={settings.emailRetentionDays}
                  onChange={(e) => setSettings({...settings, emailRetentionDays: parseInt(e.target.value)})}
                />
              </div>
            </CardContent>
          </Card>

          {/* 数据设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                数据设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="defaultBatchSize">默认批量大小</Label>
                <Input
                  id="defaultBatchSize"
                  type="number"
                  min="1"
                  max="99"
                  value={settings.defaultBatchSize}
                  onChange={(e) => setSettings({...settings, defaultBatchSize: parseInt(e.target.value)})}
                />
              </div>
              
              <div>
                <Label htmlFor="maxBatchSize">最大批量大小</Label>
                <Input
                  id="maxBatchSize"
                  type="number"
                  min="1"
                  max="999"
                  value={settings.maxBatchSize}
                  onChange={(e) => setSettings({...settings, maxBatchSize: parseInt(e.target.value)})}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoSaveData"
                  checked={settings.autoSaveData}
                  onCheckedChange={(checked) => setSettings({...settings, autoSaveData: checked as boolean})}
                />
                <Label htmlFor="autoSaveData">自动保存数据</Label>
              </div>
            </CardContent>
          </Card>

          {/* 系统信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                系统信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>系统版本</Label>
                  <Badge variant="outline">{systemInfo.version}</Badge>
                </div>
                <div>
                  <Label>数据路径</Label>
                  <Badge variant="secondary">{systemInfo.dataPath}</Badge>
                </div>
                <div>
                  <Label>数据大小</Label>
                  <Badge variant="outline">{systemInfo.totalSize}</Badge>
                </div>
                <div>
                  <Label>最后备份</Label>
                  <Badge variant="secondary">
                    {systemInfo.lastBackup || '未备份'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 数据管理 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trash2 className="h-5 w-5" />
                数据管理
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium mb-1">危险操作</p>
                    <p className="text-xs">以下操作将永久删除数据，请谨慎操作</p>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => clearData('emails')}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  清理邮箱
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => clearData('messages')}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  清理消息
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => clearData('generated')}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  清理生成数据
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => clearData('all')}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  清理所有数据
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 保存按钮 */}
        <div className="mt-6 flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={loadSettings}
            disabled={isLoading}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            重置
          </Button>
          <Button
            onClick={saveSettings}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                保存中...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                保存设置
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
