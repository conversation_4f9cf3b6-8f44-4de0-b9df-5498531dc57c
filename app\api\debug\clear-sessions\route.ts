import { NextRequest, NextResponse } from 'next/server'
import { getActiveSessions } from '@/lib/session-manager'

/**
 * POST /api/debug/clear-sessions
 * 调试接口：清理所有活跃会话
 */
export async function POST(request: NextRequest) {
  try {
    // 获取所有活跃会话
    const sessions = getActiveSessions()
    
    // 手动清理所有会话
    const { removeActiveSession } = await import('@/lib/session-manager')
    let removedCount = 0
    
    for (const session of sessions) {
      const removed = removeActiveSession(session.sessionId)
      if (removed) {
        removedCount++
      }
    }

    return NextResponse.json({
      success: true,
      message: `清理了 ${removedCount} 个活跃会话`,
      data: {
        removedCount,
        totalSessions: sessions.length
      }
    })
  } catch (error) {
    console.error('清理会话失败:', error)
    return NextResponse.json({
      success: false,
      error: '清理会话失败'
    }, { status: 500 })
  }
}
