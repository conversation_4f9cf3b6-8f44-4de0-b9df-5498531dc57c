/**
 * 邮箱生成服务
 * 基于project2中的EmailGenerator功能提取而来
 * 支持生成临时邮箱地址
 */

interface EmailGeneratorConfig {
  domain?: string
  namesList?: string[]
}

interface GeneratedEmail {
  email: string
  password: string
  firstName: string
  lastName: string
  timestamp: number
}

export class EmailGenerator {
  private domain: string
  private namesList: string[]

  constructor(config: EmailGeneratorConfig = {}) {
    // 从环境变量读取域名，如果没有则使用默认域名
    this.domain = config.domain || process.env.NEXT_PUBLIC_TEMP_MAIL_DOMAIN || this.getRandomDomain()

    // 默认名字列表（从project2的names-dataset.txt提取的常见英文名）
    this.namesList = config.namesList || this.getDefaultNames()
  }

  /**
   * 生成随机邮箱地址
   */
  generateEmail(length: number = 6): GeneratedEmail {
    const firstName = this.generateRandomName()
    const lastName = this.generateRandomName()

    // 生成更复杂的随机前缀
    const randomPrefix = this.generateRandomPrefix()

    // 使用时间戳 + 随机数 + 随机字符组合
    const timestamp = Date.now()
    const randomNum = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
    const randomChars = this.generateRandomString(3)
    const timestampSuffix = timestamp.toString().slice(-length)

    // 多种前缀组合方式，随机选择
    const prefixOptions = [
      `${firstName.toLowerCase()}${randomChars}${timestampSuffix}`,
      `${firstName.toLowerCase()}${lastName.toLowerCase()}${randomNum}`,
      `${randomPrefix}${timestampSuffix}${randomChars}`,
      `${firstName.toLowerCase()}${randomNum}${randomChars}`,
      `${randomPrefix}${firstName.toLowerCase()}${randomNum}`,
      `${lastName.toLowerCase()}${randomChars}${timestampSuffix}`
    ]

    const selectedPrefix = prefixOptions[Math.floor(Math.random() * prefixOptions.length)]

    // 生成邮箱地址
    const email = `${selectedPrefix}@${this.domain}`

    return {
      email,
      password: this.generateRandomPassword(),
      firstName,
      lastName,
      timestamp
    }
  }

  /**
   * 创建真正的临时邮箱（使用tempmail.plus）
   */
  async createRealTempMail(): Promise<GeneratedEmail> {
    try {
      // 使用tempmail.plus创建真实临时邮箱
      const tempmailResponse = await fetch('/api/create-tempmail', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (tempmailResponse.ok) {
        const tempmailResult = await tempmailResponse.json()

        if (tempmailResult.success) {
          const account = tempmailResult.data
          return {
            email: account.email,
            password: account.password,
            firstName: account.username || this.generateRandomName(),
            lastName: this.generateRandomName(),
            timestamp: account.created
          }
        }
      }

      throw new Error('tempmail.plus 服务不可用')

    } catch (error) {
      console.error('创建真实临时邮箱失败:', error)
      // 不再回退到本地生成，直接抛出错误
      throw new Error('无法创建真实邮箱，请检查网络连接')
    }
  }

  /**
   * 批量生成邮箱地址
   */
  generateBatchEmails(count: number): GeneratedEmail[] {
    const emails: GeneratedEmail[] = []
    const usedPrefixes = new Set<string>() // 防止邮箱重复
    const usedPasswords = new Set<string>() // 防止密码重复

    for (let i = 0; i < count; i++) {
      let attempts = 0
      let email: GeneratedEmail

      // 最多尝试10次生成唯一邮箱和密码
      do {
        // 为每个邮箱生成不同的时间戳，避免重复
        const baseTimestamp = Date.now()
        const uniqueTimestamp = baseTimestamp + i + (attempts * 1000)

        const firstName = this.generateRandomName()
        const lastName = this.generateRandomName()
        const randomPrefix = this.generateRandomPrefix()
        const randomNum = Math.floor(Math.random() * 99999).toString().padStart(5, '0')
        const randomChars = this.generateRandomString(4)
        const timestampSuffix = uniqueTimestamp.toString().slice(-6)

        // 更多样化的前缀生成
        const prefixOptions = [
          `${firstName.toLowerCase()}${randomChars}${timestampSuffix}`,
          `${firstName.toLowerCase()}${lastName.toLowerCase()}${randomNum}`,
          `${randomPrefix}${timestampSuffix}${randomChars}`,
          `${firstName.toLowerCase()}${randomNum}${randomChars}`,
          `${randomPrefix}${firstName.toLowerCase()}${randomNum}`,
          `${lastName.toLowerCase()}${randomChars}${timestampSuffix}`,
          `${randomPrefix}${randomNum}${randomChars}`,
          `${firstName.toLowerCase()}${i}${randomChars}${timestampSuffix}`
        ]

        const selectedPrefix = prefixOptions[Math.floor(Math.random() * prefixOptions.length)]

        // 生成唯一密码
        let password: string
        let passwordAttempts = 0
        do {
          password = this.generateRandomPassword()
          passwordAttempts++
        } while (usedPasswords.has(password) && passwordAttempts < 5)

        email = {
          email: `${selectedPrefix}@${this.domain}`,
          password: password,
          firstName,
          lastName,
          timestamp: uniqueTimestamp
        }

        attempts++
      } while ((usedPrefixes.has(email.email.split('@')[0]) || usedPasswords.has(email.password)) && attempts < 10)

      usedPrefixes.add(email.email.split('@')[0])
      usedPasswords.add(email.password)
      emails.push(email)

      // 添加小延迟确保时间戳不同
      if (i < count - 1) {
        const start = Date.now()
        while (Date.now() - start < 2) {
          // 2ms延迟
        }
      }
    }

    return emails
  }

  /**
   * 生成随机名字
   */
  private generateRandomName(): string {
    return this.namesList[Math.floor(Math.random() * this.namesList.length)]
  }

  /**
   * 生成随机前缀
   */
  private generateRandomPrefix(): string {
    const prefixes = [
      'user', 'test', 'demo', 'temp', 'mail', 'email', 'account', 'profile',
      'member', 'guest', 'visitor', 'client', 'contact', 'info', 'hello',
      'welcome', 'new', 'fresh', 'cool', 'smart', 'quick', 'fast', 'easy',
      'simple', 'basic', 'pro', 'plus', 'premium', 'special', 'unique',
      'random', 'auto', 'gen', 'create', 'make', 'build', 'dev', 'tech'
    ]
    return prefixes[Math.floor(Math.random() * prefixes.length)]
  }

  /**
   * 生成随机字符串
   */
  private generateRandomString(length: number): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 生成随机密码
   */
  private generateRandomPassword(): string {
    // 多种密码模式，随机选择
    const passwordPatterns = [
      () => this.generatePatternPassword1(), // 字母+数字+符号
      () => this.generatePatternPassword2(), // 单词+数字+符号
      () => this.generatePatternPassword3(), // 大小写+数字
      () => this.generatePatternPassword4(), // 前缀+随机字符
      () => this.generatePatternPassword5()  // 混合模式
    ]

    const selectedPattern = passwordPatterns[Math.floor(Math.random() * passwordPatterns.length)]
    return selectedPattern()
  }

  /**
   * 密码模式1: 纯随机字符
   */
  private generatePatternPassword1(): string {
    const length = 8 + Math.floor(Math.random() * 5) // 8-12位
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
    let password = ''

    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length))
    }

    return password
  }

  /**
   * 密码模式2: 单词+数字+符号
   */
  private generatePatternPassword2(): string {
    const words = ['pass', 'user', 'mail', 'temp', 'test', 'demo', 'safe', 'key', 'code', 'auth']
    const word = words[Math.floor(Math.random() * words.length)]
    const number = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
    const symbols = '!@#$%^&*'
    const symbol = symbols[Math.floor(Math.random() * symbols.length)]

    // 随机组合顺序
    const combinations = [
      `${word}${number}${symbol}`,
      `${word.toUpperCase()}${number}${symbol}`,
      `${symbol}${word}${number}`,
      `${number}${word}${symbol}`,
      `${word}${symbol}${number}`
    ]

    return combinations[Math.floor(Math.random() * combinations.length)]
  }

  /**
   * 密码模式3: 大小写字母+数字
   */
  private generatePatternPassword3(): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'

    let password = ''
    const length = 8 + Math.floor(Math.random() * 4) // 8-11位

    // 确保包含大写、小写和数字
    password += lowercase[Math.floor(Math.random() * lowercase.length)]
    password += uppercase[Math.floor(Math.random() * uppercase.length)]
    password += numbers[Math.floor(Math.random() * numbers.length)]

    // 填充剩余位数
    const allChars = lowercase + uppercase + numbers
    for (let i = 3; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)]
    }

    // 打乱顺序
    return password.split('').sort(() => Math.random() - 0.5).join('')
  }

  /**
   * 密码模式4: 前缀+随机字符
   */
  private generatePatternPassword4(): string {
    const prefixes = ['usr', 'tmp', 'gen', 'pwd', 'key', 'sec', 'acc', 'mem']
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
    const randomPart = this.generateRandomString(6)
    const number = Math.floor(Math.random() * 999).toString().padStart(3, '0')

    return `${prefix}${randomPart}${number}`
  }

  /**
   * 密码模式5: 混合模式
   */
  private generatePatternPassword5(): string {
    const timestamp = Date.now().toString().slice(-4)
    const randomChars = this.generateRandomString(4)
    const name = this.generateRandomName().toLowerCase().slice(0, 4)
    const symbols = '!@#$%'
    const symbol = symbols[Math.floor(Math.random() * symbols.length)]

    const patterns = [
      `${name}${timestamp}${symbol}`,
      `${randomChars}${timestamp}${name}`,
      `${symbol}${name}${randomChars}${timestamp}`,
      `${timestamp}${name}${randomChars}`,
      `${name}${symbol}${randomChars}${timestamp}`
    ]

    return patterns[Math.floor(Math.random() * patterns.length)]
  }

  /**
   * 获取随机域名
   */
  private getRandomDomain(): string {
    const domains = [
      'tempmail.plus',
      '10minutemail.com',
      'guerrillamail.com',
      'mailinator.com',
      'temp-mail.org'
    ]
    
    return domains[Math.floor(Math.random() * domains.length)]
  }

  /**
   * 获取默认名字列表
   */
  private getDefaultNames(): string[] {
    return [
      // 男性名字 - 扩展版
      'John', 'James', 'Robert', 'Michael', 'William', 'David', 'Richard', 'Joseph',
      'Thomas', 'Christopher', 'Charles', 'Daniel', 'Matthew', 'Anthony', 'Mark', 'Donald',
      'Steven', 'Paul', 'Andrew', 'Joshua', 'Kenneth', 'Kevin', 'Brian', 'George',
      'Timothy', 'Ronald', 'Jason', 'Edward', 'Jeffrey', 'Ryan', 'Jacob', 'Gary',
      'Nicholas', 'Eric', 'Jonathan', 'Stephen', 'Larry', 'Justin', 'Scott', 'Brandon',
      'Benjamin', 'Samuel', 'Gregory', 'Alexander', 'Patrick', 'Frank', 'Raymond', 'Jack',
      'Dennis', 'Jerry', 'Tyler', 'Aaron', 'Jose', 'Henry', 'Adam', 'Douglas',
      'Nathan', 'Peter', 'Zachary', 'Kyle', 'Noah', 'Alan', 'Ethan', 'Jeremy',
      'Lionel', 'Mason', 'Lucas', 'Logan', 'Oliver', 'Liam', 'Elijah', 'Wayne',

      // 女性名字 - 扩展版
      'Mary', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth', 'Barbara', 'Susan', 'Jessica',
      'Sarah', 'Karen', 'Nancy', 'Lisa', 'Betty', 'Helen', 'Sandra', 'Donna',
      'Carol', 'Ruth', 'Sharon', 'Michelle', 'Laura', 'Kimberly', 'Deborah',
      'Dorothy', 'Amy', 'Angela', 'Ashley', 'Brenda', 'Emma', 'Olivia', 'Cynthia',
      'Marie', 'Janet', 'Catherine', 'Frances', 'Christine', 'Samantha', 'Debra', 'Rachel',
      'Carolyn', 'Janet', 'Virginia', 'Maria', 'Heather', 'Diane', 'Julie', 'Joyce',
      'Victoria', 'Kelly', 'Christina', 'Joan', 'Evelyn', 'Lauren', 'Judith', 'Megan',
      'Cheryl', 'Andrea', 'Hannah', 'Jacqueline', 'Martha', 'Gloria', 'Teresa', 'Sara',
      'Janice', 'Marie', 'Julia', 'Kathryn', 'Frances', 'Jean', 'Alice', 'Judy',
      'Sophia', 'Isabella', 'Charlotte', 'Amelia', 'Mia', 'Harper', 'Evelyn', 'Abigail',
      
      // 现代流行名字
      'Emma', 'Olivia', 'Ava', 'Isabella', 'Sophia', 'Charlotte', 'Mia', 'Amelia',
      'Harper', 'Evelyn', 'Abigail', 'Emily', 'Elizabeth', 'Mila', 'Ella', 'Avery',
      'Liam', 'Noah', 'Oliver', 'Elijah', 'William', 'James', 'Benjamin', 'Lucas',
      'Henry', 'Alexander', 'Mason', 'Michael', 'Ethan', 'Daniel', 'Jacob', 'Logan',
      
      // 姓氏
      'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
      'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas',
      'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White',
      'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young'
    ]
  }

  /**
   * 验证邮箱格式
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * 获取邮箱域名
   */
  static getEmailDomain(email: string): string {
    return email.split('@')[1] || ''
  }

  /**
   * 获取邮箱用户名部分
   */
  static getEmailUsername(email: string): string {
    return email.split('@')[0] || ''
  }
}

// 默认导出一个实例
export const defaultEmailGenerator = new EmailGenerator()

// 便捷函数
export const generateEmail = () => defaultEmailGenerator.generateEmail()
export const generateBatchEmails = (count: number) => defaultEmailGenerator.generateBatchEmails(count)

// 类型导出
export type { GeneratedEmail, EmailGeneratorConfig }
