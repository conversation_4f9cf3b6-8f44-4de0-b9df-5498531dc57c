#!/bin/bash

# 美国地址生成器 Docker 部署脚本
# 使用方法: ./scripts/deploy.sh [环境] [选项]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "美国地址生成器 Docker 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  dev         开发环境部署"
    echo "  prod        生产环境部署"
    echo ""
    echo "选项:"
    echo "  --build     强制重新构建镜像"
    echo "  --no-cache  构建时不使用缓存"
    echo "  --nginx     同时启动 Nginx 反向代理"
    echo "  --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev                    # 开发环境部署"
    echo "  $0 prod --build          # 生产环境部署并重新构建"
    echo "  $0 prod --nginx          # 生产环境部署并启动 Nginx"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查环境变量文件
check_env_file() {
    local env_file=$1
    
    if [ ! -f "$env_file" ]; then
        log_error "环境变量文件 $env_file 不存在"
        log_info "请复制 .env.local.example 为 $env_file 并配置相应的环境变量"
        exit 1
    fi
    
    # 检查必需的环境变量
    local required_vars=("NEXT_PUBLIC_SUPABASE_URL" "NEXT_PUBLIC_SUPABASE_ANON_KEY" "ADMIN_USERNAME" "ADMIN_PASSWORD" "JWT_SECRET")
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" "$env_file" || grep -q "^${var}=your_" "$env_file"; then
            log_error "环境变量 $var 未正确配置在 $env_file 中"
            exit 1
        fi
    done
    
    log_success "环境变量文件检查通过"
}

# 构建 Docker 镜像
build_image() {
    local no_cache=$1
    
    log_info "构建 Docker 镜像..."
    
    local build_args=""
    if [ "$no_cache" = true ]; then
        build_args="--no-cache"
    fi
    
    docker build $build_args -t us-fake-gen-ui:latest .
    
    log_success "Docker 镜像构建完成"
}

# 部署应用
deploy_app() {
    local env=$1
    local env_file=$2
    local with_nginx=$3
    
    log_info "部署应用 (环境: $env)..."
    
    # 设置 Docker Compose 文件
    local compose_cmd="docker-compose"
    if docker compose version &> /dev/null; then
        compose_cmd="docker compose"
    fi
    
    # 设置环境变量文件
    export COMPOSE_FILE="docker-compose.yml"
    if [ -f "$env_file" ]; then
        export $(grep -v '^#' "$env_file" | xargs)
    fi
    
    # 停止现有容器
    log_info "停止现有容器..."
    $compose_cmd down
    
    # 启动服务
    local services="app"
    if [ "$with_nginx" = true ]; then
        services="app nginx"
        export COMPOSE_PROFILES="with-nginx"
    fi
    
    log_info "启动服务: $services"
    $compose_cmd up -d $services
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if $compose_cmd ps | grep -q "Up"; then
        log_success "应用部署成功"
        
        # 显示访问信息
        echo ""
        log_info "应用访问信息:"
        if [ "$with_nginx" = true ]; then
            echo "  HTTP:  http://localhost"
            echo "  HTTPS: https://localhost (需要配置 SSL 证书)"
        else
            echo "  应用:  http://localhost:3000"
        fi
        echo "  健康检查: http://localhost:3000/api/health"
        
    else
        log_error "应用部署失败"
        $compose_cmd logs
        exit 1
    fi
}

# 主函数
main() {
    local env="dev"
    local force_build=false
    local no_cache=false
    local with_nginx=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            dev|prod)
                env=$1
                shift
                ;;
            --build)
                force_build=true
                shift
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            --nginx)
                with_nginx=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置环境变量文件
    local env_file=".env.local"
    if [ "$env" = "prod" ]; then
        env_file=".env.production"
    fi
    
    log_info "开始部署 (环境: $env)"
    
    # 执行部署步骤
    check_dependencies
    check_env_file "$env_file"
    
    if [ "$force_build" = true ]; then
        build_image "$no_cache"
    fi
    
    deploy_app "$env" "$env_file" "$with_nginx"
    
    log_success "部署完成!"
}

# 运行主函数
main "$@"
