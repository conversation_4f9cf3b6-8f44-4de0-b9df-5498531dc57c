"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { generateEmail } from "@/lib/email-generator"
import { useToast } from "@/hooks/use-toast"
import { Copy, Check, Mail, Key, User, Clock, RefreshCw, Settings, Search, Trash2 } from "lucide-react"
import Link from "next/link"

export default function EmailTestPage() {
  const [generatedEmail, setGeneratedEmail] = useState<any>(null)
  const [testEmail, setTestEmail] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)
  const [copiedField, setCopiedField] = useState<string | null>(null)
  const [emailConfig, setEmailConfig] = useState<any>(null)
  const [imapEmails, setImapEmails] = useState<any[]>([])
  const [isLoadingImapEmails, setIsLoadingImapEmails] = useState(false)
  const [isRefreshingEmails, setIsRefreshingEmails] = useState(false)
  const [inbox, setInbox] = useState<any[]>([])
  const { toast } = useToast()

  useEffect(() => {
    fetchEmailConfig()
  }, [])

  const fetchEmailConfig = async () => {
    try {
      const response = await fetch('/api/email-service')
      const result = await response.json()
      if (result.success) {
        setEmailConfig(result.data)
      }
    } catch (error) {
      console.error('获取邮件配置失败:', error)
    }
  }

  const generateNewEmail = async () => {
    setIsGenerating(true)
    try {
      const response = await fetch('/api/email-service', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'create' })
      })

      const result = await response.json()

      if (result.success) {
        const account = result.data
        const email = {
          email: account.email,
          password: account.password,
          type: account.type,
          config: account.config,
          timestamp: Date.now()
        }
        setGeneratedEmail(email)
        setTestEmail(email.email)
        toast({
          title: `${account.type === 'imap' ? 'IMAP' : 'Tempmail'}邮箱创建成功`,
          description: `邮箱: ${email.email}`,
        })
      } else {
        throw new Error(result.error || '创建失败')
      }
    } catch (error) {
      console.error('创建邮箱失败，使用本地生成:', error)
      const email = generateEmail()
      setGeneratedEmail(email)
      setTestEmail(email.email)
      toast({
        title: "本地邮箱生成成功",
        description: `邮箱: ${email.email} (本地生成)`,
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const testEmailReception = async () => {
    if (!testEmail) {
      toast({
        title: "测试失败",
        description: "请先生成或输入邮箱地址",
        variant: "destructive",
      })
      return
    }

    setIsTesting(true)
    setTestResult(null)

    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: testEmail }),
      })

      const result = await response.json()

      if (result.success) {
        setTestResult(result.data)
        toast({
          title: "测试完成",
          description: result.data.canReceive ? "邮箱可以正常接收邮件" : "邮箱无法接收邮件",
          variant: result.data.canReceive ? "default" : "destructive",
        })
      } else {
        throw new Error(result.error || '测试失败')
      }
    } catch (error) {
      toast({
        title: "测试失败",
        description: error instanceof Error ? error.message : "测试邮箱接收功能时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsTesting(false)
    }
  }

  const loadImapEmails = async () => {
    if (!testEmail) {
      toast({
        title: "加载失败",
        description: "请先生成或输入邮箱地址",
        variant: "destructive",
      })
      return
    }

    setIsLoadingImapEmails(true)

    try {
      const response = await fetch(`/api/imap-emails?tempEmail=${encodeURIComponent(testEmail)}`)
      const result = await response.json()

      if (result.success) {
        setImapEmails(result.data.emails)
        toast({
          title: "邮件加载成功",
          description: `从IMAP服务器找到 ${result.data.emails.length} 封相关邮件`,
        })
      } else {
        throw new Error(result.error || '加载IMAP邮件失败')
      }
    } catch (error) {
      toast({
        title: "加载邮件失败",
        description: error instanceof Error ? error.message : "加载邮件时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsLoadingImapEmails(false)
    }
  }

  const refreshImapEmails = async () => {
    if (!testEmail) {
      toast({
        title: "刷新失败",
        description: "请先生成或输入邮箱地址",
        variant: "destructive",
      })
      return
    }

    setIsRefreshingEmails(true)

    try {
      const response = await fetch('/api/imap-emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tempEmail: testEmail }),
      })
      const result = await response.json()

      if (result.success) {
        setImapEmails(result.data.emails)
        toast({
          title: "邮件刷新成功",
          description: `从IMAP服务器刷新到 ${result.data.emails.length} 封邮件`,
        })
      } else {
        throw new Error(result.error || '刷新IMAP邮件失败')
      }
    } catch (error) {
      toast({
        title: "刷新邮件失败",
        description: error instanceof Error ? error.message : "刷新邮件时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsRefreshingEmails(false)
    }
  }

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
      toast({
        title: "复制成功",
        description: `已复制 ${field}`,
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            临时邮箱 + IMAP转发系统
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            基于project2架构：创建临时邮箱 → 邮件转发 → IMAP获取验证码
          </p>
          <div className="flex justify-center gap-4">
            <Link href="/">
              <Button variant="outline">
                ← 返回主页
              </Button>
            </Link>
            {emailConfig && (
              <Badge variant="secondary" className="px-4 py-2">
                <Settings className="mr-2 h-4 w-4" />
                {emailConfig.mode}
              </Badge>
            )}
          </div>
        </div>

        {emailConfig && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                邮件转发系统配置
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm text-gray-600">工作模式</div>
                    <Badge variant="default">
                      {emailConfig.mode}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm text-gray-600">临时邮箱域名</div>
                    <div className="font-medium">{emailConfig.tempDomain}</div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm text-gray-600">IMAP服务器</div>
                    <div className="font-medium">{emailConfig.imapServer}:{emailConfig.imapPort}</div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm text-gray-600">IMAP用户</div>
                    <div className="font-medium">{emailConfig.imapUser}</div>
                  </div>
                </div>

                {emailConfig.workflow && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm font-medium text-blue-800 mb-2">工作流程:</div>
                    <div className="space-y-1">
                      {emailConfig.workflow.map((step: string, index: number) => (
                        <div key={index} className="text-sm text-blue-700">{step}</div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              邮箱生成和接收测试
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-4">
              <Button 
                onClick={generateNewEmail} 
                disabled={isGenerating}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Mail className="mr-2 h-4 w-4" />
                    生成新邮箱
                  </>
                )}
              </Button>
            </div>

            {generatedEmail && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="text-sm text-gray-600 mb-1">邮箱地址</div>
                      <div className="font-medium text-gray-900">{generatedEmail.email}</div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedEmail.email, "邮箱地址")}
                      className="ml-2 h-8 w-8 p-0"
                    >
                      {copiedField === "邮箱地址" ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="text-sm text-gray-600 mb-1">密码</div>
                      <div className="font-medium text-gray-900">{generatedEmail.password}</div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedEmail.password, "密码")}
                      className="ml-2 h-8 w-8 p-0"
                    >
                      {copiedField === "密码" ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  生成时间: {new Date(generatedEmail.timestamp).toLocaleString('zh-CN')}
                </div>
              </div>
            )}

            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center gap-2 mb-4">
                <RefreshCw className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">邮箱接收测试</h3>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    测试邮箱地址
                  </label>
                  <Input
                    type="email"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    placeholder="输入要测试的邮箱地址"
                    className="mb-4"
                  />
                </div>

                <div className="flex gap-2 flex-wrap">
                  <Button
                    onClick={testEmailReception}
                    disabled={isTesting || !testEmail}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isTesting ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        测试中...
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 h-4 w-4" />
                        测试邮箱接收
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={loadImapEmails}
                    disabled={isLoadingImapEmails || !testEmail}
                    variant="outline"
                    className="border-blue-300 text-blue-600 hover:bg-blue-50"
                  >
                    {isLoadingImapEmails ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        加载中...
                      </>
                    ) : (
                      <>
                        <Search className="mr-2 h-4 w-4" />
                        查看IMAP邮件
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={refreshImapEmails}
                    disabled={isRefreshingEmails || !testEmail}
                    variant="outline"
                    className="border-orange-300 text-orange-600 hover:bg-orange-50"
                  >
                    {isRefreshingEmails ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        刷新中...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        刷新邮件
                      </>
                    )}
                  </Button>
                </div>

                {testResult && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-3">测试结果</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">邮箱地址:</span>
                        <span className="font-medium">{testResult.email}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">域名:</span>
                        <span className="font-medium">{testResult.domain}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">状态:</span>
                        <Badge variant={testResult.canReceive ? "default" : "destructive"}>
                          {testResult.canReceive ? "可接收" : "无法接收"}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">服务商:</span>
                        <span className="font-medium">{testResult.provider}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">测试时间:</span>
                        <span className="font-medium">
                          {new Date(testResult.lastChecked).toLocaleString('zh-CN')}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {imapEmails.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-blue-600" />
                IMAP邮件显示框 ({imapEmails.length} 封邮件)
                <Badge variant="secondary" className="ml-2">
                  直接从IMAP获取
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600 mt-2">
                以下邮件直接从IMAP服务器获取，不经过数据库存储
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {imapEmails.map((email, index) => (
                  <div key={email.id || index} className="border border-blue-200 rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-blue-100">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <Mail className="h-4 w-4 text-blue-600 flex-shrink-0" />
                            <h3 className="font-semibold text-gray-900 truncate">
                              {email.subject || '无主题'}
                            </h3>
                            <Badge variant="outline" className="text-xs border-blue-300 text-blue-600">
                              IMAP
                            </Badge>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div className="flex items-center gap-2">
                              <span className="text-gray-500 font-medium">发件人:</span>
                              <span className="text-gray-700 truncate">{email.from}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-gray-500 font-medium">收件人:</span>
                              <span className="text-gray-700 truncate">{email.to}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-gray-500 font-medium">时间:</span>
                              <span className="text-gray-700">{new Date(email.date).toLocaleString('zh-CN')}</span>
                            </div>
                            {email.isForwarded && email.originalTo && (
                              <div className="flex items-center gap-2">
                                <span className="text-orange-600 font-medium">📧 转发:</span>
                                <span className="text-orange-700 truncate">{email.originalTo}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    {(email.text || email.content) && (
                      <div className="p-6">
                        <div className="mb-4">
                          <div className="flex items-center gap-2 mb-3">
                            <div className="w-1 h-4 bg-blue-500 rounded-full"></div>
                            <span className="text-sm font-semibold text-gray-700">邮件内容 (IMAP直接获取)</span>
                          </div>

                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="text-gray-800 whitespace-pre-wrap leading-relaxed">
                              {email.text || email.content}
                            </div>
                          </div>
                        </div>

                        <div className="border-t border-gray-100 pt-4">
                          <div className="flex items-center gap-2 mb-3">
                            <div className="w-1 h-4 bg-green-500 rounded-full"></div>
                            <span className="text-sm font-semibold text-gray-700">内容分析</span>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div className="bg-blue-50 rounded-lg p-3 text-center">
                              <div className="text-lg font-bold text-blue-600">{(email.text || email.content || '').length}</div>
                              <div className="text-xs text-blue-500">字符数</div>
                            </div>
                            <div className="bg-green-50 rounded-lg p-3 text-center">
                              <div className="text-lg font-bold text-green-600">{(email.text || email.content || '').split('\n').length}</div>
                              <div className="text-xs text-green-500">行数</div>
                            </div>
                            <div className="bg-purple-50 rounded-lg p-3 text-center">
                              <div className="text-lg font-bold text-purple-600">{(email.text || email.content || '').split(' ').length}</div>
                              <div className="text-xs text-purple-500">单词数</div>
                            </div>
                            <div className="bg-orange-50 rounded-lg p-3 text-center">
                              <div className="text-lg font-bold text-orange-600">{/\d/.test(email.text || email.content || '') ? '是' : '否'}</div>
                              <div className="text-xs text-orange-500">包含数字</div>
                            </div>
                          </div>

                          {(() => {
                            const emailText = email.text || email.content || ''
                            const codeMatch = emailText.match(/(?<![a-zA-Z@.])\b\d{6}\b/)
                            if (codeMatch) {
                              return (
                                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                                  <div className="flex items-center gap-2 mb-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span className="text-sm font-semibold text-green-800">验证码检测 (IMAP)</span>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <div className="text-green-700 text-sm mb-1">✅ 发现6位验证码</div>
                                      <div className="text-2xl font-bold text-green-600 font-mono tracking-wider">
                                        {codeMatch[0]}
                                      </div>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => copyToClipboard(codeMatch[0], "验证码")}
                                      className="h-8 w-8 p-0 hover:bg-green-100"
                                    >
                                      {copiedField === "验证码" ?
                                        <Check className="h-4 w-4 text-green-600" /> :
                                        <Copy className="h-4 w-4 text-green-600" />
                                      }
                                    </Button>
                                  </div>
                                </div>
                              )
                            }

                            const numberMatches = emailText.match(/\d+/g)
                            if (numberMatches && numberMatches.length > 0) {
                              return (
                                <div className="bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg p-4">
                                  <div className="flex items-center gap-2 mb-2">
                                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                    <span className="text-sm font-semibold text-yellow-800">数字内容检测 (IMAP)</span>
                                  </div>
                                  <div className="text-yellow-700 text-sm mb-2">⚠️ 发现数字内容，但非标准验证码格式</div>
                                  <div className="flex flex-wrap gap-2">
                                    {numberMatches.slice(0, 5).map((num, idx) => (
                                      <span key={idx} className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-mono">
                                        {num}
                                      </span>
                                    ))}
                                    {numberMatches.length > 5 && (
                                      <span className="text-yellow-600 text-xs">+{numberMatches.length - 5} 更多</span>
                                    )}
                                  </div>
                                </div>
                              )
                            }

                            return (
                              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                  <span className="text-sm font-semibold text-blue-800">内容类型 (IMAP)</span>
                                </div>
                                <div className="text-blue-700 text-sm">📝 普通文本邮件，未检测到验证码</div>
                              </div>
                            )
                          })()}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
