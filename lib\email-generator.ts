/**
 * 邮箱生成服务
 * 基于project2中的EmailGenerator功能提取而来
 * 支持生成临时邮箱地址
 */

interface EmailGeneratorConfig {
  domain?: string
  namesList?: string[]
}

interface GeneratedEmail {
  email: string
  password: string
  firstName: string
  lastName: string
  timestamp: number
}

export class EmailGenerator {
  private domain: string
  private namesList: string[]
  private defaultPassword: string

  constructor(config: EmailGeneratorConfig = {}) {
    // 从环境变量读取域名，如果没有则使用默认域名
    this.domain = config.domain || process.env.NEXT_PUBLIC_TEMP_MAIL_DOMAIN || this.getRandomDomain()

    // 默认名字列表（从project2的names-dataset.txt提取的常见英文名）
    this.namesList = config.namesList || this.getDefaultNames()

    // 生成随机密码
    this.defaultPassword = this.generateRandomPassword()
  }

  /**
   * 生成随机邮箱地址
   */
  generateEmail(length: number = 4): GeneratedEmail {
    const firstName = this.generateRandomName()
    const lastName = this.generateRandomName()

    // 使用时间戳的后几位作为唯一标识
    const timestamp = Date.now()
    const timestampSuffix = timestamp.toString().slice(-length)

    // 生成邮箱地址
    const email = `${firstName.toLowerCase()}${timestampSuffix}@${this.domain}`

    return {
      email,
      password: this.defaultPassword,
      firstName,
      lastName,
      timestamp
    }
  }

  /**
   * 创建真正的临时邮箱（使用tempmail.plus）
   */
  async createRealTempMail(): Promise<GeneratedEmail> {
    try {
      // 使用tempmail.plus创建真实临时邮箱
      const tempmailResponse = await fetch('/api/create-tempmail', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (tempmailResponse.ok) {
        const tempmailResult = await tempmailResponse.json()

        if (tempmailResult.success) {
          const account = tempmailResult.data
          return {
            email: account.email,
            password: account.password,
            firstName: account.username || this.generateRandomName(),
            lastName: this.generateRandomName(),
            timestamp: account.created
          }
        }
      }

      throw new Error('tempmail.plus 服务不可用')

    } catch (error) {
      console.error('创建真实临时邮箱失败:', error)
      // 不再回退到本地生成，直接抛出错误
      throw new Error('无法创建真实邮箱，请检查网络连接')
    }
  }

  /**
   * 批量生成邮箱地址
   */
  generateBatchEmails(count: number): GeneratedEmail[] {
    const emails: GeneratedEmail[] = []
    
    for (let i = 0; i < count; i++) {
      // 为每个邮箱生成不同的时间戳，避免重复
      setTimeout(() => {}, 1) // 确保时间戳不同
      emails.push(this.generateEmail())
    }
    
    return emails
  }

  /**
   * 生成随机名字
   */
  private generateRandomName(): string {
    return this.namesList[Math.floor(Math.random() * this.namesList.length)]
  }

  /**
   * 生成随机密码
   */
  private generateRandomPassword(length: number = 12): string {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
    let password = ''
    
    for (let i = 0; i < length; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    
    return password
  }

  /**
   * 获取随机域名
   */
  private getRandomDomain(): string {
    const domains = [
      'tempmail.plus',
      '10minutemail.com',
      'guerrillamail.com',
      'mailinator.com',
      'temp-mail.org'
    ]
    
    return domains[Math.floor(Math.random() * domains.length)]
  }

  /**
   * 获取默认名字列表
   */
  private getDefaultNames(): string[] {
    return [
      // 男性名字
      'John', 'James', 'Robert', 'Michael', 'William', 'David', 'Richard', 'Joseph',
      'Thomas', 'Christopher', 'Charles', 'Daniel', 'Matthew', 'Anthony', 'Mark', 'Donald',
      'Steven', 'Paul', 'Andrew', 'Joshua', 'Kenneth', 'Kevin', 'Brian', 'George',
      'Timothy', 'Ronald', 'Jason', 'Edward', 'Jeffrey', 'Ryan', 'Jacob', 'Gary',
      
      // 女性名字
      'Mary', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth', 'Barbara', 'Susan', 'Jessica',
      'Sarah', 'Karen', 'Nancy', 'Lisa', 'Betty', 'Helen', 'Sandra', 'Donna',
      'Carol', 'Ruth', 'Sharon', 'Michelle', 'Laura', 'Sarah', 'Kimberly', 'Deborah',
      'Dorothy', 'Lisa', 'Nancy', 'Karen', 'Betty', 'Helen', 'Sandra', 'Donna',
      
      // 现代流行名字
      'Emma', 'Olivia', 'Ava', 'Isabella', 'Sophia', 'Charlotte', 'Mia', 'Amelia',
      'Harper', 'Evelyn', 'Abigail', 'Emily', 'Elizabeth', 'Mila', 'Ella', 'Avery',
      'Liam', 'Noah', 'Oliver', 'Elijah', 'William', 'James', 'Benjamin', 'Lucas',
      'Henry', 'Alexander', 'Mason', 'Michael', 'Ethan', 'Daniel', 'Jacob', 'Logan',
      
      // 姓氏
      'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
      'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas',
      'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White',
      'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young'
    ]
  }

  /**
   * 验证邮箱格式
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * 获取邮箱域名
   */
  static getEmailDomain(email: string): string {
    return email.split('@')[1] || ''
  }

  /**
   * 获取邮箱用户名部分
   */
  static getEmailUsername(email: string): string {
    return email.split('@')[0] || ''
  }
}

// 默认导出一个实例
export const defaultEmailGenerator = new EmailGenerator()

// 便捷函数
export const generateEmail = () => defaultEmailGenerator.generateEmail()
export const generateBatchEmails = (count: number) => defaultEmailGenerator.generateBatchEmails(count)

// 类型导出
export type { GeneratedEmail, EmailGeneratorConfig }
