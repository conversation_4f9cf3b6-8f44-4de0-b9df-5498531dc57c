"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  Mail, 
  Calendar, 
  User, 
  Clock, 
  Shield, 
  Copy, 
  Check,
  Database,
  MessageSquare,
  AlertCircle,
  CheckCircle
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface EmailMessage {
  id: string
  from: string
  fromAddress: string
  to: string
  subject: string
  content: string
  textContent: string
  htmlContent: string
  date: string
  receivedAt: string
  isRead: boolean
  hasVerificationCode: boolean
  verificationCode: string | null
  createdAt: string
}

interface EmailRecord {
  id: string
  address: string
  type: string
  isActive: boolean
  createdAt: string
  config: any
}

interface PersonEmailsData {
  hasEmail: boolean
  email: string
  emailRecord?: EmailRecord
  messages?: EmailMessage[]
  totalMessages?: number
  verificationMessages?: number
  message: string
}

interface PersonEmailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  email: string | null
  personName: string
}

export default function PersonEmailsDialog({ 
  open, 
  onOpenChange, 
  email, 
  personName 
}: PersonEmailsDialogProps) {
  const [emailData, setEmailData] = useState<PersonEmailsData | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedMessage, setSelectedMessage] = useState<EmailMessage | null>(null)
  const [copiedField, setCopiedField] = useState<string>("")
  const { toast } = useToast()

  // 检查邮箱是否在数据库中
  const checkPersonEmails = async () => {
    if (!email) return

    setLoading(true)
    try {
      const response = await fetch(`/api/check-person-emails?email=${encodeURIComponent(email)}`)
      const result = await response.json()

      if (result.success) {
        setEmailData(result.data)
        if (result.data.hasEmail) {
          toast({
            title: "找到邮件记录",
            description: `${personName} 的邮箱在数据库中找到 ${result.data.totalMessages} 封邮件`,
          })
        } else {
          toast({
            title: "未找到邮件记录",
            description: `${personName} 的邮箱不在邮件数据库中`,
            variant: "destructive",
          })
        }
      } else {
        throw new Error(result.error || '检查邮箱失败')
      }
    } catch (error) {
      console.error('检查邮箱失败:', error)
      toast({
        title: "检查失败",
        description: error instanceof Error ? error.message : "检查邮箱时发生错误",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(fieldName)
      toast({
        title: "复制成功",
        description: `${fieldName} 已复制到剪贴板`,
      })
      setTimeout(() => setCopiedField(""), 2000)
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 当对话框打开时自动检查邮箱
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen)
    if (newOpen && email && !emailData) {
      checkPersonEmails()
    }
    if (!newOpen) {
      setEmailData(null)
      setSelectedMessage(null)
    }
  }

  if (!email) return null

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-900 flex items-center gap-2">
            <Mail className="h-5 w-5 text-blue-600" />
            {personName} 的邮件记录
          </DialogTitle>
          <div className="flex items-center text-sm text-gray-500 mt-2">
            <Database className="mr-2 h-4 w-4" />
            邮箱地址: {email}
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">正在检查邮件记录...</p>
              </div>
            </div>
          ) : emailData ? (
            emailData.hasEmail ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
                {/* 左侧：邮箱信息和邮件列表 */}
                <div className="space-y-4">
                  {/* 邮箱信息 */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        邮箱信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">邮箱地址:</span>
                          <div className="font-medium">{emailData.emailRecord?.address}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">邮箱类型:</span>
                          <Badge variant="outline" className="ml-2">
                            {emailData.emailRecord?.type}
                          </Badge>
                        </div>
                        <div>
                          <span className="text-gray-500">状态:</span>
                          <Badge 
                            variant={emailData.emailRecord?.isActive ? "default" : "secondary"}
                            className="ml-2"
                          >
                            {emailData.emailRecord?.isActive ? "活跃" : "非活跃"}
                          </Badge>
                        </div>
                        <div>
                          <span className="text-gray-500">创建时间:</span>
                          <div className="text-xs text-gray-600">
                            {emailData.emailRecord?.createdAt ? formatDate(emailData.emailRecord.createdAt) : '未知'}
                          </div>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div className="bg-blue-50 rounded-lg p-3">
                          <div className="text-2xl font-bold text-blue-600">{emailData.totalMessages}</div>
                          <div className="text-xs text-blue-500">总邮件数</div>
                        </div>
                        <div className="bg-green-50 rounded-lg p-3">
                          <div className="text-2xl font-bold text-green-600">{emailData.verificationMessages}</div>
                          <div className="text-xs text-green-500">验证码邮件</div>
                        </div>
                        <div className="bg-purple-50 rounded-lg p-3">
                          <div className="text-2xl font-bold text-purple-600">
                            {emailData.messages?.filter(msg => !msg.isRead).length || 0}
                          </div>
                          <div className="text-xs text-purple-500">未读邮件</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 邮件列表 */}
                  <Card className="flex-1">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <MessageSquare className="h-5 w-5 text-blue-600" />
                        邮件列表 ({emailData.messages?.length || 0})
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      <ScrollArea className="h-64">
                        <div className="space-y-2 p-4">
                          {emailData.messages?.map((message) => (
                            <div
                              key={message.id}
                              className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                                selectedMessage?.id === message.id 
                                  ? 'border-blue-500 bg-blue-50' 
                                  : 'border-gray-200 hover:bg-gray-50'
                              }`}
                              onClick={() => setSelectedMessage(message)}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <h4 className="font-medium text-sm truncate">
                                      {message.subject || '无主题'}
                                    </h4>
                                    {message.hasVerificationCode && (
                                      <Badge variant="secondary" className="text-xs">
                                        验证码
                                      </Badge>
                                    )}
                                    {!message.isRead && (
                                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                                    )}
                                  </div>
                                  <div className="text-xs text-gray-500 truncate">
                                    来自: {message.from}
                                  </div>
                                  <div className="text-xs text-gray-400">
                                    {formatDate(message.date)}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>

                {/* 右侧：邮件详情 */}
                <div>
                  {selectedMessage ? (
                    <Card className="h-full">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center justify-between">
                          <span className="truncate">{selectedMessage.subject || '无主题'}</span>
                          <div className="flex gap-1">
                            {selectedMessage.hasVerificationCode && (
                              <Badge variant="secondary">验证码</Badge>
                            )}
                            {!selectedMessage.isRead && (
                              <Badge variant="default">未读</Badge>
                            )}
                          </div>
                        </CardTitle>
                        <div className="text-sm text-gray-500 space-y-1">
                          <div>发件人: {selectedMessage.from}</div>
                          <div>收件人: {selectedMessage.to}</div>
                          <div>时间: {formatDate(selectedMessage.date)}</div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <ScrollArea className="h-96">
                          <div className="space-y-4">
                            {selectedMessage.hasVerificationCode && selectedMessage.verificationCode && (
                              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <div className="text-green-800 font-medium mb-1">验证码</div>
                                    <div className="text-2xl font-bold text-green-600 font-mono">
                                      {selectedMessage.verificationCode}
                                    </div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => copyToClipboard(selectedMessage.verificationCode!, "验证码")}
                                  >
                                    {copiedField === "验证码" ? 
                                      <Check className="h-4 w-4 text-green-600" /> : 
                                      <Copy className="h-4 w-4" />
                                    }
                                  </Button>
                                </div>
                              </div>
                            )}
                            
                            <div>
                              <h4 className="font-medium mb-2">邮件内容</h4>
                              <div className="bg-gray-50 border rounded-lg p-4">
                                <div className="whitespace-pre-wrap text-sm">
                                  {selectedMessage.textContent || selectedMessage.content || '无内容'}
                                </div>
                              </div>
                            </div>
                          </div>
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card className="h-full flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>选择一封邮件查看详情</p>
                      </div>
                    </Card>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">未找到邮件记录</h3>
                  <p className="text-gray-600">{emailData.message}</p>
                </div>
              </div>
            )
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">点击检查按钮查看邮件记录</p>
                <Button 
                  onClick={checkPersonEmails} 
                  className="mt-4"
                  disabled={loading}
                >
                  <Database className="mr-2 h-4 w-4" />
                  检查邮件记录
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
