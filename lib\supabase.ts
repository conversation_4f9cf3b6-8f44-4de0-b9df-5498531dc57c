import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 数据库表结构类型定义
export interface SavedPersonData {
  id: string
  created_at: string
  full_name: string
  first_name: string
  last_name: string
  gender: string
  birthday: string
  title: string
  hair_color: string
  country: string
  street: string
  city: string
  state: string
  state_full_name: string
  zip_code: string
  phone: string
  email: string
  full_address: string
  occupation: string
  company: string
  company_size: string
  industry: string
  status: string
  salary: string
  ssn: string
  card_type: string
  card_number: string
  cvv: number
  expiry: string
  username: string
  password: string
  height: string
  weight: string
  blood_type: string
  os: string
  guid: string
  user_agent: string
  education: string
  website: string
  security_question: string
  security_answer: string
  // 学校信息
  school_name?: string
  school_id?: string
  school_zip?: string
  school_website?: string
  school_address?: string
  school_city?: string
  school_state?: string
  school_phone?: string
  school_grades?: string
  // 大学信息
  university_name?: string
  university_id?: string
  university_zip?: string
  university_website?: string
  university_address?: string
  university_city?: string
  university_state?: string
  university_phone?: string
  university_type?: string
  // 生成参数
  generation_seed: number
  generation_params: string // JSON 字符串存储生成参数
}
