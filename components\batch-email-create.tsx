"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Mail, RefreshCw } from "lucide-react"

interface BatchEmailCreateProps {
  onBatchCreate: (count: number) => Promise<void>
  disabled?: boolean
}

export function BatchEmailCreate({ onBatchCreate, disabled = false }: BatchEmailCreateProps) {
  const [open, setOpen] = useState(false)
  const [count, setCount] = useState(5)
  const [isCreating, setIsCreating] = useState(false)

  const handleCreate = async () => {
    if (count < 1 || count > 20) {
      return
    }

    setIsCreating(true)
    try {
      await onBatchCreate(count)
      setOpen(false)
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          disabled={disabled}
          className="bg-blue-50 hover:bg-blue-100 border-blue-200"
        >
          <Plus className="h-4 w-4 mr-1" />
          批量创建
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-blue-600" />
            批量创建邮箱
          </DialogTitle>
          <DialogDescription>
            一次性创建多个临时邮箱，用于批量测试或数据生成。
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="count" className="text-right">
              数量
            </Label>
            <div className="col-span-3">
              <Input
                id="count"
                type="number"
                min="1"
                max="20"
                value={count}
                onChange={(e) => setCount(parseInt(e.target.value) || 1)}
                className="w-full"
                disabled={isCreating}
              />
              <p className="text-xs text-gray-500 mt-1">
                建议一次创建1-20个邮箱
              </p>
            </div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">注意事项：</p>
                <ul className="text-xs space-y-1">
                  <li>• 每个邮箱都是独立的临时邮箱</li>
                  <li>• 创建过程可能需要一些时间</li>
                  <li>• 建议分批创建，避免一次性创建过多</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isCreating}
          >
            取消
          </Button>
          <Button
            onClick={handleCreate}
            disabled={isCreating || count < 1 || count > 20}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isCreating ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                创建中...
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                创建 {count} 个邮箱
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
