#!/bin/bash

# Linux Docker构建问题修复脚本
# 专门解决npm ci失败的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示横幅
echo -e "${BLUE}"
echo "=========================================="
echo "  Linux Docker构建问题修复工具"
echo "  解决npm ci失败问题"
echo "=========================================="
echo -e "${NC}"

# 检查文件
log_info "检查项目文件..."

if [ ! -f "package.json" ]; then
    log_error "package.json文件不存在"
    exit 1
fi

if [ ! -f "pnpm-lock.yaml" ]; then
    log_error "pnpm-lock.yaml文件不存在"
    exit 1
fi

# 方案1: 生成package-lock.json
log_info "方案1: 生成package-lock.json文件..."

if [ ! -f "package-lock.json" ]; then
    log_info "正在生成package-lock.json..."
    npm install --package-lock-only
    
    if [ -f "package-lock.json" ]; then
        log_success "package-lock.json生成成功"
    else
        log_error "package-lock.json生成失败"
    fi
else
    log_info "package-lock.json已存在"
fi

# 方案2: 创建.npmrc配置
log_info "方案2: 创建.npmrc配置文件..."

cat > .npmrc << 'EOF'
# NPM配置优化
registry=https://registry.npmjs.org/
package-lock=true
shrinkwrap=false
save-exact=false
fund=false
audit=false
progress=false
loglevel=warn

# 解决依赖问题
legacy-peer-deps=true
strict-peer-deps=false

# 网络配置
fetch-retries=3
fetch-retry-factor=2
fetch-retry-mintimeout=10000
fetch-retry-maxtimeout=60000
EOF

log_success ".npmrc配置文件创建完成"

# 方案3: 创建优化的Dockerfile
log_info "方案3: 创建优化的Dockerfile..."

cat > Dockerfile.linux-fixed << 'EOF'
# Linux Docker构建修复版本
FROM node:20-alpine AS base

# 安装系统依赖
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    git \
    curl \
    ca-certificates

WORKDIR /app

# 依赖安装阶段
FROM base AS deps

# 复制依赖文件
COPY package.json package-lock.json* pnpm-lock.yaml* .npmrc* ./

# 设置npm配置
RUN npm config set registry https://registry.npmjs.org/ && \
    npm config set legacy-peer-deps true && \
    npm config set audit false && \
    npm config set fund false

# 尝试多种安装方式
RUN if [ -f "package-lock.json" ]; then \
        echo "使用npm ci安装..." && \
        npm ci --only=production --ignore-scripts --no-audit; \
    elif [ -f "pnpm-lock.yaml" ]; then \
        echo "使用pnpm安装..." && \
        corepack enable && \
        corepack prepare pnpm@latest --activate && \
        pnpm install --frozen-lockfile --prod --ignore-scripts; \
    else \
        echo "使用npm install安装..." && \
        npm install --only=production --ignore-scripts --no-audit; \
    fi

# 开发依赖安装阶段
FROM base AS dev-deps

# 复制依赖文件
COPY package.json package-lock.json* pnpm-lock.yaml* .npmrc* ./

# 设置npm配置
RUN npm config set registry https://registry.npmjs.org/ && \
    npm config set legacy-peer-deps true && \
    npm config set audit false && \
    npm config set fund false

# 安装所有依赖
RUN if [ -f "package-lock.json" ]; then \
        npm ci --ignore-scripts --no-audit; \
    elif [ -f "pnpm-lock.yaml" ]; then \
        corepack enable && \
        corepack prepare pnpm@latest --activate && \
        pnpm install --frozen-lockfile --ignore-scripts; \
    else \
        npm install --ignore-scripts --no-audit; \
    fi

# 构建阶段
FROM base AS builder

COPY --from=dev-deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

RUN npm run build

# 运行阶段
FROM base AS runner

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3002
ENV HOSTNAME="0.0.0.0"

RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

RUN mkdir -p /app/logs && chown -R nextjs:nodejs /app

USER nextjs

EXPOSE 3002

HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3002/api/health || exit 1

CMD ["npm", "start"]
EOF

log_success "优化的Dockerfile创建完成"

# 方案4: 创建修复版docker-compose
log_info "方案4: 创建修复版docker-compose..."

cat > docker-compose.linux-fixed.yml << 'EOF'
version: '3.8'

services:
  us-fake-gen-app:
    build:
      context: .
      dockerfile: Dockerfile.linux-fixed
    container_name: us-fake-gen-app-fixed
    restart: unless-stopped
    ports:
      - "3000:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - HOSTNAME=0.0.0.0
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - ADMIN_USERNAME=${ADMIN_USERNAME}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
EOF

log_success "修复版docker-compose创建完成"

# 方案5: 测试构建
log_info "方案5: 测试Docker构建..."

echo "正在测试构建，这可能需要几分钟..."

if docker build -f Dockerfile.linux-fixed -t us-fake-gen-ui:test . 2>/dev/null; then
    log_success "Docker构建测试成功!"
    
    echo
    echo "🎉 修复完成！现在可以使用以下命令部署："
    echo
    echo "# 使用修复版配置部署"
    echo "docker-compose -f docker-compose.linux-fixed.yml up -d"
    echo
    echo "# 或者使用修复版Dockerfile"
    echo "docker build -f Dockerfile.linux-fixed -t us-fake-gen-ui:latest ."
    echo "docker run -p 3000:3002 us-fake-gen-ui:latest"
    echo
    
else
    log_error "Docker构建仍然失败"
    echo
    echo "请尝试以下手动修复步骤："
    echo
    echo "1. 检查网络连接"
    echo "2. 清理Docker缓存: docker system prune -a"
    echo "3. 增加Docker内存限制"
    echo "4. 手动安装依赖: npm install"
    echo "5. 使用云平台部署 (Vercel, Railway等)"
    echo
fi

# 清理临时文件
log_info "清理临时文件..."
# 保留生成的有用文件，只清理临时文件

echo
echo "=========================================="
echo "修复脚本执行完成"
echo "=========================================="
echo
echo "生成的文件："
echo "- .npmrc (NPM配置优化)"
echo "- package-lock.json (NPM锁定文件)"
echo "- Dockerfile.linux-fixed (修复版Dockerfile)"
echo "- docker-compose.linux-fixed.yml (修复版compose)"
echo
echo "建议使用修复版配置进行部署！"
