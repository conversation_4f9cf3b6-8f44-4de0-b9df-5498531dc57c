/**
 * Token黑名单管理
 * 用于管理已登出或失效的JWT token
 */

// 黑名单token存储（简单的内存存储，生产环境应使用数据库或Redis）
const tokenBlacklist = new Set<string>()

// 存储token的过期时间，用于自动清理
const tokenExpiryMap = new Map<string, number>()

/**
 * 将token添加到黑名单
 */
export function blacklistToken(token: string, expiryTime?: number) {
  tokenBlacklist.add(token)
  
  // 如果提供了过期时间，记录下来用于自动清理
  if (expiryTime) {
    tokenExpiryMap.set(token, expiryTime)
  }
  
  // 设置自动清理（24小时后或token过期时间）
  const cleanupTime = expiryTime ? Math.max(0, expiryTime * 1000 - Date.now()) : 24 * 60 * 60 * 1000
  
  setTimeout(() => {
    tokenBlacklist.delete(token)
    tokenExpiryMap.delete(token)
  }, cleanupTime)
}

/**
 * 检查token是否在黑名单中
 */
export function isTokenBlacklisted(token: string): boolean {
  return tokenBlacklist.has(token)
}

/**
 * 从黑名单中移除token（一般不需要手动调用）
 */
export function removeFromBlacklist(token: string): boolean {
  const removed = tokenBlacklist.delete(token)
  tokenExpiryMap.delete(token)
  return removed
}

/**
 * 获取黑名单中的token数量
 */
export function getBlacklistSize(): number {
  return tokenBlacklist.size
}

/**
 * 清理过期的token（手动清理函数）
 */
export function cleanupExpiredTokens(): number {
  const now = Math.floor(Date.now() / 1000)
  let cleanedCount = 0
  
  for (const [token, expiryTime] of tokenExpiryMap.entries()) {
    if (expiryTime < now) {
      tokenBlacklist.delete(token)
      tokenExpiryMap.delete(token)
      cleanedCount++
    }
  }
  
  return cleanedCount
}

/**
 * 获取黑名单统计信息
 */
export function getBlacklistStats() {
  const now = Math.floor(Date.now() / 1000)
  let expiredCount = 0
  let activeCount = 0
  
  for (const expiryTime of tokenExpiryMap.values()) {
    if (expiryTime < now) {
      expiredCount++
    } else {
      activeCount++
    }
  }
  
  return {
    total: tokenBlacklist.size,
    active: activeCount,
    expired: expiredCount,
    withoutExpiry: tokenBlacklist.size - tokenExpiryMap.size
  }
}
