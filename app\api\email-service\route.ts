import { NextRequest, NextResponse } from 'next/server'

/**
 * 完整邮件服务API
 * 支持tempmail.plus、IMAP、POP3多种模式
 */

interface EmailConfig {
  tempMail: string
  domain: string
  imapServer?: string
  imapPort?: number
  imapUser?: string
  imapPass?: string
  imapDir?: string
  protocol?: 'IMAP' | 'POP3'
}

class ServerEmailService {
  private config: EmailConfig
  private useImap: boolean

  constructor() {
    this.config = {
      tempMail: process.env.TEMP_MAIL || 'null',
      domain: process.env.DOMAIN || 'mcpserver.sbs',
      imapServer: process.env.IMAP_SERVER,
      imapPort: parseInt(process.env.IMAP_PORT || '993'),
      imapUser: process.env.IMAP_USER,
      imapPass: process.env.IMAP_PASS,
      imapDir: process.env.IMAP_DIR || 'INBOX',
      protocol: (process.env.IMAP_PROTOCOL as 'IMAP' | 'POP3') || 'IMAP'
    }

    // 总是使用tempmail创建邮箱，验证码通过IMAP获取
    this.useImap = false
  }

  /**
   * 创建邮箱账户
   * 创建临时邮箱，邮件会转发到IMAP邮箱
   */
  async createEmailAccount() {
    // 创建临时邮箱（邮件会转发到IMAP邮箱）
    const username = this.generateRandomUsername()
    const email = `${username}@${this.config.domain}`
    const password = this.generateRandomPassword()

    return {
      email,
      password,
      type: 'tempmail-with-imap',
      config: {
        tempDomain: this.config.domain,
        imapServer: this.config.imapServer,
        imapPort: this.config.imapPort,
        imapUser: this.config.imapUser ? this.config.imapUser.replace(/(.{3}).*(@.*)/, '$1***$2') : undefined,
        protocol: this.config.protocol,
        description: '临时邮箱，邮件转发到IMAP邮箱获取验证码'
      }
    }
  }

  /**
   * 获取验证码
   * 从IMAP邮箱获取转发的邮件中的验证码
   */
  async getVerificationCode(email: string, maxRetries: number = 5) {
    console.log(`开始获取验证码流程，邮箱: ${email}，最大重试次数: ${maxRetries}`)

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        console.log(`尝试从IMAP邮箱获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`)
        console.log(`临时邮箱: ${email}`)
        console.log(`IMAP邮箱: ${this.config.imapUser}`)

        // 总是通过IMAP获取验证码（因为邮件会转发到IMAP邮箱）
        const code = await this.getCodeByImap(email)

        if (code) {
          console.log(`成功获取到验证码: ${code}`)
          return {
            code,
            message: `验证码获取成功 (从IMAP邮箱: ${this.config.imapUser})`,
            success: true,
            attempts: attempt + 1
          }
        }

        console.log(`第 ${attempt + 1} 次尝试未找到验证码`)
        if (attempt < maxRetries - 1) {
          console.log('等待5秒后重试...')
          // 等待5秒后重试（减少等待时间）
          await new Promise(resolve => setTimeout(resolve, 5000))
        }

      } catch (error) {
        console.error(`第 ${attempt + 1} 次获取验证码失败:`, error)
        if (attempt < maxRetries - 1) {
          console.log('等待5秒后重试...')
          await new Promise(resolve => setTimeout(resolve, 5000))
        }
      }
    }

    console.log(`所有尝试都失败了，返回失败结果`)
    return {
      code: null,
      message: `经过 ${maxRetries} 次尝试后仍未从IMAP邮箱获取到验证码`,
      success: false,
      attempts: maxRetries
    }
  }

  /**
   * 通过IMAP获取验证码
   * 从转发到IMAP邮箱的邮件中提取验证码
   */
  private async getCodeByImap(tempEmail: string): Promise<string | null> {
    try {
      console.log('从真实IMAP邮箱获取验证码')
      console.log(`临时邮箱: ${tempEmail}`)
      console.log(`IMAP服务器: ${this.config.imapServer}:${this.config.imapPort}`)
      console.log(`IMAP用户: ${this.config.imapUser}`)

      // 使用简化的IMAP实现
      const imapResult = await this.connectToIMAP(tempEmail)

      if (imapResult.code) {
        console.log(`从IMAP找到验证码: ${imapResult.code}`)
        return imapResult.code
      }

      console.log('IMAP中未找到验证码')
      return null
    } catch (error) {
      console.error('IMAP获取验证码失败:', error)
      return null
    }
  }

  /**
   * 真实的IMAP连接实现
   */
  private async connectToIMAP(tempEmail: string): Promise<{
    code: string | null
    emails: any[]
    message: string
  }> {
    try {
      console.log('尝试真实IMAP连接...')
      console.log(`搜索条件: TO "${tempEmail}"`)

      // 使用简单IMAP实现
      const { SimpleIMAP } = await import('@/lib/simple-imap')
      const imap = new SimpleIMAP()

      const result = await imap.getVerificationCode(tempEmail)

      if (result.code) {
        console.log(`从真实IMAP中提取到验证码: ${result.code}`)
      } else {
        console.log('真实IMAP中未找到验证码')
      }

      return result

    } catch (error) {
      console.error('真实IMAP连接失败:', error)

      // 不再使用模拟数据，直接返回错误
      return {
        code: null,
        emails: [],
        message: `IMAP连接失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 从邮件文本中提取验证码
   */
  private extractCodeFromEmailText(emailText: string, tempEmail: string): string | null {
    // 移除临时邮箱地址避免误识别
    let cleanText = emailText.replace(new RegExp(tempEmail, 'g'), '')

    // 查找6位数字验证码
    const codeMatch = cleanText.match(/(?<![a-zA-Z@.])\b\d{6}\b/)
    return codeMatch ? codeMatch[0] : null
  }

  /**
   * 通过POP3获取验证码（暂不支持）
   */
  private async getCodeByPop3(email: string): Promise<string | null> {
    console.log('POP3模式暂不支持，请使用IMAP模式')
    return null
  }

  /**
   * 通过tempmail.plus获取验证码
   */
  private async getCodeByTempmail(email: string): Promise<string | null> {
    try {
      const [username, domain] = email.split('@')
      const extension = `@${domain}`
      
      // 获取邮件列表
      const listUrl = `https://tempmail.plus/api/mails?email=${username}${extension}&limit=20`
      const listResponse = await fetch(listUrl)
      const listData = await listResponse.json()
      
      if (!listData.result || !listData.first_id) {
        return null
      }

      // 获取最新邮件内容
      const mailUrl = `https://tempmail.plus/api/mails/${listData.first_id}?email=${username}${extension}`
      const mailResponse = await fetch(mailUrl)
      const mailData = await mailResponse.json()
      
      if (!mailData.result) {
        return null
      }

      // 提取验证码
      const mailText = mailData.text || ''
      const mailSubject = mailData.subject || ''
      console.log(`找到邮件主题: ${mailSubject}`)
      
      // 避免域名中的数字被误识别为验证码
      const cleanText = mailText.replace(email, '')
      const codeMatch = cleanText.match(/(?<![a-zA-Z@.])\b\d{6}\b/)
      
      if (codeMatch) {
        // 删除邮件
        await this.deleteTempmail(email, listData.first_id)
        return codeMatch[0]
      }

      return null
    } catch (error) {
      console.error('Tempmail获取验证码失败:', error)
      return null
    }
  }

  /**
   * 删除tempmail邮件
   */
  private async deleteTempmail(email: string, mailId: string): Promise<boolean> {
    try {
      const [username, domain] = email.split('@')
      const extension = `@${domain}`
      
      const response = await fetch('https://tempmail.plus/api/mails/', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          email: `${username}${extension}`,
          first_id: mailId
        })
      })

      const data = await response.json()
      return data.result === true
    } catch (error) {
      console.error('删除tempmail邮件失败:', error)
      return false
    }
  }

  /**
   * 生成随机用户名
   */
  private generateRandomUsername(): string {
    const names = [
      'john', 'jane', 'mike', 'sarah', 'david', 'lisa', 'tom', 'mary',
      'james', 'anna', 'robert', 'emma', 'william', 'olivia', 'richard', 'sophia'
    ]
    
    const name = names[Math.floor(Math.random() * names.length)]
    const timestamp = Date.now().toString().slice(-6)
    
    return `${name}${timestamp}`
  }

  /**
   * 生成随机密码（至少12位，包含大小写字母、数字和特殊符号）
   */
  private generateRandomPassword(): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const specialChars = '!@#$%^&*'
    const allChars = lowercase + uppercase + numbers + specialChars

    let password = ''

    // 确保包含每种类型的字符
    password += lowercase[Math.floor(Math.random() * lowercase.length)]
    password += uppercase[Math.floor(Math.random() * uppercase.length)]
    password += numbers[Math.floor(Math.random() * numbers.length)]
    password += specialChars[Math.floor(Math.random() * specialChars.length)]

    // 填充剩余位数（至少8位，总共至少12位）
    const remainingLength = 8 + Math.floor(Math.random() * 4) // 8-11位
    for (let i = 0; i < remainingLength; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)]
    }

    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('')
  }

  /**
   * 获取配置信息
   */
  getConfig() {
    return {
      mode: 'Tempmail + IMAP',
      description: '临时邮箱 + IMAP转发模式',
      tempDomain: this.config.domain,
      imapServer: this.config.imapServer,
      imapPort: this.config.imapPort,
      imapUser: this.config.imapUser ? this.config.imapUser.replace(/(.{3}).*(@.*)/, '$1***$2') : undefined,
      protocol: this.config.protocol,
      tempMail: this.config.tempMail,
      workflow: [
        '1. 创建临时邮箱 (如: <EMAIL>)',
        '2. 邮件自动转发到IMAP邮箱',
        '3. 通过IMAP获取验证码'
      ]
    }
  }
}

const emailService = new ServerEmailService()

/**
 * 处理邮件服务请求
 */
export async function POST(request: NextRequest) {
  try {
    let body = {}
    try {
      const text = await request.text()
      if (text.trim()) {
        body = JSON.parse(text)
      }
    } catch (parseError) {
      console.error('JSON解析失败:', parseError)
      // 如果解析失败，使用默认值
      body = {}
    }

    const { action = 'create', email, maxRetries = 5 } = body as any

    // 处理不同的操作
    switch (action) {
      case 'create':
        // 创建邮箱账户
        const account = await emailService.createEmailAccount()

        return NextResponse.json({
          success: true,
          data: account
        })

      case 'refresh':
        // 刷新邮件 - 使用真实的邮件同步
        if (!email) {
          return NextResponse.json({
            success: false,
            error: '邮箱地址不能为空'
          }, { status: 400 })
        }

        console.log(`刷新邮箱: ${email}`)

        // 调用邮件同步API进行真实的邮件检查
        try {
          const syncResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3002'}/api/email-sync-simple`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              emailAddress: email,
              syncType: 'single'
            }),
          })

          const syncResult = await syncResponse.json()

          return NextResponse.json({
            success: syncResult.success,
            data: {
              newMessages: syncResult.data?.newMessages || 0,
              message: syncResult.success ? '邮件刷新完成' : '邮件刷新失败'
            },
            error: syncResult.error
          })
        } catch (error) {
          return NextResponse.json({
            success: false,
            data: {
              newMessages: 0,
              message: '邮件刷新失败'
            },
            error: error instanceof Error ? error.message : '刷新邮件时发生错误'
          })
        }

      default:
        // 默认创建邮箱（向后兼容）
        const defaultAccount = await emailService.createEmailAccount()

        return NextResponse.json({
          success: true,
          data: defaultAccount
        })
    }
  } catch (error) {
    console.error('邮件服务操作失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '邮件服务操作时发生未知错误'
    }, { status: 500 })
  }
}

/**
 * 获取验证码
 */
export async function PUT(request: NextRequest) {
  try {
    const { email, maxRetries = 5 } = await request.json()
    
    if (!email) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址不能为空'
      }, { status: 400 })
    }
    
    const result = await emailService.getVerificationCode(email, maxRetries)
    
    return NextResponse.json({
      success: result.success,
      data: result
    })
  } catch (error) {
    console.error('获取验证码失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取验证码时发生未知错误'
    }, { status: 500 })
  }
}

/**
 * 获取配置信息
 */
export async function GET() {
  try {
    const config = emailService.getConfig()
    
    return NextResponse.json({
      success: true,
      data: config
    })
  } catch (error) {
    console.error('获取配置失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取配置时发生未知错误'
    }, { status: 500 })
  }
}
