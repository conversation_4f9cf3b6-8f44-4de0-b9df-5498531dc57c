@echo off
echo.
echo ==========================================
echo Quick Docker Build Fix
echo ==========================================
echo.

echo [INFO] This script will fix the pnpm build issue by using npm instead

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)

echo [INFO] Step 1: Cleaning Docker cache...
docker system prune -f

echo [INFO] Step 2: Building with fixed Dockerfile (using npm)...
docker build -f Dockerfile.fixed -t us-fake-gen-ui:latest . --no-cache

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Docker build completed successfully!
    echo.
    echo [INFO] You can now run the application with:
    echo   docker run -p 3000:3000 us-fake-gen-ui:latest
    echo.
    echo [INFO] Or use docker-compose:
    echo   docker-compose up -d
    echo.
    echo [INFO] Access the application at: http://localhost:3000
    echo.
) else (
    echo.
    echo [ERROR] Build failed. Trying alternative approach...
    echo.
    echo [INFO] Step 3: Trying with local npm install first...
    
    REM Generate package-lock.json locally
    if not exist "package-lock.json" (
        echo [INFO] Generating package-lock.json...
        npm install --package-lock-only
    )
    
    echo [INFO] Building with package-lock.json...
    docker build -f Dockerfile.fixed -t us-fake-gen-ui:latest .
    
    if %errorlevel% equ 0 (
        echo [SUCCESS] Build successful with package-lock.json!
    ) else (
        echo [ERROR] Build still failed. Please check the error messages above.
        echo.
        echo [INFO] Alternative solutions:
        echo 1. Try running: npm install locally first
        echo 2. Check your internet connection
        echo 3. Increase Docker memory in Docker Desktop settings
        echo 4. Use cloud deployment instead (Vercel, Netlify)
    )
)

echo.
pause
