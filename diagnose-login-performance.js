#!/usr/bin/env node

/**
 * 登录性能诊断脚本
 * 用于快速检测登录相关的性能问题
 */

const https = require('https')
const http = require('http')

// 配置
const config = {
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  timeout: 5000,
  maxRetries: 3
}

/**
 * 发送HTTP请求并测量时间
 */
function timeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    const protocol = url.startsWith('https:') ? https : http
    
    const req = protocol.request(url, {
      timeout: config.timeout,
      ...options
    }, (res) => {
      let data = ''
      
      res.on('data', chunk => {
        data += chunk
      })
      
      res.on('end', () => {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        resolve({
          statusCode: res.statusCode,
          duration,
          headers: res.headers,
          data: data.length < 1000 ? data : data.substring(0, 1000) + '...',
          size: data.length
        })
      })
    })
    
    req.on('error', (error) => {
      const endTime = Date.now()
      const duration = endTime - startTime
      
      reject({
        error: error.message,
        duration,
        timeout: duration >= config.timeout
      })
    })
    
    req.on('timeout', () => {
      req.destroy()
      const endTime = Date.now()
      const duration = endTime - startTime
      
      reject({
        error: 'Request timeout',
        duration,
        timeout: true
      })
    })
    
    if (options.body) {
      req.write(options.body)
    }
    
    req.end()
  })
}

/**
 * 测试登录页面加载
 */
async function testLoginPageLoad() {
  console.log('🔍 测试登录页面加载...')
  
  try {
    const result = await timeRequest(`${config.baseUrl}/login`)
    
    console.log(`✅ 登录页面加载: ${result.duration}ms`)
    console.log(`   状态码: ${result.statusCode}`)
    console.log(`   响应大小: ${result.size} bytes`)
    
    if (result.duration > 2000) {
      console.warn(`⚠️  警告: 登录页面加载时间过长 (${result.duration}ms)`)
    }
    
    return result
  } catch (error) {
    console.error(`❌ 登录页面加载失败: ${error.error} (${error.duration}ms)`)
    if (error.timeout) {
      console.error('   原因: 请求超时')
    }
    return null
  }
}

/**
 * 测试登录API响应
 */
async function testLoginAPI() {
  console.log('🔍 测试登录API响应...')
  
  const testCredentials = {
    username: 'test',
    password: 'test123'
  }
  
  try {
    const result = await timeRequest(`${config.baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testCredentials)
    })
    
    console.log(`✅ 登录API响应: ${result.duration}ms`)
    console.log(`   状态码: ${result.statusCode}`)
    
    if (result.duration > 1000) {
      console.warn(`⚠️  警告: 登录API响应时间过长 (${result.duration}ms)`)
    }
    
    return result
  } catch (error) {
    console.error(`❌ 登录API测试失败: ${error.error} (${error.duration}ms)`)
    if (error.timeout) {
      console.error('   原因: 请求超时')
    }
    return null
  }
}

/**
 * 测试认证状态API
 */
async function testAuthStatusAPI() {
  console.log('🔍 测试认证状态API...')
  
  try {
    const result = await timeRequest(`${config.baseUrl}/api/auth/status`)
    
    console.log(`✅ 认证状态API: ${result.duration}ms`)
    console.log(`   状态码: ${result.statusCode}`)
    
    if (result.duration > 500) {
      console.warn(`⚠️  警告: 认证状态API响应时间过长 (${result.duration}ms)`)
    }
    
    return result
  } catch (error) {
    console.error(`❌ 认证状态API测试失败: ${error.error} (${error.duration}ms)`)
    return null
  }
}

/**
 * 测试数据库连接
 */
async function testDatabaseConnection() {
  console.log('🔍 测试数据库连接...')
  
  try {
    const result = await timeRequest(`${config.baseUrl}/api/debug/database`)
    
    console.log(`✅ 数据库连接测试: ${result.duration}ms`)
    console.log(`   状态码: ${result.statusCode}`)
    
    if (result.duration > 2000) {
      console.warn(`⚠️  警告: 数据库连接时间过长 (${result.duration}ms)`)
    }
    
    return result
  } catch (error) {
    console.error(`❌ 数据库连接测试失败: ${error.error} (${error.duration}ms)`)
    return null
  }
}

/**
 * 生成诊断报告
 */
function generateReport(results) {
  console.log('\n📊 性能诊断报告')
  console.log('=' .repeat(50))
  
  const tests = [
    { name: '登录页面加载', result: results.loginPage, threshold: 2000 },
    { name: '登录API响应', result: results.loginAPI, threshold: 1000 },
    { name: '认证状态API', result: results.authStatus, threshold: 500 },
    { name: '数据库连接', result: results.database, threshold: 2000 }
  ]
  
  let totalIssues = 0
  
  tests.forEach(test => {
    if (!test.result) {
      console.log(`❌ ${test.name}: 测试失败`)
      totalIssues++
    } else if (test.result.duration > test.threshold) {
      console.log(`⚠️  ${test.name}: ${test.result.duration}ms (超过阈值 ${test.threshold}ms)`)
      totalIssues++
    } else {
      console.log(`✅ ${test.name}: ${test.result.duration}ms`)
    }
  })
  
  console.log('\n📋 建议:')
  
  if (totalIssues === 0) {
    console.log('✅ 所有测试通过，性能良好！')
  } else {
    console.log(`⚠️  发现 ${totalIssues} 个性能问题`)
    
    if (results.loginAPI && results.loginAPI.duration > 1000) {
      console.log('- 登录API响应慢，检查邮件同步是否阻塞')
    }
    
    if (results.database && results.database.duration > 2000) {
      console.log('- 数据库连接慢，检查Supabase配置和网络')
    }
    
    if (results.loginPage && results.loginPage.duration > 2000) {
      console.log('- 登录页面加载慢，检查静态资源和中间件')
    }
    
    if (results.authStatus && results.authStatus.duration > 500) {
      console.log('- 认证状态API慢，检查JWT验证逻辑')
    }
  }
  
  console.log('\n🔧 优化建议:')
  console.log('1. 查看 LOGIN_PERFORMANCE_OPTIMIZATION.md 文档')
  console.log('2. 启用性能监控查看详细耗时')
  console.log('3. 检查网络连接和服务器状态')
  console.log('4. 优化数据库查询和连接池')
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始登录性能诊断...')
  console.log(`📍 目标地址: ${config.baseUrl}`)
  console.log(`⏱️  超时设置: ${config.timeout}ms`)
  console.log('')
  
  const results = {}
  
  // 并行执行测试
  const [loginPage, loginAPI, authStatus, database] = await Promise.allSettled([
    testLoginPageLoad(),
    testLoginAPI(),
    testAuthStatusAPI(),
    testDatabaseConnection()
  ])
  
  results.loginPage = loginPage.status === 'fulfilled' ? loginPage.value : null
  results.loginAPI = loginAPI.status === 'fulfilled' ? loginAPI.value : null
  results.authStatus = authStatus.status === 'fulfilled' ? authStatus.value : null
  results.database = database.status === 'fulfilled' ? database.value : null
  
  generateReport(results)
}

// 运行诊断
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 诊断过程发生错误:', error)
    process.exit(1)
  })
}

module.exports = { timeRequest, testLoginPageLoad, testLoginAPI, testAuthStatusAPI, testDatabaseConnection }
