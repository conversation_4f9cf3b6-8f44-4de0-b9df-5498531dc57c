import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

/**
 * 获取单个邮箱的详细信息和消息列表
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 获取邮箱信息
    const { data: email, error: emailError } = await supabase
      .from('emails')
      .select('*')
      .eq('id', id)
      .single()

    if (emailError || !email) {
      return NextResponse.json({
        success: false,
        error: '邮箱不存在'
      }, { status: 404 })
    }

    // 获取该邮箱的消息列表
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('*')
      .eq('email_id', id)
      .order('received_at', { ascending: false })

    if (messagesError) {
      console.error('获取消息列表失败:', messagesError)
      return NextResponse.json({
        success: false,
        error: '获取消息列表失败: ' + messagesError.message
      }, { status: 500 })
    }

    // 转换消息格式
    const formattedMessages = (messages || []).map(message => ({
      id: message.id,
      emailId: message.email_id,
      fromAddress: message.from_address,
      toAddress: message.to_address,
      subject: message.subject || '无主题',
      textContent: message.text_content,
      htmlContent: message.html_content,
      receivedAt: message.received_at,
      isRead: message.is_read,
      hasVerificationCode: message.has_verification_code,
      verificationCode: message.verification_code
    }))

    // 转换邮箱格式
    const formattedEmail = {
      id: email.id,
      address: email.address,
      password: email.password,
      type: email.type,
      config: email.config,
      createdAt: email.created_at_timestamp || Date.now(),
      isActive: email.is_active,
      unreadCount: email.unread_count || 0,
      hasNewMessages: email.has_new_messages || false
    }

    return NextResponse.json({
      success: true,
      data: {
        email: formattedEmail,
        messages: formattedMessages
      }
    })

  } catch (error) {
    console.error('获取邮箱详情失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取邮箱详情失败'
    }, { status: 500 })
  }
}

/**
 * 删除邮箱
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 检查邮箱是否存在
    const { data: email, error: checkError } = await supabase
      .from('emails')
      .select('address')
      .eq('id', id)
      .single()

    if (checkError || !email) {
      return NextResponse.json({
        success: false,
        error: '邮箱不存在'
      }, { status: 404 })
    }

    // 删除邮箱（级联删除相关消息）
    const { error: deleteError } = await supabase
      .from('emails')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('删除邮箱失败:', deleteError)
      return NextResponse.json({
        success: false,
        error: '删除邮箱失败: ' + deleteError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `邮箱 ${email.address} 已删除`
    })

  } catch (error) {
    console.error('删除邮箱失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除邮箱失败'
    }, { status: 500 })
  }
}

/**
 * 更新邮箱信息
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { unreadCount, hasNewMessages, isActive } = body

    const updateData: any = {}
    
    if (typeof unreadCount === 'number') {
      updateData.unread_count = unreadCount
    }
    
    if (typeof hasNewMessages === 'boolean') {
      updateData.has_new_messages = hasNewMessages
    }
    
    if (typeof isActive === 'boolean') {
      updateData.is_active = isActive
    }

    updateData.updated_at = new Date().toISOString()

    const { data, error } = await supabase
      .from('emails')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('更新邮箱失败:', error)
      return NextResponse.json({
        success: false,
        error: '更新邮箱失败: ' + error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        id: data.id,
        address: data.address,
        password: data.password,
        type: data.type,
        config: data.config,
        createdAt: data.created_at_timestamp || Date.now(),
        isActive: data.is_active,
        unreadCount: data.unread_count || 0,
        hasNewMessages: data.has_new_messages || false
      },
      message: '邮箱更新成功'
    })

  } catch (error) {
    console.error('更新邮箱失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '更新邮箱失败'
    }, { status: 500 })
  }
}
