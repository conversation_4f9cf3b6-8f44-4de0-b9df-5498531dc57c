@echo off
echo.
echo ==========================================
echo Local Production Deployment (No Docker)
echo ==========================================
echo.

echo [INFO] This script will build and run the application locally without Docker

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed
    echo [INFO] Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo [OK] Node.js is installed
node --version

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not available
    pause
    exit /b 1
)

echo [OK] npm is available
npm --version

echo.
echo [INFO] Step 1: Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    echo [INFO] Trying to fix with clean install...
    
    if exist "node_modules" (
        echo [INFO] Removing old node_modules...
        rmdir /s /q node_modules
    )
    
    if exist "package-lock.json" (
        echo [INFO] Removing package-lock.json...
        del package-lock.json
    )
    
    echo [INFO] Clean install...
    npm install
    
    if %errorlevel% neq 0 (
        echo [ERROR] Still failed to install dependencies
        pause
        exit /b 1
    )
)

echo [SUCCESS] Dependencies installed successfully

echo.
echo [INFO] Step 2: Building the application...
npm run build

if %errorlevel% neq 0 (
    echo [ERROR] Build failed
    pause
    exit /b 1
)

echo [SUCCESS] Build completed successfully

echo.
echo [INFO] Step 3: Starting the production server...
echo [INFO] The application will be available at: http://localhost:3002
echo [INFO] Press Ctrl+C to stop the server
echo.

npm run start
