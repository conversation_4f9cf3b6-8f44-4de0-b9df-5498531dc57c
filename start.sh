#!/bin/bash

# 美国地址生成器 - 快速启动脚本
# 简化版启动脚本，适合快速测试

echo "🚀 美国地址生成器 - 快速启动"
echo "================================"

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装"
    echo "💡 请先安装Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js: $(node --version)"

# 检查项目文件
if [ ! -f "package.json" ]; then
    echo "❌ package.json不存在，请在项目根目录运行"
    exit 1
fi

echo "✅ 项目文件检查通过"

# 检查环境变量
if [ ! -f ".env.local" ]; then
    if [ -f ".env.local.example" ]; then
        echo "📝 创建环境变量文件..."
        cp .env.local.example .env.local
        echo "⚠️  请编辑.env.local文件配置数据库等信息"
        echo "📋 必需配置项:"
        echo "   - NEXT_PUBLIC_SUPABASE_URL"
        echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY"
        echo "   - ADMIN_USERNAME"
        echo "   - ADMIN_PASSWORD"
        echo "   - JWT_SECRET"
        echo
        read -p "配置完成后按回车继续..."
    else
        echo "❌ 环境变量文件不存在"
        exit 1
    fi
fi

echo "✅ 环境变量文件存在"

# 安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    if [ -f "pnpm-lock.yaml" ]; then
        if command -v pnpm &> /dev/null; then
            pnpm install
        else
            echo "📦 安装pnpm..."
            npm install -g pnpm
            pnpm install
        fi
    else
        npm install
    fi
fi

echo "✅ 依赖已安装"

# 构建应用（如果需要）
if [ ! -d ".next" ]; then
    echo "🔨 构建应用..."
    if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
        pnpm run build
    else
        npm run build
    fi
fi

echo "✅ 应用已构建"

# 检查端口
if netstat -tuln 2>/dev/null | grep -q ":3002 "; then
    echo "⚠️  端口3002已被占用"
    read -p "是否继续启动? (y/n): " continue_start
    if [[ ! $continue_start =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 启动应用
echo "🚀 启动应用..."
echo
echo "📱 访问地址: http://localhost:3002"
echo "🔧 停止应用: Ctrl+C"
echo

# 根据参数选择启动模式
if [ "$1" = "dev" ]; then
    echo "🔧 开发模式启动..."
    if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
        pnpm dev
    else
        npm run dev
    fi
else
    echo "🏭 生产模式启动..."
    if [ -f "pnpm-lock.yaml" ] && command -v pnpm &> /dev/null; then
        pnpm start
    else
        npm start
    fi
fi
