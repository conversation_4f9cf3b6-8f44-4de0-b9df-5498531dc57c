import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // 直接尝试创建表的简化版本
    const createEmailTableSQL = `
      CREATE TABLE IF NOT EXISTS emails (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        
        -- 邮箱基本信息
        address TEXT NOT NULL UNIQUE,
        password TEXT,
        type TEXT DEFAULT 'tempmail',
        config JSONB,
        
        -- 状态信息
        is_active BOOLEAN DEFAULT true,
        created_at_timestamp BIGINT DEFAULT EXTRACT(epoch FROM NOW()) * 1000,
        
        -- 统计信息
        unread_count INTEGER DEFAULT 0,
        has_new_messages BOOLEAN DEFAULT false
      );
    `

    const createMessageTableSQL = `
      CREATE TABLE IF NOT EXISTS messages (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        
        -- 关联邮箱
        email_id UUID REFERENCES emails(id) ON DELETE CASCADE,
        
        -- 邮件信息
        from_address TEXT NOT NULL,
        to_address TEXT NOT NULL,
        subject TEXT,
        text_content TEXT,
        html_content TEXT,
        
        -- 状态信息
        received_at BIGINT NOT NULL,
        is_read BOOLEAN DEFAULT false,
        has_verification_code BOOLEAN DEFAULT false,
        verification_code TEXT
      );
    `

    const createIndexesSQL = `
      CREATE INDEX IF NOT EXISTS idx_emails_address ON emails(address);
      CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at);
      CREATE INDEX IF NOT EXISTS idx_messages_email_id ON messages(email_id);
      CREATE INDEX IF NOT EXISTS idx_messages_received_at ON messages(received_at);
    `

    // 注意：Supabase客户端库通常不支持直接执行DDL语句
    // 这里我们返回SQL语句，让用户手动在Supabase控制台执行
    return NextResponse.json({
      success: true,
      message: '请在Supabase控制台的SQL编辑器中执行以下SQL语句',
      sql: {
        emailTable: createEmailTableSQL,
        messageTable: createMessageTableSQL,
        indexes: createIndexesSQL
      },
      instructions: [
        '1. 登录到 Supabase 控制台',
        '2. 进入 SQL 编辑器',
        '3. 依次复制并执行上面的SQL语句',
        '4. 先执行 emailTable，再执行 messageTable，最后执行 indexes',
        '5. 或者直接执行 database/email-tables.sql 文件中的内容'
      ],
      quickSQL: `
-- 快速执行版本（复制整个内容到Supabase SQL编辑器）
CREATE TABLE IF NOT EXISTS emails (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  address TEXT NOT NULL UNIQUE,
  password TEXT,
  type TEXT DEFAULT 'tempmail',
  config JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at_timestamp BIGINT DEFAULT EXTRACT(epoch FROM NOW()) * 1000,
  unread_count INTEGER DEFAULT 0,
  has_new_messages BOOLEAN DEFAULT false
);

CREATE TABLE IF NOT EXISTS messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  email_id UUID REFERENCES emails(id) ON DELETE CASCADE,
  from_address TEXT NOT NULL,
  to_address TEXT NOT NULL,
  subject TEXT,
  text_content TEXT,
  html_content TEXT,
  received_at BIGINT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  has_verification_code BOOLEAN DEFAULT false,
  verification_code TEXT
);

CREATE INDEX IF NOT EXISTS idx_emails_address ON emails(address);
CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_email_id ON messages(email_id);
CREATE INDEX IF NOT EXISTS idx_messages_received_at ON messages(received_at);
      `
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '生成SQL失败'
    }, { status: 500 })
  }
}
