/**
 * 简单的IMAP实现
 * 使用Node.js内置模块实现基本的IMAP功能
 */

import { connect as tlsConnect } from 'tls'

interface IMAPConfig {
  host: string
  port: number
  user: string
  password: string
}

interface EmailResult {
  code: string | null
  emails: any[]
  message: string
}

export class SimpleIMAP {
  private config: IMAPConfig
  private socket: any = null
  private tagCounter = 0
  private responseBuffer = ''

  constructor() {
    this.config = {
      host: process.env.IMAP_SERVER || 'imap.qq.com',
      port: parseInt(process.env.IMAP_PORT || '993'),
      user: process.env.IMAP_USER || '',
      password: process.env.IMAP_PASS || ''
    }
  }

  /**
   * 生成IMAP命令标签
   */
  private getTag(): string {
    return `A${String(++this.tagCounter).padStart(3, '0')}`
  }

  /**
   * 发送IMAP命令并等待响应
   */
  private async sendCommand(command: string): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'))
        return
      }

      const tag = this.getTag()
      const fullCommand = `${tag} ${command}\r\n`

      console.log(`发送IMAP命令: ${fullCommand.trim()}`)

      let response = ''
      let responseComplete = false
      let timeoutCleared = false
      let listenerRemoved = false

      const onData = (data: Buffer) => {
        response += data.toString()

        // 检查是否收到完整响应
        const lines = response.split('\r\n')
        for (const line of lines) {
          if (line.startsWith(`${tag} OK`) || line.startsWith(`${tag} NO`) || line.startsWith(`${tag} BAD`)) {
            responseComplete = true
            break
          }
        }

        if (responseComplete && !listenerRemoved) {
          if (this.socket) {
            this.socket.removeListener('data', onData)
          }
          listenerRemoved = true
          if (!timeoutCleared) {
            clearTimeout(timeoutId)
            timeoutCleared = true
          }
          console.log(`收到IMAP响应: ${response.trim()}`)
          resolve(response)
        }
      }

      this.socket.on('data', onData)
      this.socket.write(fullCommand)

      // 设置超时
      const timeoutId = setTimeout(() => {
        if (!responseComplete && !timeoutCleared && !listenerRemoved) {
          if (this.socket) {
            this.socket.removeListener('data', onData)
          }
          listenerRemoved = true
          timeoutCleared = true
          reject(new Error('IMAP command timeout'))
        }
      }, 30000)
    })
  }

  /**
   * 连接到IMAP服务器
   */
  async connect(): Promise<boolean> {
    try {
      console.log(`连接到IMAP服务器: ${this.config.host}:${this.config.port}`)
      
      return new Promise((resolve, reject) => {
        this.socket = tlsConnect({
          host: this.config.host,
          port: this.config.port,
          rejectUnauthorized: false
        }, () => {
          console.log('IMAP TLS连接建立成功')
          resolve(true)
        })

        this.socket.on('error', (error: Error) => {
          console.error('IMAP连接错误:', error)
          reject(error)
        })

        this.socket.on('close', () => {
          console.log('IMAP连接关闭')
        })

        // 等待服务器欢迎消息
        this.socket.once('data', (data: Buffer) => {
          console.log('IMAP服务器欢迎消息:', data.toString().trim())
        })
      })
    } catch (error) {
      console.error('IMAP连接失败:', error)
      return false
    }
  }

  /**
   * 登录IMAP服务器
   */
  async login(): Promise<boolean> {
    try {
      const response = await this.sendCommand(`LOGIN ${this.config.user} ${this.config.password}`)
      return response.includes('OK')
    } catch (error) {
      console.error('IMAP登录失败:', error)
      return false
    }
  }

  /**
   * 选择邮箱文件夹
   */
  async selectFolder(folder: string = 'INBOX'): Promise<boolean> {
    try {
      const response = await this.sendCommand(`SELECT ${folder}`)
      return response.includes('OK')
    } catch (error) {
      console.error('选择文件夹失败:', error)
      return false
    }
  }

  /**
   * 搜索包含特定收件人的邮件
   */
  async searchByRecipient(recipient: string): Promise<number[]> {
    try {
      const response = await this.sendCommand(`SEARCH TO "${recipient}"`)
      
      if (response.includes('OK')) {
        // 解析搜索结果
        const lines = response.split('\r\n')
        for (const line of lines) {
          if (line.startsWith('* SEARCH')) {
            const uids = line.replace('* SEARCH', '').trim().split(' ')
            return uids.filter(uid => uid && !isNaN(parseInt(uid))).map(uid => parseInt(uid))
          }
        }
      }
      
      return []
    } catch (error) {
      console.error('搜索邮件失败:', error)
      return []
    }
  }

  /**
   * 获取邮件内容
   */
  async fetchEmail(uid: number): Promise<any> {
    try {
      const response = await this.sendCommand(`FETCH ${uid} (ENVELOPE BODY[TEXT])`)
      
      if (response.includes('OK')) {
        // 简化的邮件解析
        const bodyText = this.extractBodyFromResponse(response)
        const envelope = this.extractEnvelopeFromResponse(response)
        
        return {
          id: `imap_${uid}`,
          uid,
          from: envelope.from || 'unknown',
          to: envelope.to || this.config.user,
          subject: envelope.subject || 'No Subject',
          text: bodyText,
          date: new Date().toISOString()
        }
      }
      
      return null
    } catch (error) {
      console.error('获取邮件失败:', error)
      return null
    }
  }

  /**
   * 从响应中提取邮件正文
   */
  private extractBodyFromResponse(response: string): string {
    // 查找BODY[TEXT]部分
    const bodyMatch = response.match(/BODY\[TEXT\]\s*{(\d+)}\r?\n([\s\S]*?)(?=\r?\n\)|\r?\nA\d+|\r?\n$)/i)
    if (bodyMatch) {
      const bodyContent = bodyMatch[2].trim()
      console.log('原始邮件正文:', bodyContent)

      // 解析multipart邮件
      const cleanContent = this.parseMultipartContent(bodyContent)
      console.log('解析后的邮件内容:', cleanContent)

      return cleanContent
    }
    return ''
  }

  /**
   * 解析multipart邮件内容
   */
  private parseMultipartContent(content: string): string {
    console.log('开始解析multipart内容:', content.substring(0, 200) + '...')

    // 如果不是multipart邮件，直接返回
    if (!content.includes('Content-Type:')) {
      console.log('不是multipart邮件，直接返回')
      return content.trim()
    }

    // 更简单的text/plain匹配
    const textPlainMatch = content.match(/Content-Type:\s*text\/plain[^]*?\r?\n\r?\n([^]*?)(?=\r?\n\r?\n--|\r?\n--)/i)
    if (textPlainMatch) {
      let textContent = textPlainMatch[1].trim()
      console.log('找到text/plain内容:', textContent)

      if (textContent) {
        return textContent
      }
    }

    // 尝试HTML内容
    const htmlMatch = content.match(/Content-Type:\s*text\/html[^]*?\r?\n\r?\n([^]*?)(?=\r?\n\r?\n--|\r?\n--)/i)
    if (htmlMatch) {
      let htmlContent = htmlMatch[1].trim()
      console.log('找到HTML内容:', htmlContent)

      // 简单的HTML标签移除
      const textFromHtml = htmlContent.replace(/<[^>]*>/g, '').trim()
      console.log('HTML转文本:', textFromHtml)

      if (textFromHtml) {
        return textFromHtml
      }
    }

    // 最后的回退方案：查找所有非边界标记的文本
    const lines = content.split(/\r?\n/)
    let textLines: string[] = []
    let inTextSection = false

    for (const line of lines) {
      if (line.startsWith('--')) {
        inTextSection = false
        continue
      }

      if (line.includes('Content-Type: text/plain')) {
        inTextSection = true
        continue
      }

      if (inTextSection && line.trim() && !line.includes('Content-Type') && !line.includes('charset')) {
        textLines.push(line.trim())
      }
    }

    const result = textLines.join(' ').trim()
    console.log('回退方案结果:', result)
    return result
  }

  /**
   * 从响应中提取信封信息
   */
  private extractEnvelopeFromResponse(response: string): any {
    try {
      // 查找ENVELOPE部分
      const envelopeMatch = response.match(/ENVELOPE \((.*?)\) BODY/s)
      if (!envelopeMatch) {
        console.log('未找到ENVELOPE信息')
        return {
          from: '未知发件人',
          to: '未知收件人',
          subject: '验证码邮件'
        }
      }

      const envelopeData = envelopeMatch[1]
      console.log('ENVELOPE数据:', envelopeData)

      // 解析信封数据
      const subject = this.extractFromEnvelope(envelopeData, 'SUBJECT')
      const from = this.extractFromEnvelope(envelopeData, 'FROM')
      const to = this.extractFromEnvelope(envelopeData, 'TO')

      return {
        from: from || '未知发件人',
        to: to || '未知收件人',
        subject: subject || '验证码邮件'
      }
    } catch (error) {
      console.error('解析信封信息失败:', error)
      return {
        from: '未知发件人',
        to: '未知收件人',
        subject: '验证码邮件'
      }
    }
  }

  /**
   * 删除邮件
   */
  async deleteEmail(uid: number): Promise<boolean> {
    try {
      await this.sendCommand(`STORE ${uid} +FLAGS (\\Deleted)`)
      const response = await this.sendCommand('EXPUNGE')
      return response.includes('OK')
    } catch (error) {
      console.error('删除邮件失败:', error)
      return false
    }
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    try {
      if (this.socket) {
        await this.sendCommand('LOGOUT')
        this.socket.end()
      }
    } catch (error) {
      console.error('关闭IMAP连接失败:', error)
    } finally {
      this.socket = null
    }
  }

  /**
   * 获取验证码的主要方法
   */
  async getVerificationCode(tempEmail: string): Promise<EmailResult> {
    const emails: any[] = []
    
    try {
      // 连接和登录
      const connected = await this.connect()
      if (!connected) {
        throw new Error('无法连接到IMAP服务器')
      }

      const loggedIn = await this.login()
      if (!loggedIn) {
        throw new Error('IMAP登录失败')
      }

      const selected = await this.selectFolder('INBOX')
      if (!selected) {
        throw new Error('无法选择INBOX文件夹')
      }

      // 搜索包含临时邮箱地址的邮件
      const uids = await this.searchByRecipient(tempEmail)
      console.log(`找到 ${uids.length} 封相关邮件`)

      // 获取邮件内容
      for (const uid of uids.slice(-5)) { // 只获取最新的5封邮件
        const email = await this.fetchEmail(uid)
        if (email) {
          emails.push(email)
          
          // 检查是否包含验证码
          const code = this.extractVerificationCode(email.text, tempEmail)
          if (code) {
            // 删除已处理的邮件
            await this.deleteEmail(uid)
            await this.close()
            
            return {
              code,
              emails,
              message: `验证码获取成功: ${code}`
            }
          }
        }
      }

      await this.close()
      
      return {
        code: null,
        emails,
        message: '未找到验证码'
      }

    } catch (error) {
      await this.close()
      console.error('获取验证码失败:', error)
      
      return {
        code: null,
        emails,
        message: `获取验证码失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 从信封数据中提取字段
   */
  private extractFromEnvelope(envelopeData: string, field: string): string {
    try {
      // ENVELOPE格式：(date subject from sender reply-to to cc bcc in-reply-to message-id)
      // 每个地址字段格式：(("name" NIL "user" "domain"))

      // 分割信封字段
      const parts = this.parseEnvelopeParts(envelopeData)

      switch (field) {
        case 'SUBJECT':
          // 主题是第二个字段
          if (parts.length > 1) {
            const subject = parts[1]
            return this.decodeBase64Subject(subject.replace(/"/g, ''))
          }
          return ''

        case 'FROM':
          // 发件人是第三个字段
          if (parts.length > 2) {
            return this.parseEmailAddress(parts[2])
          }
          return ''

        case 'TO':
          // 收件人是第六个字段
          if (parts.length > 5) {
            return this.parseEmailAddress(parts[5])
          }
          return ''

        default:
          return ''
      }
    } catch (error) {
      console.error(`解析${field}失败:`, error)
      return ''
    }
  }

  /**
   * 解析信封部分
   */
  private parseEnvelopeParts(envelopeData: string): string[] {
    const parts: string[] = []
    let current = ''
    let depth = 0
    let inQuotes = false

    for (let i = 0; i < envelopeData.length; i++) {
      const char = envelopeData[i]

      if (char === '"' && envelopeData[i-1] !== '\\') {
        inQuotes = !inQuotes
        current += char
      } else if (!inQuotes) {
        if (char === '(') {
          depth++
          current += char
        } else if (char === ')') {
          depth--
          current += char
        } else if (char === ' ' && depth === 0) {
          if (current.trim()) {
            parts.push(current.trim())
            current = ''
          }
        } else {
          current += char
        }
      } else {
        current += char
      }
    }

    if (current.trim()) {
      parts.push(current.trim())
    }

    return parts
  }

  /**
   * 解析邮箱地址
   */
  private parseEmailAddress(addressPart: string): string {
    try {
      // 格式：(("name" NIL "user" "domain"))
      const match = addressPart.match(/\(\(".*?" NIL "([^"]+)" "([^"]+)"\)\)/)
      if (match) {
        return `${match[1]}@${match[2]}`
      }

      // 简化格式：((NIL NIL "user" "domain"))
      const simpleMatch = addressPart.match(/\(\(NIL NIL "([^"]+)" "([^"]+)"\)\)/)
      if (simpleMatch) {
        return `${simpleMatch[1]}@${simpleMatch[2]}`
      }

      // 更简单的格式：("user" "domain")
      const basicMatch = addressPart.match(/"([^"]+)" "([^"]+)"/)
      if (basicMatch) {
        return `${basicMatch[1]}@${basicMatch[2]}`
      }

      return ''
    } catch (error) {
      console.error('解析邮箱地址失败:', error)
      return ''
    }
  }

  /**
   * 解码Base64编码的主题
   */
  private decodeBase64Subject(subject: string): string {
    try {
      // 处理 =?utf-8?B?...?= 格式
      if (subject.includes('=?utf-8?B?')) {
        const base64Match = subject.match(/=\?utf-8\?B\?([^?]+)\?=/i)
        if (base64Match) {
          const decoded = Buffer.from(base64Match[1], 'base64').toString('utf-8')
          return decoded
        }
      }

      // 处理 =?utf-8?Q?...?= 格式 (Quoted-Printable)
      if (subject.includes('=?utf-8?Q?')) {
        const qpMatch = subject.match(/=\?utf-8\?Q\?([^?]+)\?=/i)
        if (qpMatch) {
          // 简单的Quoted-Printable解码
          const decoded = qpMatch[1].replace(/=([0-9A-F]{2})/g, (match, hex) => {
            return String.fromCharCode(parseInt(hex, 16))
          })
          return decoded
        }
      }

      return subject
    } catch (error) {
      console.error('解码主题失败:', error)
      return subject
    }
  }

  /**
   * 提取验证码
   */
  private extractVerificationCode(text: string, tempEmail: string): string | null {
    console.log('开始提取验证码，邮件内容:', text)

    // 移除临时邮箱地址避免误识别
    let cleanText = text.replace(new RegExp(tempEmail, 'g'), '')
    console.log('清理后的文本:', cleanText)

    // 多种验证码模式匹配
    const patterns = [
      /(?:验证码|verification code|code)[：:\s]*(\d{6})/i,  // 验证码: 123456
      /(?:您的|your)[^0-9]*(\d{6})/i,                      // 您的验证码是123456
      /(?<![a-zA-Z@.])\b(\d{6})\b/,                        // 独立的6位数字
      /(?:pin|code)[：:\s]*(\d{4,8})/i,                    // PIN: 1234
    ]

    for (const pattern of patterns) {
      const match = cleanText.match(pattern)
      if (match) {
        const code = match[1] || match[0]
        console.log(`找到验证码: ${code} (使用模式: ${pattern})`)

        // 验证码长度检查
        if (code.length >= 4 && code.length <= 8) {
          return code
        }
      }
    }

    console.log('未找到验证码')
    return null
  }
}
