-- 创建保存个人数据的表
CREATE TABLE saved_person_data (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 基本信息
  full_name TEXT NOT NULL,
  first_name TEXT,
  last_name TEXT,
  gender TEXT,
  birthday TEXT,
  title TEXT,
  hair_color TEXT,
  country TEXT,
  
  -- 地址信息
  street TEXT,
  city TEXT,
  state TEXT,
  state_full_name TEXT,
  zip_code TEXT,
  phone TEXT,
  email TEXT,
  full_address TEXT,
  
  -- 工作信息
  occupation TEXT,
  company TEXT,
  company_size TEXT,
  industry TEXT,
  status TEXT,
  salary TEXT,
  
  -- 金融信息
  ssn TEXT,
  card_type TEXT,
  card_number TEXT,
  cvv INTEGER,
  expiry TEXT,
  
  -- 账户信息
  username TEXT,
  password TEXT,
  security_question TEXT,
  security_answer TEXT,
  
  -- 身体信息
  height TEXT,
  weight TEXT,
  blood_type TEXT,
  
  -- 技术信息
  os TEXT,
  guid TEXT,
  user_agent TEXT,
  
  -- 其他信息
  education TEXT,
  website TEXT,
  
  -- 学校信息
  school_name TEXT,
  school_id TEXT,
  school_zip TEXT,
  school_website TEXT,
  school_address TEXT,
  school_city TEXT,
  school_state TEXT,
  school_phone TEXT,
  school_grades TEXT,
  
  -- 大学信息
  university_name TEXT,
  university_id TEXT,
  university_zip TEXT,
  university_website TEXT,
  university_address TEXT,
  university_city TEXT,
  university_state TEXT,
  university_phone TEXT,
  university_type TEXT,
  
  -- 生成参数
  generation_seed INTEGER,
  generation_params JSONB
);

-- 创建邮件管理系统的邮箱表
CREATE TABLE emails (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- 邮箱基本信息
  address TEXT NOT NULL UNIQUE,
  password TEXT,
  type TEXT DEFAULT 'tempmail',
  config JSONB,

  -- 状态信息
  is_active BOOLEAN DEFAULT true,
  created_at_timestamp BIGINT DEFAULT EXTRACT(epoch FROM NOW()) * 1000,

  -- 统计信息
  unread_count INTEGER DEFAULT 0,
  has_new_messages BOOLEAN DEFAULT false
);

-- 创建邮件消息表
CREATE TABLE messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- 关联邮箱
  email_id UUID REFERENCES emails(id) ON DELETE CASCADE,

  -- 邮件信息
  from_address TEXT NOT NULL,
  to_address TEXT NOT NULL,
  subject TEXT,
  text_content TEXT,
  html_content TEXT,

  -- 状态信息
  received_at BIGINT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  has_verification_code BOOLEAN DEFAULT false,
  verification_code TEXT
);

-- 创建索引
CREATE INDEX idx_emails_address ON emails(address);
CREATE INDEX idx_emails_created_at ON emails(created_at);
CREATE INDEX idx_messages_email_id ON messages(email_id);
CREATE INDEX idx_messages_received_at ON messages(received_at);

-- 创建索引以提高查询性能
CREATE INDEX idx_saved_person_data_created_at ON saved_person_data(created_at);
CREATE INDEX idx_saved_person_data_full_name ON saved_person_data(full_name);
CREATE INDEX idx_saved_person_data_email ON saved_person_data(email);
CREATE INDEX idx_saved_person_data_city ON saved_person_data(city);
CREATE INDEX idx_saved_person_data_state ON saved_person_data(state);

-- 创建全文搜索索引
CREATE INDEX idx_saved_person_data_search ON saved_person_data 
USING gin(to_tsvector('english', full_name || ' ' || email || ' ' || city || ' ' || state));

-- 启用行级安全策略（RLS）
ALTER TABLE saved_person_data ENABLE ROW LEVEL SECURITY;

-- 创建策略：允许所有操作（在生产环境中应该根据需要限制）
CREATE POLICY "Allow all operations" ON saved_person_data
FOR ALL USING (true);

-- 注释
COMMENT ON TABLE saved_person_data IS '保存生成的个人数据';
COMMENT ON COLUMN saved_person_data.generation_seed IS '生成数据时使用的随机种子';
COMMENT ON COLUMN saved_person_data.generation_params IS '生成数据时的参数配置（JSON格式）';
